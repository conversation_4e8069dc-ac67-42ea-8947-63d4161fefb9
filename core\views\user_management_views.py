from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import super_admin_required
from ..mongo_models import User
from ..utils import generate_temp_password, send_temp_password_email
from mongoengine.queryset.visitor import Q
import logging

logger = logging.getLogger(__name__)

class UserSearchView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        """Rechercher des utilisateurs (super admin uniquement)"""
        try:
            search_query = request.GET.get('query', '')
            role_filter = request.GET.get('role', None)

            # Construction de la requête
            query = Q(name__icontains=search_query) | Q(email__icontains=search_query)
            if role_filter:
                query = query & Q(role=role_filter)

            users = User.objects(query)
            
            return Response([
                {
                    'id': str(user.id),
                    'name': user.name,
                    'email': user.email,
                    'role': user.role,
                    'created_at': user.created_at,
                    'last_login': user.last_login
                } for user in users
            ])

        except Exception as e:
            logger.error(f"Erreur lors de la recherche d'utilisateurs: {str(e)}")
            return Response({"error": str(e)}, status=500)

class UserCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def post(self, request):
        """Créer un nouvel utilisateur (super admin uniquement)"""
        try:
            data = request.data
            name = data.get('name')
            email = data.get('email')
            role = data.get('role', 'employee')

            # Validations
            if not name or len(name.strip()) < 2:
                return Response({"error": "Le nom est requis (minimum 2 caractères)"}, status=400)

            if not email:
                return Response({"error": "L'email est requis"}, status=400)

            if role not in ['admin', 'employee', 'client']:
                return Response({"error": "Rôle invalide. Choisir 'admin', 'employee' ou 'client'"}, status=400)

            # Vérifier si l'email existe déjà
            if User.objects(email=email).first():
                return Response({"error": "Un utilisateur avec cet email existe déjà"}, status=400)

            # Créer l'utilisateur
            temp_password = generate_temp_password()
            user = User(
                name=name,
                email=email,
                role=role,
                temp_password_required=True
            )
            
            # Définir les permissions par défaut selon le rôle
            if role == 'admin':
                user.permissions.update({
                    'manage_teams': True,
                    'manage_team_tasks': True,
                    'manage_team_calendars': True,
                    'view_team_dashboards': True
                })

            user.set_password(temp_password)
            user.save()

            # Envoyer l'email avec les identifiants
            try:
                send_temp_password_email(email, temp_password, name)
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
                user.delete()
                return Response({
                    "error": "Impossible d'envoyer l'email avec les identifiants",
                    "detail": str(e)
                }, status=500)

            return Response({
                "message": "Utilisateur créé avec succès",
                "user": {
                    "id": str(user.id),
                    "name": user.name,
                    "email": user.email,
                    "role": user.role
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Erreur lors de la création d'un utilisateur: {str(e)}")
            return Response({"error": str(e)}, status=500)