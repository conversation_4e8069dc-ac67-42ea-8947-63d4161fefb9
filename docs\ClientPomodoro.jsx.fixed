import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion } from 'framer-motion';

const ClientPomodoro = () => {
  const [settings, setSettings] = useState({
    focus_duration: 25,
    short_break_duration: 5,
    long_break_duration: 15,
    sessions_before_long_break: 4
  });
  
  const [currentSession, setCurrentSession] = useState({
    active: false,
    status: 'inactive',
    remaining_time: 0,
    focus_duration: 25,
    break_duration: 5
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  
  // Utiliser useRef pour éviter les re-renders inutiles
  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  // Fonction pour formater le temps
  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Fonction pour faire un appel API
  const apiCall = useCallback(async (endpoint, method = 'GET', data = null) => {
    try {
      const token = localStorage.getItem('access_token');
      const config = {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };
      
      if (data) {
        config.body = JSON.stringify(data);
      }
      
      const response = await fetch(`http://localhost:8000/api${endpoint}`, config);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    }
  }, []);

  // Charger les paramètres Pomodoro
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await apiCall('/pomodoro/settings/');
      setSettings(data);
      setCurrentSession(data.current_session);
    } catch (error) {
      setError(`Erreur lors du chargement des paramètres: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Contrôler la session Pomodoro
  const controlSession = useCallback(async (action) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await apiCall(`/pomodoro/control/${action}/`, 'POST');
      setCurrentSession(data.current_session);
      
      // Afficher un message de succès
      if (data.message) {
        console.log(data.message);
      }
    } catch (error) {
      setError(`Erreur lors du contrôle de la session: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Mettre à jour les paramètres
  const updateSettings = useCallback(async (newSettings) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await apiCall('/pomodoro/settings/', 'PUT', newSettings);
      setSettings(data.settings);
      setIsSettingsOpen(false);
    } catch (error) {
      setError(`Erreur lors de la mise à jour des paramètres: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Effet pour le timer
  useEffect(() => {
    if (currentSession.active && currentSession.status === 'active') {
      intervalRef.current = setInterval(() => {
        setCurrentSession(prev => {
          if (prev.remaining_time <= 1) {
            // Session terminée
            controlSession('complete');
            return prev;
          }
          return {
            ...prev,
            remaining_time: prev.remaining_time - 1
          };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [currentSession.active, currentSession.status, controlSession]);

  // Charger les paramètres au montage du composant
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // Fonction pour jouer un son (optionnel)
  const playNotificationSound = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.play().catch(error => {
        console.log('Impossible de jouer le son:', error);
      });
    }
  }, []);

  // Gestionnaires d'événements
  const handleStart = () => controlSession('start');
  const handlePause = () => controlSession('pause');
  const handleResume = () => controlSession('resume');
  const handleReset = () => controlSession('reset');

  const handleSettingsSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(e.target);
    const newSettings = {
      focus_duration: parseInt(formData.get('focus_duration')),
      short_break_duration: parseInt(formData.get('short_break_duration')),
      long_break_duration: parseInt(formData.get('long_break_duration')),
      sessions_before_long_break: parseInt(formData.get('sessions_before_long_break'))
    };
    updateSettings(newSettings);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full"
      >
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">Mode Pomodoro</h1>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          {/* Timer Display */}
          <div className="mb-8">
            <div className="text-6xl font-mono font-bold text-gray-800 mb-4">
              {formatTime(currentSession.remaining_time)}
            </div>
            <div className="text-lg text-gray-600">
              {currentSession.status === 'active' && 'Session de focus en cours'}
              {currentSession.status === 'paused' && 'Session en pause'}
              {currentSession.status === 'inactive' && 'Prêt à commencer'}
              {currentSession.status === 'completed' && 'Session terminée !'}
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex justify-center space-x-4 mb-8">
            {!currentSession.active ? (
              <button
                onClick={handleStart}
                disabled={isLoading}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50"
              >
                Démarrer
              </button>
            ) : (
              <>
                {currentSession.status === 'active' ? (
                  <button
                    onClick={handlePause}
                    disabled={isLoading}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50"
                  >
                    Pause
                  </button>
                ) : (
                  <button
                    onClick={handleResume}
                    disabled={isLoading}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50"
                  >
                    Reprendre
                  </button>
                )}
                <button
                  onClick={handleReset}
                  disabled={isLoading}
                  className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50"
                >
                  Réinitialiser
                </button>
              </>
            )}
          </div>

          {/* Settings Button */}
          <button
            onClick={() => setIsSettingsOpen(!isSettingsOpen)}
            className="text-gray-600 hover:text-gray-800 mb-4"
          >
            ⚙️ Paramètres
          </button>

          {/* Settings Panel */}
          {isSettingsOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="border-t pt-4"
            >
              <form onSubmit={handleSettingsSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Durée de focus (minutes)
                  </label>
                  <input
                    type="number"
                    name="focus_duration"
                    defaultValue={settings.focus_duration}
                    min="1"
                    max="60"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Pause courte (minutes)
                  </label>
                  <input
                    type="number"
                    name="short_break_duration"
                    defaultValue={settings.short_break_duration}
                    min="1"
                    max="30"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Pause longue (minutes)
                  </label>
                  <input
                    type="number"
                    name="long_break_duration"
                    defaultValue={settings.long_break_duration}
                    min="1"
                    max="60"
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 rounded-md font-semibold disabled:opacity-50"
                >
                  Sauvegarder
                </button>
              </form>
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Audio element for notifications (optional) */}
      <audio ref={audioRef} preload="auto">
        <source src="/notification.mp3" type="audio/mpeg" />
        <source src="/notification.ogg" type="audio/ogg" />
      </audio>
    </div>
  );
};

export default ClientPomodoro;
