// Exemple de composant pour afficher un événement du calendrier avec le nom de l'équipe et du responsable
import React from 'react';
import { Card, CardContent, Typography, Box, Chip, Avatar } from '@mui/material';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

const EventCard = ({ event }) => {
  // Formatage des dates pour l'affichage
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return format(date, 'dd MMMM yyyy', { locale: fr });
  };

  return (
    <Card 
      sx={{ 
        mb: 2, 
        borderLeft: 6, 
        borderColor: event.status === 'completed' ? 'success.main' : 
                    event.status === 'archived' ? 'text.disabled' : 'primary.main' 
      }}
    >
      <CardContent>
        <Typography variant="h6" component="div">
          {event.title}
        </Typography>
        
        {/* Affichage du nom de l'équipe */}
        {event.team_name && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              Équipe:
            </Typography>
            <Chip 
              label={event.team_name} 
              size="small" 
              color="primary" 
              variant="outlined" 
            />
          </Box>
        )}
        
        {/* Affichage du responsable */}
        {event.created_by_name && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              Responsable:
            </Typography>
            <Chip
              avatar={<Avatar>{event.created_by_name.charAt(0).toUpperCase()}</Avatar>}
              label={event.created_by_name}
              size="small"
              variant="outlined"
            />
          </Box>
        )}
        
        <Box sx={{ mt: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Date: {formatDate(event.start_date)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Horaire: {event.start_time} - {event.end_time}
          </Typography>
        </Box>
        
        {event.description && (
          <Typography variant="body2" sx={{ mt: 1 }}>
            {event.description}
          </Typography>
        )}
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Chip 
            label={event.status === 'pending' ? 'En attente' : 
                  event.status === 'completed' ? 'Terminé' : 'Archivé'} 
            size="small"
            color={event.status === 'completed' ? 'success' : 
                  event.status === 'archived' ? 'default' : 'warning'}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default EventCard;