#!/usr/bin/env python3
"""
Script de test pour vérifier les fonctions sécurisées
"""

import os
import sys
import django
import math

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from core.views.bi_views import safe_divide, safe_percentage, safe_round, safe_float, safe_int

def test_safe_functions():
    """
    Teste toutes les fonctions sécurisées pour éviter les NaN
    """
    print("=== Test des fonctions sécurisées ===\n")
    
    # Test safe_divide
    print("--- Test safe_divide ---")
    test_cases_divide = [
        (10, 2, 5.0),
        (10, 0, 0.0),  # Division par zéro
        (None, 5, 0.0),  # Numérateur None
        (10, None, 0.0),  # Dénominateur None
        (float('nan'), 5, 0.0),  # NaN
        (10, float('inf'), 0.0),  # Infini
        ("10", "2", 5.0),  # <PERSON><PERSON><PERSON>
        ("abc", 5, 0.0),  # <PERSON>îne invalide
    ]
    
    for num, den, expected in test_cases_divide:
        result = safe_divide(num, den)
        status = "✅" if result == expected else "❌"
        print(f"{status} safe_divide({num}, {den}) = {result} (attendu: {expected})")
    
    # Test safe_percentage
    print("\n--- Test safe_percentage ---")
    test_cases_percentage = [
        (10, 100, 10.0),
        (0, 100, 0.0),
        (10, 0, 0.0),  # Division par zéro
        (None, 100, 0.0),  # None
        (50, 200, 25.0),
        (100, 100, 100.0),
        (float('nan'), 100, 0.0),  # NaN
    ]
    
    for num, den, expected in test_cases_percentage:
        result = safe_percentage(num, den)
        status = "✅" if result == expected else "❌"
        print(f"{status} safe_percentage({num}, {den}) = {result}% (attendu: {expected}%)")
    
    # Test safe_int
    print("\n--- Test safe_int ---")
    test_cases_int = [
        (10, 10),
        (10.5, 10),
        ("10", 10),
        ("10.5", 10),
        (None, 0),
        ("abc", 0),
        (float('nan'), 0),
        (float('inf'), 0),
        ("", 0),
    ]
    
    for value, expected in test_cases_int:
        result = safe_int(value)
        status = "✅" if result == expected else "❌"
        print(f"{status} safe_int({value}) = {result} (attendu: {expected})")
    
    # Test safe_float
    print("\n--- Test safe_float ---")
    test_cases_float = [
        (10, 10.0),
        (10.5, 10.5),
        ("10.5", 10.5),
        (None, 0.0),
        ("abc", 0.0),
        (float('nan'), 0.0),
        (float('inf'), 0.0),
        ("", 0.0),
    ]
    
    for value, expected in test_cases_float:
        result = safe_float(value)
        status = "✅" if result == expected else "❌"
        print(f"{status} safe_float({value}) = {result} (attendu: {expected})")
    
    # Test safe_round
    print("\n--- Test safe_round ---")
    test_cases_round = [
        (10.567, 10.57),
        (10.123, 10.12),
        (None, 0.0),
        (float('nan'), 0.0),
        (float('inf'), 0.0),
        ("10.567", 10.57),
        ("abc", 0.0),
    ]
    
    for value, expected in test_cases_round:
        result = safe_round(value)
        status = "✅" if result == expected else "❌"
        print(f"{status} safe_round({value}) = {result} (attendu: {expected})")
    
    print("\n=== Test de scénarios réels ===")
    
    # Simuler des données réelles qui causaient des NaN
    events_completed = 0
    events_total = 0
    
    # Calcul qui causait NaN avant
    completion_rate = safe_percentage(events_completed, events_total)
    print(f"✅ Taux de completion (0/0): {completion_rate}% (pas de NaN)")
    
    # Autre scénario problématique
    tasks_pending = None
    tasks_total = 10
    
    pending_rate = safe_percentage(tasks_pending, tasks_total)
    print(f"✅ Taux en attente (None/10): {pending_rate}% (pas de NaN)")
    
    # Test avec des valeurs string venant de la DB
    string_value = "5"
    int_result = safe_int(string_value)
    print(f"✅ Conversion string vers int: '{string_value}' -> {int_result}")
    
    print("\n🎉 Tous les tests des fonctions sécurisées sont réussis!")
    return True

def test_nan_detection():
    """
    Teste la détection et la gestion des valeurs NaN
    """
    print("\n=== Test de détection des NaN ===")
    
    # Créer des valeurs NaN
    nan_value = float('nan')
    inf_value = float('inf')
    normal_value = 10.5
    
    print(f"math.isnan({nan_value}) = {math.isnan(nan_value)}")
    print(f"math.isinf({inf_value}) = {math.isinf(inf_value)}")
    print(f"math.isnan({normal_value}) = {math.isnan(normal_value)}")
    
    # Test avec les fonctions sécurisées
    print(f"safe_float(NaN) = {safe_float(nan_value)}")
    print(f"safe_float(Inf) = {safe_float(inf_value)}")
    print(f"safe_float(10.5) = {safe_float(normal_value)}")
    
    return True

if __name__ == "__main__":
    success1 = test_safe_functions()
    success2 = test_nan_detection()
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont réussis! Les fonctions sécurisées fonctionnent correctement.")
    else:
        print("\n❌ Des problèmes ont été détectés dans les fonctions sécurisées.")
        sys.exit(1)
