/**
 * Composant de contrôle d'accès basé sur les permissions d'équipe
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/components/teams/
 */

import React from 'react';
import { useAuth } from '../../contexts/AuthContext'; // Ajustez le chemin selon votre structure
import { checkTeamPermissions } from '../../services/permissionService'; // Ajustez le chemin selon votre structure

/**
 * Composant qui affiche son contenu uniquement si l'utilisateur a la permission requise pour l'équipe
 * @param {Object} team - L'équipe concernée
 * @param {String} permissionType - Type de permission (canView, canManage, canAddMembers, etc.)
 * @param {ReactNode} children - Le contenu à afficher
 * @param {ReactNode} fallback - Contenu alternatif si la permission est refusée
 */
const TeamPermissionGate = ({ team, permissionType, children, fallback = null }) => {
  const { user } = useAuth();
  const permissions = checkTeamPermissions(user, team);
  
  if (permissions[permissionType]) {
    return <>{children}</>;
  }
  
  return fallback;
};

export default TeamPermissionGate;

/**
 * Exemple d'utilisation dans un composant d'équipe:
 * 
 * import TeamPermissionGate from '../components/teams/TeamPermissionGate';
 * 
 * const TeamCard = ({ team }) => {
 *   return (
 *     <div className="team-card">
 *       <h3>{team.name}</h3>
 *       <p>{team.description}</p>
 *       
 *       <TeamPermissionGate team={team} permissionType="canManage">
 *         <div className="team-actions">
 *           <button className="edit-btn">Modifier</button>
 *           <button className="delete-btn">Supprimer</button>
 *         </div>
 *       </TeamPermissionGate>
 *       
 *       <TeamPermissionGate team={team} permissionType="canAddMembers">
 *         <button className="add-member-btn">Ajouter un membre</button>
 *       </TeamPermissionGate>
 *     </div>
 *   );
 * };
 */