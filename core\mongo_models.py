from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>an<PERSON>ield
from werkzeug.security import generate_password_hash, check_password_hash
from bson import ObjectId
from datetime import datetime , timezone
import logging
from .models.event_model import Event
from .models.personal_event_model import PersonalEvent
from .models.personal_note_model import PersonalNote
from .models.personal_journal_model import PersonalJournal

logger = logging.getLogger(__name__)

class User(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    name = StringField(required=True)
    email = StringField(required=True, unique=True)
    password = StringField(required=True)
    temp_password_required = BooleanField(default=False)
    temp_password_used = BooleanField(default=False)  # Pour suivre si le mot de passe temporaire a déjà été utilisé
    password_reset_token = StringField()
    password_reset_expires = DateTimeField()
    role = StringField(default='client', choices=['super_admin', 'admin', 'employee', 'client'])
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    last_login = DateTimeField()
    permissions = DictField(default={
        # Permissions par défaut pour tous les utilisateurs
        # Ces permissions seront ajustées en fonction du rôle lors de la création

        # Gestion utilisateurs (réservé aux super_admin)
        'manage_users': False,
        'activate_user_permissions': False,

        # Gestion équipes (pour les admins)
        'manage_teams': False,
        'manage_team_tasks': False,
        'manage_team_calendars': False,

        # Gestion personnelle (pour tous les employés)
        'manage_personal_tasks': True,
        'activate_focus_mode': False,
        'manage_journal_notes': False,
        'manage_personal_calendar': True,

        # Dashboards
        'view_main_dashboard': True,
        'view_team_dashboards': False,
        'view_personal_dashboard': True,

        # Archives et personnalisation
        'view_archives': False,
        'unarchive_items': False,
        'customize_events': False
    })

    def save(self, *args, **kwargs):
        import logging
        logger = logging.getLogger(__name__)

        # Ajuster les permissions en fonction du rôle lors de la sauvegarde
        if self.role == 'super_admin':
            # S'assurer que le super_admin a toutes les permissions nécessaires
            self.permissions['manage_users'] = True
            self.permissions['activate_user_permissions'] = True
            # Ajouter d'autres permissions spécifiques au super_admin si nécessaire
            logger.info(f"Mise à jour des permissions pour super_admin: {self.email}")
        elif self.role == 'admin':
            self.permissions['manage_teams'] = True
            self.permissions['manage_team_tasks'] = True
            self.permissions['manage_team_calendars'] = True
            self.permissions['view_team_dashboards'] = True
            logger.info(f"Mise à jour des permissions pour admin: {self.email}")
        elif self.role == 'employee':
            # Les employés ont déjà les permissions de gestion personnelle par défaut
            logger.info(f"Permissions par défaut pour employee: {self.email}")
        elif self.role == 'client':
            # Les clients ont des permissions limitées
            logger.info(f"Permissions limitées pour client: {self.email}")

        # Journaliser les modifications importantes
        if '_changed_fields' in self._data and ('password' in self._changed_fields or 'role' in self._changed_fields):
            logger.info(f"Modification importante pour l'utilisateur {self.email}: {self._changed_fields}")

        return super(User, self).save(*args, **kwargs)

    def update_last_login(self):
        """Met à jour la date de dernière connexion"""
        self.last_login = datetime.now(timezone.utc)
        self.save()

    def get_account_age(self):
        """Retourne l'âge du compte en jours"""
        if not self.created_at:
            return 0
        delta = datetime.now(timezone.utc) - self.created_at
        return delta.days

    def get_last_login_formatted(self):
        """Retourne la date de dernière connexion formatée"""
        if not self.last_login:
            return "Jamais connecté"
        return self.last_login.strftime("%d %B %Y à %H:%M")

    def get_creation_date_formatted(self):
        """Retourne la date de création formatée"""
        if not self.created_at:
            return "Date inconnue"
        return self.created_at.strftime("%d %B %Y à %H:%M")
    meta = {
        'collection': 'users',
        'indexes': [
            {'fields': ['email'], 'unique': True}
        ]
    }

    # Add these properties for Django authentication
    @property
    def is_authenticated(self):
        return True

    @property
    def is_active(self):
        return True

    @property
    def is_anonymous(self):
        return False

    def get_username(self):
        return self.email

    def set_password(self, password):
        """Hash et stocke le mot de passe"""
        import logging
        logger = logging.getLogger(__name__)

        if not password:
            logger.warning(f"Tentative de définition d'un mot de passe vide pour {self.email}")
            return

        # Vérification de la complexité du mot de passe
        if len(password) < 8:
            logger.warning(f"Tentative de définition d'un mot de passe trop court pour {self.email}")
            # Ne pas lever d'exception ici, car cela pourrait bloquer d'autres processus
            # La validation doit être faite au niveau de la vue

        # Hasher et stocker le mot de passe
        self.password = generate_password_hash(password)
        logger.info(f"Mot de passe mis à jour pour {self.email}, rôle: {self.role}")

    def check_password(self, password):
        """Vérifie le mot de passe hashé"""
        import logging
        logger = logging.getLogger(__name__)

        if not password:
            logger.warning(f"Tentative de vérification avec un mot de passe vide pour {self.email}")
            return False

        if not self.password:
            logger.warning(f"L'utilisateur {self.email} n'a pas de mot de passe défini")
            return False

        result = check_password_hash(self.password, password)
        if not result:
            logger.warning(f"Échec de vérification du mot de passe pour {self.email}")

        return result

    def is_super_admin(self):
        return self.role == 'super_admin'

    def cascade_delete(self):
        """
        Supprime l'utilisateur et toutes ses références dans l'application.
        Cette méthode doit être appelée avant de supprimer l'utilisateur.
        """
        user_id = str(self.id)
        user_role = self.role
        user_email = self.email
        user_name = self.name
        logger.info(f"Début de la suppression en cascade RADICALE pour l'utilisateur {user_email} (ID: {user_id}, rôle: {user_role})")

        try:
            # APPROCHE RADICALE: Supprimer ou mettre à jour toutes les références à l'utilisateur dans toutes les collections

            # 1. ÉQUIPES
            # 1.1 Supprimer toutes les équipes dont l'utilisateur est responsable
            admin_teams = Team.objects(responsable=user_id)
            admin_teams_count = admin_teams.count()
            logger.info(f"Suppression de {admin_teams_count} équipes dont l'utilisateur {user_email} est responsable")

            for team in admin_teams:
                team_id = str(team.id)
                team_name = team.name

                # Supprimer tous les événements associés à cette équipe
                Event.objects(team_id=team_id).delete()

                # Supprimer toutes les tâches associées à cette équipe
                from .models.team_task_model import TeamTask
                TeamTask.objects(team_id=team_id).delete()

                # Supprimer l'équipe
                team.delete()
                logger.info(f"Équipe {team_name} (ID: {team_id}) et toutes ses données associées supprimées")

            # 1.2 Retirer l'utilisateur de toutes les équipes dont il est membre
            teams_with_user = Team.objects(members__has_key=user_id)
            teams_member_count = teams_with_user.count()
            logger.info(f"Retrait de l'utilisateur de {teams_member_count} équipes dont il est membre")

            for team in teams_with_user:
                if user_id in team.members:
                    del team.members[user_id]
                    team.save()

            # 2. ÉVÉNEMENTS
            # 2.1 Supprimer tous les événements créés par l'utilisateur
            events_created = Event.objects(created_by=user_id)
            events_created_count = events_created.count()
            events_created.delete()
            logger.info(f"Suppression de {events_created_count} événements créés par l'utilisateur")

            # 2.2 Mettre à jour tous les événements où l'utilisateur est assigné
            events_assigned = Event.objects(member_id=user_id)
            events_assigned_count = events_assigned.count()
            events_assigned.update(member_id=None, member_name=None)
            logger.info(f"Mise à jour de {events_assigned_count} événements où l'utilisateur était assigné")

            # 2.3 Mettre à jour TOUS les événements où l'utilisateur apparaît dans created_by_name
            # Approche plus radicale: mettre à jour tous les événements avec l'ID ou le nom de l'utilisateur
            events_created_name = Event.objects(created_by_name=user_name)
            events_created_name_count = events_created_name.count()
            events_created_name.update(created_by_name="Utilisateur supprimé")

            # Mettre à jour également tous les événements avec l'ID de l'utilisateur, même si le nom ne correspond pas
            # Cela garantit que toutes les références sont mises à jour, même en cas d'incohérence
            Event.objects(created_by=user_id).update(created_by_name="Utilisateur supprimé")

            logger.info(f"Mise à jour de {events_created_name_count} événements avec le nom de l'utilisateur")

            # 3. ÉVÉNEMENTS PERSONNELS
            personal_events = PersonalEvent.objects(created_by=user_id)
            personal_events_count = personal_events.count()
            personal_events.delete()
            logger.info(f"Suppression de {personal_events_count} événements personnels de l'utilisateur")

            # 4. TÂCHES D'ÉQUIPE
            from .models.team_task_model import TeamTask

            # 4.1 Supprimer toutes les tâches créées par l'utilisateur
            team_tasks_created = TeamTask.objects(created_by=user_id)
            team_tasks_created_count = team_tasks_created.count()
            team_tasks_created.delete()
            logger.info(f"Suppression de {team_tasks_created_count} tâches d'équipe créées par l'utilisateur")

            # 4.2 Mettre à jour toutes les tâches où l'utilisateur est responsable
            team_tasks_responsible = TeamTask.objects(responsable=user_id)
            team_tasks_responsible_count = team_tasks_responsible.count()
            team_tasks_responsible.update(responsable=None, responsable_name="Responsable supprimé")
            logger.info(f"Mise à jour de {team_tasks_responsible_count} tâches d'équipe dont l'utilisateur était responsable")

            # 4.3 Mettre à jour toutes les tâches où l'utilisateur est assigné
            team_tasks_assigned = TeamTask.objects(member_id=user_id)
            team_tasks_assigned_count = team_tasks_assigned.count()
            team_tasks_assigned.update(member_id=None, member_name=None)
            logger.info(f"Mise à jour de {team_tasks_assigned_count} tâches d'équipe où l'utilisateur était assigné")

            # 4.4 Mettre à jour toutes les tâches où l'utilisateur apparaît dans created_by_name ou responsable_name
            team_tasks_created_name = TeamTask.objects(created_by_name=user_name)
            team_tasks_created_name_count = team_tasks_created_name.count()
            team_tasks_created_name.update(created_by_name="Utilisateur supprimé")

            # Mettre à jour également toutes les tâches avec l'ID de l'utilisateur, même si le nom ne correspond pas
            TeamTask.objects(created_by=user_id).update(created_by_name="Utilisateur supprimé")

            # Mettre à jour le nom du responsable dans toutes les tâches
            team_tasks_resp_name = TeamTask.objects(responsable_name=user_name)
            team_tasks_resp_name_count = team_tasks_resp_name.count()
            team_tasks_resp_name.update(responsable_name="Responsable supprimé")

            # Mettre à jour également toutes les tâches avec l'ID de l'utilisateur comme responsable
            TeamTask.objects(responsable=user_id).update(responsable_name="Responsable supprimé")

            logger.info(f"Mise à jour de {team_tasks_created_name_count} tâches avec le nom de l'utilisateur comme créateur")
            logger.info(f"Mise à jour de {team_tasks_resp_name_count} tâches avec le nom de l'utilisateur comme responsable")

            # 5. TÂCHES PERSONNELLES
            from .models.personal_task_model import PersonalTask
            personal_tasks = PersonalTask.objects(created_by=user_id)
            personal_tasks_count = personal_tasks.count()
            personal_tasks.delete()
            logger.info(f"Suppression de {personal_tasks_count} tâches personnelles de l'utilisateur")

            # 6. NOTES PERSONNELLES
            from .models.personal_note_model import PersonalNote
            personal_notes = PersonalNote.objects(created_by=user_id)
            personal_notes_count = personal_notes.count()
            personal_notes.delete()
            logger.info(f"Suppression de {personal_notes_count} notes personnelles de l'utilisateur")

            # 7. JOURNAUX PERSONNELS
            from .models.personal_journal_model import PersonalJournal
            personal_journals = PersonalJournal.objects(created_by=user_id)
            personal_journals_count = personal_journals.count()
            personal_journals.delete()
            logger.info(f"Suppression de {personal_journals_count} entrées de journal de l'utilisateur")

            # 8. PARAMÈTRES POMODORO
            from .models.pomodoro_model import PomodoroSettings
            try:
                pomodoro_settings = PomodoroSettings.objects.get(user_id=user_id)
                pomodoro_settings.delete()
                logger.info(f"Suppression des paramètres Pomodoro de l'utilisateur {user_email}")
            except PomodoroSettings.DoesNotExist:
                logger.info(f"Aucun paramètre Pomodoro trouvé pour l'utilisateur {user_email}")

            # 9. MÉTRIQUES BI
            from .models.bi_model import BiMetric, BiDashboard
            # Supprimer les métriques BI de l'utilisateur
            bi_metrics = BiMetric.objects(user_id=user_id)
            bi_metrics_count = bi_metrics.count()
            bi_metrics.delete()
            logger.info(f"Suppression de {bi_metrics_count} métriques BI de l'utilisateur")

            # Supprimer les tableaux de bord BI de l'utilisateur
            bi_dashboards = BiDashboard.objects(user_id=user_id)
            bi_dashboards_count = bi_dashboards.count()
            bi_dashboards.delete()
            logger.info(f"Suppression de {bi_dashboards_count} tableaux de bord BI de l'utilisateur")

            # 10. RECHERCHE SUPPLÉMENTAIRE DE RÉFÉRENCES
            # Rechercher et mettre à jour toutes les autres collections qui pourraient contenir des références à l'utilisateur

            # 10.1 Vérifier si d'autres équipes ont encore des références à l'utilisateur
            remaining_teams_resp = Team.objects(responsable=user_id).count()
            if remaining_teams_resp > 0:
                logger.warning(f"Il reste encore {remaining_teams_resp} équipes avec l'utilisateur comme responsable")
                Team.objects(responsable=user_id).update(responsable="", responsable_name="Responsable supprimé")

            remaining_teams_members = Team.objects(members__has_key=user_id).count()
            if remaining_teams_members > 0:
                logger.warning(f"Il reste encore {remaining_teams_members} équipes avec l'utilisateur comme membre")
                # Approche radicale pour supprimer les références restantes
                for team in Team.objects(members__has_key=user_id):
                    if user_id in team.members:
                        del team.members[user_id]
                        team.save()

            logger.info(f"Fin de la suppression en cascade RADICALE pour l'utilisateur {user_email} (ID: {user_id})")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la suppression en cascade pour l'utilisateur {user_email} (ID: {user_id}): {str(e)}")
            # Même en cas d'erreur, on essaie de continuer pour supprimer le maximum de références
            try:
                # Dernière tentative de nettoyage radical
                # 1. Équipes
                Team.objects(responsable=user_id).update(responsable="", responsable_name="Responsable supprimé")
                Team.objects(responsable_name=user_name).update(responsable_name="Responsable supprimé")

                # Supprimer l'utilisateur des équipes dont il est membre
                for team in Team.objects(members__has_key=user_id):
                    if user_id in team.members:
                        del team.members[user_id]
                        team.save()

                # 2. Événements
                # Supprimer les événements créés par l'utilisateur
                Event.objects(created_by=user_id).delete()
                # Mettre à jour les événements où l'utilisateur est assigné
                Event.objects(member_id=user_id).update(member_id="", member_name="")
                # Mettre à jour les événements avec le nom de l'utilisateur
                Event.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 3. Événements personnels
                PersonalEvent.objects(created_by=user_id).delete()
                PersonalEvent.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 4. Tâches d'équipe
                from .models.team_task_model import TeamTask
                # Supprimer les tâches créées par l'utilisateur
                TeamTask.objects(created_by=user_id).delete()
                # Mettre à jour les tâches où l'utilisateur est responsable
                TeamTask.objects(responsable=user_id).update(responsable="", responsable_name="Responsable supprimé")
                TeamTask.objects(responsable_name=user_name).update(responsable_name="Responsable supprimé")
                # Mettre à jour les tâches où l'utilisateur est assigné
                TeamTask.objects(member_id=user_id).update(member_id="", member_name="")
                # Mettre à jour les tâches avec le nom de l'utilisateur
                TeamTask.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 5. Tâches personnelles
                from .models.personal_task_model import PersonalTask
                PersonalTask.objects(created_by=user_id).delete()
                PersonalTask.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 6. Notes personnelles
                from .models.personal_note_model import PersonalNote
                PersonalNote.objects(created_by=user_id).delete()
                PersonalNote.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 7. Journaux personnels
                from .models.personal_journal_model import PersonalJournal
                PersonalJournal.objects(created_by=user_id).delete()
                PersonalJournal.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

                # 8. Paramètres Pomodoro
                from .models.pomodoro_model import PomodoroSettings
                try:
                    pomodoro_settings = PomodoroSettings.objects.get(user_id=user_id)
                    pomodoro_settings.delete()
                except PomodoroSettings.DoesNotExist:
                    pass

                # 9. Métriques BI
                from .models.bi_model import BiMetric, BiDashboard
                BiMetric.objects(user_id=user_id).delete()
                BiDashboard.objects(user_id=user_id).delete()

                logger.info(f"Nettoyage de secours effectué pour l'utilisateur {user_email}")
                return True
            except Exception as e2:
                logger.error(f"Échec du nettoyage de secours pour l'utilisateur {user_email}: {str(e2)}")
                raise

    def __str__(self):
        return f"User(id={self.id}, email={self.email}, role={self.role})"

class Team(Document):
    name = StringField(required=True, unique=True)
    description = StringField()
    responsable = StringField(required=True)  # ID de l'admin qui a créé l'équipe
    responsable_name = StringField(required=True)  # Nom de l'admin qui a créé l'équipe
    members = DictField(default={})  # {user_id: {name, role, added_at}} pour les employés uniquement
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    def can_manage_team(self, user):
        """Vérifie si un utilisateur peut gérer cette équipe"""
        # Vérifier si l'utilisateur est un admin et s'il est le responsable de l'équipe
        if user.role == 'admin':
            # Si l'admin est le responsable de l'équipe, il peut la gérer
            return str(user.id) == self.responsable
        return False

    def can_view_team(self, user):
        """Vérifie si un utilisateur peut voir cette équipe"""
        # Les admins peuvent voir toutes les équipes
        if user.role == 'admin':
            return True
        # Les employés peuvent voir les équipes dont ils sont membres
        if user.role == 'employee':
            return str(user.id) in self.members
        # Les super_admin n'ont pas accès aux équipes
        return False

    def add_member(self, user):
        """Ajoute un employé à l'équipe"""
        if user.role != 'employee':
            raise ValueError('Seuls les employés peuvent être membres d\'une équipe')

        user_id = str(user.id)

        # Recharger l'équipe depuis la base de données pour éviter les problèmes de cache
        self.reload()

        if user_id in self.members:
            # Au lieu de lever une exception, retourner True si l'utilisateur est déjà membre
            # Cela évite les erreurs lors des tentatives d'ajout multiples
            logger.info(f"L'utilisateur {user_id} est déjà membre de l'équipe {self.id}")
            return True

        # Ajouter l'employé avec son nom et son rôle
        self.members[user_id] = {
            'name': user.name,
            'role': 'employee',
            'added_at': datetime.now(timezone.utc)
        }
        self.updated_at = datetime.now(timezone.utc)
        self.save()
        return True

    def remove_member(self, user_id):
        """Retire un employé de l'équipe"""
        user_id_str = str(user_id)
        if user_id_str not in self.members:
            raise ValueError('Cet employé n\'est pas membre de l\'équipe')

        try:
            del self.members[user_id_str]
            self.updated_at = datetime.now(timezone.utc)
            self.save()
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la suppression du membre {user_id_str}: {str(e)}")
            return False

    meta = {
        'collection': 'teams',
        'indexes': [
            {'fields': ['name'], 'unique': True},
            {'fields': ['responsable']}
        ]
    }



    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Vérifier si c'est une mise à jour réelle (pas juste un save sans changement)
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(Team, self).save(*args, **kwargs)



    def set_responsable(self, user):
        """Set team responsable"""
        if user.role != 'admin':
            raise ValueError("Only admins can be team responsables")
        if not user.permissions.get('manage_teams', False):
            raise ValueError("Admin must have team management permissions")
        self.responsable = str(user.id)
        self.responsable_name = user.name
        self.save()  # Utilise la méthode save() surchargée

    def cascade_delete(self):
        """
        Supprime l'équipe et toutes ses données associées (événements, tâches, etc.)
        Cette méthode doit être appelée avant de supprimer l'équipe.
        """
        team_id = str(self.id)
        team_name = self.name
        logger.info(f"Début de la suppression en cascade pour l'équipe {team_name} (ID: {team_id})")

        try:
            # 1. Supprimer tous les événements associés à cette équipe
            from .models.event_model import Event
            events = Event.objects(team_id=team_id)
            events_count = events.count()
            events.delete()
            logger.info(f"Suppression de {events_count} événements associés à l'équipe {team_name}")

            # 2. Supprimer toutes les tâches associées à cette équipe
            from .models.team_task_model import TeamTask
            team_tasks = TeamTask.objects(team_id=team_id)
            team_tasks_count = team_tasks.count()
            team_tasks.delete()
            logger.info(f"Suppression de {team_tasks_count} tâches associées à l'équipe {team_name}")

            # 3. Supprimer l'équipe elle-même
            logger.info(f"Équipe {team_name} (ID: {team_id}) et toutes ses données associées supprimées")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de la suppression en cascade pour l'équipe {team_name} (ID: {team_id}): {str(e)}")
            return False



class BlacklistedToken(Document):
    token = StringField(required=True, unique=True)
    blacklisted_at = DateTimeField()
    jti = StringField(required=False)  # JWT ID pour une recherche plus efficace

    meta = {
        'collection': 'blacklisted_tokens',
        'indexes': [
            {'fields': ['token'], 'unique': True},
            {'fields': ['jti']},  # Index sur le JTI pour une recherche plus rapide
            {'fields': ['blacklisted_at'], 'expireAfterSeconds': 86400 * 7}  # Auto-delete after 7 days
        ]
    }

    def save(self, *args, **kwargs):
        if not self.blacklisted_at:
            self.blacklisted_at = datetime.now(timezone.utc)
        return super(BlacklistedToken, self).save(*args, **kwargs)

    @classmethod
    def is_blacklisted(cls, token):
        """Check if a token is blacklisted"""
        try:
            # Vérifier d'abord par le token complet
            if cls.objects(token=token).first():
                return True

            # Si le token n'est pas trouvé directement, essayer de vérifier par JTI
            # Cette partie est utile pour la compatibilité avec les anciens tokens
            from rest_framework_simplejwt.tokens import TokenError
            try:
                from rest_framework_simplejwt.tokens import UntypedToken
                # Essayer de décoder le token pour obtenir le JTI
                decoded = UntypedToken(token)
                jti = decoded.get('jti')
                if jti and cls.objects(jti=jti).first():
                    return True
            except TokenError:
                # Si le token ne peut pas être décodé, ignorer cette vérification
                pass

            return False
        except Exception as e:
            # En cas d'erreur, logger et considérer le token comme non blacklisté
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Erreur lors de la vérification du token blacklisté: {str(e)}")
            return False