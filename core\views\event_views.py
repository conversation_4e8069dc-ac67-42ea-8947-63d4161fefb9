from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import User, Team
from ..models.event_model import Event
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class EventListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request):
        """Créer un nouvel événement (admin responsable de l'équipe uniquement)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de l'événement est requis"}, status=400)
            if not data.get('start_date') or not data.get('end_date'):
                return Response({"error": "Les dates de début et de fin sont requises"}, status=400)
            if not data.get('start_time') or not data.get('end_time'):
                return Response({"error": "Les heures de début et de fin sont requises"}, status=400)

            # Vérifier qu'au moins une équipe ou un membre est assigné
            if not data.get('team_id') and not data.get('member_id'):
                return Response({"error": "L'événement doit être assigné à une équipe ou à un membre"}, status=400)

            # Vérifier si un événement avec le même titre existe déjà pour la même équipe/membre
            duplicate_query = {'title': data['title']}
            if data.get('team_id'):
                duplicate_query['team_id'] = data['team_id']
            if data.get('member_id'):
                duplicate_query['member_id'] = data['member_id']

            if Event.objects(**duplicate_query).first():
                scope = "cette équipe" if data.get('team_id') else "ce membre"
                return Response({"error": f"Un événement avec ce titre existe déjà pour {scope}"}, status=400)

            # Vérifier que l'admin est responsable de l'équipe ou du membre assigné
            if data.get('team_id'):
                from ..mongo_models import Team
                try:
                    team = Team.objects.get(id=data['team_id'])
                    if team.responsable != str(user.id):
                        return Response({"error": "Vous n'êtes pas autorisé à créer un événement pour cette équipe. Seul l'administrateur responsable de l'équipe peut le faire."}, status=403)
                except Team.DoesNotExist:
                    return Response({"error": "Équipe non trouvée"}, status=404)
                except Exception as e:
                    logger.error(f"Erreur lors de la vérification de l'équipe: {str(e)}")
                    return Response({"error": "Une erreur est survenue lors de la vérification de l'équipe", "message": "Une erreur est survenue lors de la vérification de l'équipe"}, status=403)
            elif data.get('member_id'):
                # Vérifier si le membre fait partie d'une équipe dont l'admin est responsable
                from ..mongo_models import Team, User
                try:
                    # Vérifier si le membre existe
                    User.objects.get(id=data['member_id'])
                    # Trouver les équipes dont l'admin est responsable
                    admin_teams = Team.objects(responsable=str(user.id))
                    member_found = False
                    for team in admin_teams:
                        if data['member_id'] in team.members:
                            member_found = True
                            break
                    if not member_found:
                        return Response({"error": "Vous n'êtes pas autorisé à créer un événement pour ce membre. Seul l'administrateur responsable de l'équipe dont le membre fait partie peut le faire."}, status=403)
                except User.DoesNotExist:
                    return Response({"error": "Membre non trouvé"}, status=404)
                except Exception as e:
                    logger.error(f"Erreur lors de la vérification du membre: {str(e)}")
                    return Response({"error": "Une erreur est survenue lors de la vérification du membre", "message": "Une erreur est survenue lors de la vérification de l'équipe"}, status=403)

            # Vérifier que les dates sont valides (pas dans le passé)
            try:
                start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
                now = datetime.now(timezone.utc)

                if start_date.date() < now.date():
                    return Response({"error": "La date de début ne peut pas être dans le passé"}, status=400)
                if end_date.date() < start_date.date():
                    return Response({"error": "La date de fin ne peut pas être antérieure à la date de début"}, status=400)
            except ValueError:
                return Response({"error": "Format de date invalide"}, status=400)

            # Créer l'événement
            event = Event(
                title=data['title'],
                description=data.get('description', ''),
                start_date=datetime.fromisoformat(data['start_date'].replace('Z', '+00:00')),
                end_date=datetime.fromisoformat(data['end_date'].replace('Z', '+00:00')),
                start_time=data['start_time'],
                end_time=data['end_time'],
                note=data.get('note', ''),
                status='pending',
                color=data.get('color', '#3788d8'),  # Utiliser la couleur fournie ou la couleur par défaut
                team_id=data.get('team_id'),
                member_id=data.get('member_id'),
                created_by=str(user.id),
                created_by_name=user.name
            )

            # Ajouter le nom de l'équipe si une équipe est assignée
            if data.get('team_id'):
                try:
                    from ..mongo_models import Team
                    team = Team.objects.get(id=data['team_id'])
                    event.team_name = team.name
                except Exception as e:
                    logger.error(f"Erreur lors de la récupération du nom de l'équipe: {str(e)}")
                    # Continuer sans le nom de l'équipe si une erreur se produit
            event.save()

            return Response({
                "message": "Événement créé avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "description": event.description,
                    "start_date": event.start_date,
                    "end_date": event.end_date,
                    "start_time": event.start_time,
                    "end_time": event.end_time,
                    "note": event.note,
                    "status": event.status,
                    "color": event.color,
                    "team_id": event.team_id,
                    "team_name": event.team_name,
                    "member_id": event.member_id,
                    "created_by": event.created_by,
                    "created_by_name": event.created_by_name,
                    "created_at": event.created_at,
                    "updated_at": event.updated_at
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in EventListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de l'événement"}, status=500)

    def get(self, request):
        """Liste des événements (filtrée selon le rôle et les permissions)"""
        try:
            user = request.user

            # Filtrer les événements selon le rôle de l'utilisateur
            if user.role == 'admin':
                # Les admins voient uniquement les événements des équipes dont ils sont responsables
                # Utilisation de l'importation globale de Team
                # Récupérer les équipes dont l'admin est responsable
                admin_teams = Team.objects(responsable=str(user.id)).values_list('id')
                admin_team_ids = [str(team_id) for team_id in admin_teams]

                # Récupérer les membres de ces équipes
                team_members = []
                for team_id in admin_team_ids:
                    try:
                        team = Team.objects.get(id=team_id)
                        team_members.extend(list(team.members.keys()))
                    except Exception:
                        continue

                # Filtrer les événements
                if admin_team_ids:
                    events = Event.objects().filter(__raw__={'$or': [
                        {'team_id': {'$in': admin_team_ids}},
                        {'member_id': {'$in': team_members}}
                    ]})
                else:
                    # Si l'admin n'est responsable d'aucune équipe, il ne voit aucun événement
                    events = Event.objects().none()
            elif user.role == 'employee':
                # Les employés voient les événements qui leur sont assignés ou assignés à leurs équipes
                user_id = str(user.id)

                # Récupérer les équipes dont l'utilisateur est membre
                user_teams = Team.objects(members__has_key=user_id).values_list('id')
                team_ids = [str(team_id) for team_id in user_teams]

                # Filtrer les événements
                events = Event.objects()
                if team_ids:
                    events = events.filter(__raw__={'$or': [
                        {'member_id': user_id},
                        {'team_id': {'$in': team_ids}}
                    ]})
                else:
                    events = events.filter(member_id=user_id)
            else:
                # Les autres rôles n'ont pas accès aux événements
                return Response({"error": "Vous n'avez pas les droits pour voir les événements"}, status=403)

            # Formater les événements
            events_data = []
            for event in events:
                events_data.append({
                    'id': str(event.id),
                    'title': event.title,
                    'description': event.description,
                    'start_date': event.start_date,
                    'end_date': event.end_date,
                    'start_time': event.start_time,
                    'end_time': event.end_time,
                    'note': event.note,
                    'status': event.status,
                    'color': event.color,
                    'team_id': event.team_id,
                    'team_name': event.team_name,
                    'member_id': event.member_id,
                    'created_by': event.created_by,
                    'created_by_name': event.created_by_name,
                    'created_at': event.created_at,
                    'updated_at': event.updated_at,
                    'can_manage': event.can_manage_event(user),
                    'can_update_status': event.can_update_status(user)
                })

            return Response(events_data)

        except Exception as e:
            logger.error(f"Error in EventListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class EventDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, event_id):
        """Récupérer les détails d'un événement"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user

            # Vérifier les droits d'accès
            if user.role == 'admin':
                # Les admins ont accès uniquement aux événements des équipes dont ils sont responsables
                if event.team_id:
                    from ..mongo_models import Team
                    try:
                        team = Team.objects.get(id=event.team_id)
                        if str(user.id) != team.responsable:
                            return Response({"error": "Vous n'avez pas les droits pour voir cet événement. Seul l'administrateur responsable de l'équipe peut y accéder."}, status=403)
                    except Team.DoesNotExist:
                        return Response({"error": "Équipe non trouvée"}, status=404)
                elif event.member_id:
                    # Vérifier si le membre fait partie d'une équipe dont l'admin est responsable
                    from ..mongo_models import Team
                    admin_teams = Team.objects(responsable=str(user.id)).values_list('id')
                    admin_team_ids = [str(team_id) for team_id in admin_teams]

                    member_in_admin_team = False
                    for team_id in admin_team_ids:
                        try:
                            team = Team.objects.get(id=team_id)
                            if event.member_id in team.members:
                                member_in_admin_team = True
                                break
                        except Exception:
                            continue

                    if not member_in_admin_team:
                        return Response({"error": "Vous n'avez pas les droits pour voir cet événement. Seul l'administrateur responsable de l'équipe dont le membre fait partie peut y accéder."}, status=403)
                else:
                    # Si l'événement n'est associé ni à une équipe ni à un membre, l'admin ne peut pas y accéder
                    return Response({"error": "Vous n'avez pas les droits pour voir cet événement."}, status=403)
            elif user.role == 'employee':
                # Les employés ont accès aux événements qui leur sont assignés ou assignés à leurs équipes
                user_id = str(user.id)

                # Si l'événement est assigné à un membre spécifique
                if event.member_id and event.member_id != user_id:
                    return Response({"error": "Vous n'avez pas les droits pour voir cet événement"}, status=403)

                # Si l'événement est assigné à une équipe
                if event.team_id:
                    team = Team.objects.get(id=event.team_id)
                    if user_id not in team.members:
                        return Response({"error": "Vous n'avez pas les droits pour voir cet événement"}, status=403)
            else:
                # Les autres rôles n'ont pas accès aux événements
                return Response({"error": "Vous n'avez pas les droits pour voir les événements"}, status=403)

            # Retourner les détails de l'événement
            return Response({
                'id': str(event.id),
                'title': event.title,
                'description': event.description,
                'start_date': event.start_date,
                'end_date': event.end_date,
                'start_time': event.start_time,
                'end_time': event.end_time,
                'note': event.note,
                'status': event.status,
                'color': event.color,
                'team_id': event.team_id,
                'team_name': event.team_name,
                'member_id': event.member_id,
                'created_by': event.created_by,
                'created_by_name': event.created_by_name,
                'created_at': event.created_at,
                'updated_at': event.updated_at,
                'can_manage': event.can_manage_event(user),
                'can_update_status': event.can_update_status(user)
            })

        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventDetailView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la récupération des détails de l'événement"}, status=500)

    @admin_required
    def put(self, request, event_id):
        """Modifier un événement (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cet événement
            if not event.can_manage_event(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cet événement. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            data = request.data

            # Mettre à jour les champs
            if 'title' in data:
                # Vérifier si un autre événement avec ce titre existe déjà pour la même équipe/membre
                if data['title'] != event.title:
                    duplicate_query = {'title': data['title']}
                    if event.team_id:
                        duplicate_query['team_id'] = event.team_id
                    if event.member_id:
                        duplicate_query['member_id'] = event.member_id

                    if Event.objects(**duplicate_query).first():
                        scope = "cette équipe" if event.team_id else "ce membre"
                        return Response({"error": f"Un événement avec ce titre existe déjà pour {scope}"}, status=400)
                event.title = data['title']
            if 'description' in data:
                event.description = data['description']
            if 'start_date' in data:
                event.start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
            if 'end_date' in data:
                event.end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
            if 'start_time' in data:
                event.start_time = data['start_time']
            if 'end_time' in data:
                event.end_time = data['end_time']
            if 'note' in data:
                event.note = data['note']
            if 'status' in data:
                event.status = data['status']
            if 'color' in data:
                event.color = data['color']
            if 'team_id' in data:
                event.team_id = data['team_id']
                # Mettre à jour le nom de l'équipe si l'ID de l'équipe change
                if data['team_id']:
                    try:
                        from ..mongo_models import Team
                        team = Team.objects.get(id=data['team_id'])
                        event.team_name = team.name
                    except Exception as e:
                        logger.error(f"Erreur lors de la récupération du nom de l'équipe: {str(e)}")
                else:
                    event.team_name = None
            if 'member_id' in data:
                event.member_id = data['member_id']

            # Sauvegarder les modifications
            event.save()

            return Response({
                "message": "Événement mis à jour avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "description": event.description,
                    "start_date": event.start_date,
                    "end_date": event.end_date,
                    "start_time": event.start_time,
                    "end_time": event.end_time,
                    "note": event.note,
                    "status": event.status,
                    "color": event.color,
                    "team_id": event.team_id,
                    "team_name": event.team_name,
                    "member_id": event.member_id,
                    "created_by": event.created_by,
                    "created_by_name": event.created_by_name,
                    "created_at": event.created_at,
                    "updated_at": event.updated_at
                }
            })

        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventDetailView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour de l'événement"}, status=500)

    @admin_required
    def delete(self, request, event_id):
        """Supprimer un événement (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cet événement
            # ou si l'admin est celui qui a créé l'événement
            if not event.can_manage_event(user) and event.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cet événement. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Supprimer l'événement
            event.delete()

            return Response({"message": "Événement supprimé avec succès"})

        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventDetailView.delete: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression de l'événement"}, status=500)

class EventStatusUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, event_id):
        """Mettre à jour le statut d'un événement (uniquement pour les membres assignés, pas pour les admins)"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user

            # Si l'utilisateur est un admin, retourner une erreur 403
            if user.role == 'admin':
                return Response({"error": "Les administrateurs ne sont pas autorisés à mettre à jour le statut des événements, ils peuvent seulement les consulter"}, status=403)

            # Vérifier les droits de mise à jour du statut pour les employés
            if not event.can_update_status(user):
                return Response({"error": "Vous n'avez pas les droits pour mettre à jour le statut de cet événement"}, status=403)

            # Mettre à jour le statut
            data = request.data
            if 'status' not in data:
                return Response({"error": "Le statut est requis"}, status=400)

            event.status = data['status']
            event.save()

            return Response({
                "message": "Statut de l'événement mis à jour avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "status": event.status,
                    "updated_at": event.updated_at
                }
            })

        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventStatusUpdateView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la vérification de l'équipe", "message": "Une erreur est survenue lors de la vérification de l'équipe"}, status=403)

class EventArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, event_id):
        """Archiver un événement (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cet événement
            # ou si l'admin est celui qui a créé l'événement
            if not event.can_manage_event(user) and event.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cet événement. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Archiver l'événement
            event.status = 'archived'
            event.save()

            return Response({
                "message": "Événement archivé avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "status": event.status,
                    "updated_at": event.updated_at
                }
            })

        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventArchiveView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de l'archivage de l'événement"}, status=500)