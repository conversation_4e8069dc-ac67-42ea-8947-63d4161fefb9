import React, { useState, useEffect, useCallback, useRef } from 'react';

const ClientPomodoro = () => {
  const [settings, setSettings] = useState({
    focus_duration: 25,
    short_break_duration: 5,
    long_break_duration: 15,
    sessions_before_long_break: 4
  });
  
  const [currentSession, setCurrentSession] = useState({
    active: false,
    status: 'inactive',
    remaining_time: 0,
    focus_duration: 25,
    break_duration: 5
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  
  // Utiliser useRef pour éviter les re-renders inutiles
  const intervalRef = useRef(null);

  // Fonction pour formater le temps
  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Fonction pour faire un appel API
  const apiCall = useCallback(async (endpoint, method = 'GET', data = null) => {
    try {
      const token = localStorage.getItem('access_token');
      const config = {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };
      
      if (data) {
        config.body = JSON.stringify(data);
      }
      
      const response = await fetch(`http://localhost:8000/api${endpoint}`, config);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    }
  }, []);

  // Charger les paramètres Pomodoro
  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await apiCall('/pomodoro/settings/');
      setSettings(data);
      setCurrentSession(data.current_session);
      console.log('Settings loaded:', data);
    } catch (error) {
      setError(`Erreur lors du chargement des paramètres: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Contrôler la session Pomodoro - VERSION CORRIGÉE
  const controlSession = useCallback(async (action) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccessMessage('');
      
      console.log(`🎯 Action demandée: ${action}`);
      console.log('📊 État actuel:', currentSession);
      
      const data = await apiCall(`/pomodoro/control/${action}/`, 'POST');
      
      console.log('📥 Réponse serveur:', data);
      
      // ✅ CORRECTION CRITIQUE : Mettre à jour l'état immédiatement
      if (data.current_session) {
        setCurrentSession(data.current_session);
        console.log('🔄 État mis à jour:', data.current_session);
      }
      
      // Afficher le message de succès
      if (data.message) {
        setSuccessMessage(data.message);
        console.log('✅ Message:', data.message);
        
        // Effacer le message après 3 secondes
        setTimeout(() => setSuccessMessage(''), 3000);
      }
      
    } catch (error) {
      console.error('❌ Erreur contrôle session:', error);
      setError(`Erreur lors du contrôle de la session: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall, currentSession]);

  // ✅ CORRECTION CRITIQUE : Gestion du timer basée sur le statut
  useEffect(() => {
    console.log('🕐 Timer effect - Status:', currentSession.status, 'Active:', currentSession.active);
    
    // Nettoyer l'intervalle existant
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Démarrer le timer SEULEMENT si la session est active ET pas en pause
    if (currentSession.active && currentSession.status === 'active') {
      console.log('▶️ Démarrage du timer');
      
      intervalRef.current = setInterval(() => {
        setCurrentSession(prev => {
          const newRemainingTime = prev.remaining_time - 1;
          
          console.log('⏰ Timer tick:', newRemainingTime);
          
          if (newRemainingTime <= 0) {
            console.log('⏰ Session terminée automatiquement');
            controlSession('complete');
            return prev;
          }
          
          return {
            ...prev,
            remaining_time: newRemainingTime
          };
        });
      }, 1000);
    } else {
      console.log('⏸️ Timer arrêté - Status:', currentSession.status);
    }

    // Nettoyage
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [currentSession.active, currentSession.status, controlSession]);

  // Charger les paramètres au montage du composant
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  // Gestionnaires d'événements
  const handleStart = () => {
    console.log('🚀 Démarrage demandé');
    controlSession('start');
  };
  
  const handlePause = () => {
    console.log('⏸️ Pause demandée');
    controlSession('pause');
  };
  
  const handleResume = () => {
    console.log('▶️ Reprise demandée');
    controlSession('resume');
  };
  
  const handleReset = () => {
    console.log('🔄 Reset demandé');
    controlSession('reset');
  };

  // Fonction pour déterminer le texte du statut
  const getStatusText = () => {
    switch (currentSession.status) {
      case 'active':
        return 'Session de travail en cours';
      case 'paused':
        return 'Session en pause';
      case 'inactive':
        return 'Prêt à commencer';
      case 'completed':
        return 'Session terminée !';
      default:
        return 'État inconnu';
    }
  };

  // Fonction pour déterminer la couleur du statut
  const getStatusColor = () => {
    switch (currentSession.status) {
      case 'active':
        return 'text-green-600';
      case 'paused':
        return 'text-yellow-600';
      case 'inactive':
        return 'text-gray-600';
      case 'completed':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-2xl p-8 max-w-md w-full">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">Mode Pomodoro</h1>
          
          {/* Messages */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          
          {successMessage && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {successMessage}
            </div>
          )}

          {/* Timer Display */}
          <div className="mb-8">
            <div className="text-6xl font-mono font-bold text-gray-800 mb-4">
              {formatTime(currentSession.remaining_time)}
            </div>
            <div className={`text-lg font-semibold ${getStatusColor()}`}>
              {getStatusText()}
            </div>
            
            {/* Debug Info */}
            <div className="text-xs text-gray-400 mt-2">
              Status: {currentSession.status} | Active: {currentSession.active ? 'Oui' : 'Non'}
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex justify-center space-x-4 mb-8">
            {!currentSession.active ? (
              <button
                onClick={handleStart}
                disabled={isLoading}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50 transition-colors"
              >
                {isLoading ? 'Démarrage...' : 'Démarrer'}
              </button>
            ) : (
              <>
                {currentSession.status === 'active' ? (
                  <button
                    onClick={handlePause}
                    disabled={isLoading}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50 transition-colors"
                  >
                    {isLoading ? 'Pause...' : 'Pause'}
                  </button>
                ) : currentSession.status === 'paused' ? (
                  <button
                    onClick={handleResume}
                    disabled={isLoading}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50 transition-colors"
                  >
                    {isLoading ? 'Reprise...' : 'Reprendre'}
                  </button>
                ) : null}
                
                <button
                  onClick={handleReset}
                  disabled={isLoading}
                  className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold disabled:opacity-50 transition-colors"
                >
                  {isLoading ? 'Reset...' : 'Réinitialiser'}
                </button>
              </>
            )}
          </div>

          {/* Refresh Button pour debug */}
          <button
            onClick={loadSettings}
            disabled={isLoading}
            className="text-gray-600 hover:text-gray-800 text-sm underline"
          >
            🔄 Actualiser l'état
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClientPomodoro;
