# 🚀 Documentation Backend Notora - API et Importation de Données

## 📋 Vue d'ensemble

Cette documentation couvre la logique d'importation des données, les routes API et les dernières modifications apportées au backend de l'application Notora. Ce document est destiné à l'équipe frontend pour faciliter l'intégration et la compréhension du backend.

## 📊 Modèles de données BI et Métriques

### Modèles principaux

Le système utilise MongoDB avec MongoEngine comme ORM et comprend les modèles suivants pour les métriques et tableaux de bord:

1. **BiMetricSnapshot** - Stocke les instantanés de métriques BI
   - Utilisé pour le suivi historique des métriques
   - Remplace l'ancien modèle BiMetric pour éviter les conflits
   - Stocke des métriques par type de tableau de bord (super_admin, admin, employee, client)

2. **BiMetric** - Ancien modèle gardé pour compatibilité
   - Stocke différents types de métriques (user_activity, team_task_status, etc.)
   - N'utilise plus d'index pour éviter les erreurs

3. **BiDashboard** - Configuration des tableaux de bord BI
   - Stocke les préférences d'affichage par utilisateur
   - Permet la personnalisation des widgets affichés

4. **DailyLoginTracker** - Suivi des connexions quotidiennes
   - Enregistre les connexions utilisateurs par jour
   - Fournit des méthodes pour obtenir les statistiques de connexion

5. **AdminActivityTracker** - Suivi des activités des administrateurs
   - Enregistre les statistiques d'équipes gérées par les admins
   - Fournit des méthodes pour calculer et récupérer les métriques d'activité

## 🔄 Logique d'importation des données

### Mécanisme de capture des métriques

1. **Capture en temps réel**
   - Les métriques sont calculées à la demande lors des appels API
   - Les calculs sont effectués directement sur les données MongoDB
   - Utilisation de fonctions sécurisées (safe_divide, safe_percentage, etc.) pour éviter les erreurs

2. **Sauvegarde des instantanés**
   - Méthode `_save_metrics_snapshot` dans `SuperAdminDashboardView`
   - Stocke périodiquement l'état des métriques pour analyse historique
   - Format: `BiMetricSnapshot` avec horodatage et type de tableau de bord

3. **Compatibilité avec l'ancien format**
   - Méthode `_save_legacy_metrics` dans `SuperAdminDashboardView`
   - Maintient la compatibilité avec le frontend existant
   - Utilise l'ancien modèle `BiMetric`

### Suivi des connexions utilisateurs

1. **Enregistrement des connexions**
   - Méthode `add_user_login` dans `DailyLoginTracker`
   - Appelée lors de chaque connexion utilisateur
   - Enregistre l'ID utilisateur et incrémente les compteurs

2. **Statistiques de connexion**
   - Méthodes `get_users_logged_today` et `get_total_logins_today`
   - Fournissent des données pour les tableaux de bord en temps réel

## 🌐 Routes API

### Routes principales BI

```
GET /api/bi/metrics/                  - Récupère les métriques BI selon le rôle
GET /api/bi/dashboard/                - Récupère/configure le tableau de bord de l'utilisateur
GET /api/bi/historical-data/          - Récupère les données historiques des métriques
GET /api/bi/super-admin/dashboard/    - Tableau de bord spécifique super admin
GET /api/bi/admin/dashboard/          - Tableau de bord spécifique admin
GET /api/bi/realtime/login-stats/     - Statistiques de connexion en temps réel
GET /api/dashboard/stats              - Route alternative pour compatibilité
```

### Routes de débogage

```
GET /api/bi/debug/login-data/         - Débogage des données de connexion
GET /api/bi/admin/debug/              - Débogage des activités admin
GET /api/debug/employees/             - Débogage des données employés
```

## 🔍 Détail des vues principales

### SuperAdminDashboardView

- **Endpoint**: `/api/bi/super-admin/dashboard/`
- **Méthode**: GET
- **Paramètres**:
  - `period`: '1h', '24h', '7d', '30d', 'today' (défaut)
  - `manual_refresh`: true/false (défaut: true)
- **Fonctionnalités**:
  - Calcule les métriques en temps réel pour le super admin
  - Fournit des statistiques sur tous les utilisateurs et leur activité
  - Sauvegarde périodiquement les instantanés pour l'historique

### BiDashboardView

- **Endpoint**: `/api/bi/dashboard/`
- **Méthodes**: GET, POST
- **Fonctionnalités**:
  - GET: Récupère la configuration du tableau de bord de l'utilisateur
  - POST: Met à jour la configuration (widgets, disposition)
  - Crée un tableau de bord par défaut si aucun n'existe

### RealTimeLoginStatsView

- **Endpoint**: `/api/bi/realtime/login-stats/`
- **Méthode**: GET
- **Fonctionnalités**:
  - Fournit des statistiques de connexion en temps réel
  - Calcule les tendances par rapport aux périodes précédentes
  - Utilisé pour les widgets de connexion dans les tableaux de bord

## 🔐 Sécurité et Permissions

- Toutes les routes BI nécessitent l'authentification (`IsAuthenticated`)
- Certaines routes requièrent des permissions spécifiques:
  - `super_admin_required` - Réservé aux super administrateurs
  - `admin_required` - Réservé aux administrateurs et super administrateurs

## 🛠️ Fonctions utilitaires

Le système utilise plusieurs fonctions utilitaires pour garantir la robustesse des calculs:

- `safe_divide(numerator, denominator, default=0)` - Division sécurisée évitant les divisions par zéro
- `safe_percentage(numerator, denominator, default=0.0)` - Calcul de pourcentage sécurisé
- `safe_round(value, decimals=2, default=0.0)` - Arrondi sécurisé évitant les valeurs NaN
- `safe_float(value, default=0.0)` - Conversion en float sécurisée
- `safe_int(value, default=0)` - Conversion en entier sécurisée

## 📈 Dernières modifications

### Améliorations récentes

1. **Nouveau modèle BiMetricSnapshot**
   - Remplace l'ancien système pour éviter les conflits d'index
   - Meilleure organisation des données historiques
   - Support pour différents types de périodes (daily, weekly, monthly)

2. **Optimisation des calculs**
   - Fonctions sécurisées pour éviter les erreurs de calcul
   - Meilleure gestion des valeurs nulles ou non numériques

3. **Amélioration du suivi des connexions**
   - Système plus robuste de tracking des connexions utilisateurs
   - Meilleure agrégation des statistiques par période

4. **Tableaux de bord personnalisables**
   - Support pour la personnalisation des widgets par utilisateur
   - Configuration sauvegardée dans BiDashboard

### Problèmes connus

1. **Compatibilité avec l'ancien format**
   - L'ancien modèle BiMetric est maintenu pour la compatibilité
   - Les index ont été supprimés pour éviter les erreurs

2. **Performance des requêtes agrégées**
   - Les calculs en temps réel peuvent être lents sur de grands volumes
   - Envisager l'utilisation de caching côté frontend (30 secondes recommandé)

## 🔌 Intégration avec le Frontend

### Format de réponse

Toutes les API renvoient des réponses JSON avec une structure cohérente:

```json
{
  "timestamp": "2025-05-28T14:42:09+01:00",
  "is_realtime": true,
  "metric_cards": [...],
  "charts": {...},
  "detailed_stats": {...},
  "metadata": {...}
}
```

### Recommandations d'intégration

1. **Rafraîchissement des données**
   - Utiliser un intervalle de 30 secondes pour les données en temps réel
   - Implémenter un cache côté client pour réduire la charge serveur

2. **Gestion des erreurs**
   - Vérifier les codes d'erreur HTTP (401, 403, 500)
   - Implémenter une logique de retry avec backoff exponentiel

3. **Authentification**
   - Utiliser le système JWT avec refresh token
   - Vérifier la validité du token avant chaque requête importante

## 📞 Support et Contact

Pour toute question concernant l'API ou l'importation des données, veuillez contacter l'équipe backend.

---

**Note**: Cette documentation est destinée à l'équipe frontend et contient les informations essentielles pour l'intégration avec le backend. Pour une documentation plus détaillée sur chaque endpoint, veuillez consulter la documentation API complète.
