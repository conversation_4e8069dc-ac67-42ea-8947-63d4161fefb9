from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from ..decorators import super_admin_required, admin_required
from ..mongo_models import User, Team
from ..models.event_model import Event
from ..models.personal_event_model import PersonalEvent
from ..models.team_task_model import TeamTask
from ..models.personal_task_model import PersonalTask
from ..models.pomodoro_model import PomodoroSettings
from ..models.bi_model import BiMetric, BiMetricSnapshot, BiDashboard, DailyLoginTracker, AdminActivityTracker
from datetime import datetime, timezone, timedelta
import logging
import math

def safe_divide(numerator, denominator, default=0):
    """
    Division sécurisée qui évite les divisions par zéro et les valeurs NaN
    """
    try:
        # S'assurer que les valeurs sont numériques
        numerator = float(numerator) if numerator is not None else 0
        denominator = float(denominator) if denominator is not None else 0

        if denominator == 0:
            return default
        result = numerator / denominator
        if math.isnan(result) or math.isinf(result):
            return default
        return float(result)  # Garantir un retour numérique
    except (TypeError, ValueError, ZeroDivisionError):
        return float(default)  # Garantir un retour numérique

def safe_percentage(numerator, denominator, default=0.0):
    """
    Calcul de pourcentage sécurisé qui évite les valeurs NaN
    """
    try:
        # S'assurer que les valeurs sont numériques
        numerator = float(numerator) if numerator is not None else 0.0
        denominator = float(denominator) if denominator is not None else 0.0

        if denominator == 0:
            return float(default)
        result = (numerator / denominator) * 100
        if math.isnan(result) or math.isinf(result):
            return float(default)
        return float(round(result, 2))  # Garantir un retour numérique
    except (TypeError, ValueError, ZeroDivisionError):
        return float(default)  # Garantir un retour numérique

def safe_round(value, decimals=2, default=0.0):
    """
    Arrondi sécurisé qui évite les valeurs NaN
    """
    try:
        # Vérifier si la valeur est une chaîne et tenter de la convertir
        if isinstance(value, str):
            # Nettoyer la chaîne pour enlever tout caractère non numérique sauf le point décimal
            cleaned_value = ''.join(c for c in value if c.isdigit() or c == '.')
            if cleaned_value:
                value = float(cleaned_value)
            else:
                return float(default)

        if value is None or math.isnan(float(value)) or math.isinf(float(value)):
            return float(default)

        return float(round(float(value), decimals))  # Garantir un retour numérique
    except (TypeError, ValueError):
        return float(default)  # Garantir un retour numérique

def safe_float(value, default=0.0):
    """
    Conversion en float sécurisée
    """
    try:
        # Vérifier si la valeur est une chaîne et tenter de la convertir
        if isinstance(value, str):
            # Nettoyer la chaîne pour enlever tout caractère non numérique sauf le point décimal
            cleaned_value = ''.join(c for c in value if c.isdigit() or c == '.')
            if cleaned_value:
                value = float(cleaned_value)
            else:
                return float(default)

        if value is None or (isinstance(value, float) and (math.isnan(value) or math.isinf(value))):
            return float(default)

        return float(value)  # Garantir un retour numérique float
    except (TypeError, ValueError):
        return float(default)  # Garantir un retour numérique float

def safe_int(value, default=0):
    """
    Conversion en entier sécurisée
    """
    try:
        # Vérifier si la valeur est une chaîne et tenter de la convertir
        if isinstance(value, str):
            # Nettoyer la chaîne pour enlever tout caractère non numérique sauf le point décimal
            cleaned_value = ''.join(c for c in value if c.isdigit() or c == '.')
            if cleaned_value:
                # Convertir d'abord en float puis en int pour gérer les décimales
                value = float(cleaned_value)
            else:
                return int(default)

        if value is None or (isinstance(value, float) and (math.isnan(value) or math.isinf(value))):
            return int(default)

        return int(float(value))  # Garantir un retour numérique entier
    except (TypeError, ValueError):
        return int(default)  # Garantir un retour numérique entier

logger = logging.getLogger(__name__)

class BiMetricsView(APIView):
    """
    Vue pour récupérer les métriques BI
    """
    permission_classes = [IsAuthenticated]  # Tous les utilisateurs authentifiés peuvent accéder

    def get(self, request):
        """
        Récupère les métriques BI en fonction du rôle de l'utilisateur
        """
        try:
            # Vérifier que l'utilisateur est bien authentifié
            if not request.user.is_authenticated:
                logger.warning("Tentative d'accès aux métriques BI sans authentification")
                return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

            user = request.user
            logger.info(f"Récupération des métriques BI pour: {user.email}, rôle: {user.role}")

            # Métriques pour super admin
            if user.role == 'super_admin':
                logger.info(f"Récupération des métriques super admin pour: {user.email}")
                return self._get_super_admin_metrics()

            # Métriques pour admin
            elif user.role == 'admin':
                logger.info(f"Récupération des métriques admin pour: {user.email}")
                return self._get_admin_metrics(user)

            # Métriques pour employé ou client
            else:
                logger.info(f"Récupération des métriques utilisateur pour: {user.email}, rôle: {user.role}")
                return self._get_user_metrics(user)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des métriques BI: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class SuperAdminDashboardView(APIView):
    """
    Vue spécialisée pour le tableau de bord en temps réel du super admin
    """
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        """
        Récupère les métriques pour le tableau de bord super admin avec filtrage par période
        Paramètres de requête:
        - period: '1h', '24h', '7d', '30d' (défaut: 'today')
        - manual_refresh: true/false (défaut: true)
        """
        try:
            # Récupérer les paramètres de requête
            period = request.query_params.get('period', 'today')
            manual_refresh = request.query_params.get('manual_refresh', 'true').lower() == 'true'

            logger.info(f"Récupération des métriques pour le super admin - Période: {period}, Mise à jour manuelle: {manual_refresh}")

            # Timestamp de la requête
            now = datetime.now(timezone.utc)

            # 1. NOMBRE TOTAL D'UTILISATEURS
            total_users = User.objects.count()
            logger.info(f"Nombre total d'utilisateurs: {total_users}")

            # 2. CALCUL DES PÉRIODES SELON LE FILTRE
            if period == '1h':
                period_start = now - timedelta(hours=1)
                period_name = 'Dernière heure'
                active_users_period = User.objects(last_login__gte=period_start).count()
                users_logged_period = active_users_period  # Pas de tracker pour 1h
                total_logins_period = active_users_period  # Approximation
            elif period == '24h':
                period_start = now - timedelta(days=1)
                period_name = 'Dernières 24h'
                active_users_period = User.objects(last_login__gte=period_start).count()
                users_logged_period = active_users_period
                total_logins_period = active_users_period  # Approximation
            elif period == '7d':
                period_start = now - timedelta(days=7)
                period_name = 'Derniers 7 jours'
                active_users_period = User.objects(last_login__gte=period_start).count()
                users_logged_period = active_users_period
                total_logins_period = active_users_period  # Approximation
            elif period == '30d':
                period_start = now - timedelta(days=30)
                period_name = 'Derniers 30 jours'
                active_users_period = User.objects(last_login__gte=period_start).count()
                users_logged_period = active_users_period
                total_logins_period = active_users_period  # Approximation
            else:  # 'today' par défaut
                period_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                period_name = 'Aujourd\'hui'
                users_logged_period = DailyLoginTracker.get_users_logged_today()
                total_logins_period = DailyLoginTracker.get_total_logins_today()
                active_users_period = users_logged_period

            # Utilisateurs inactifs pour la période sélectionnée
            inactive_users_period = total_users - active_users_period

            # Calculs pour toutes les périodes (pour les statistiques détaillées)
            thirty_days_ago = now - timedelta(days=30)
            seven_days_ago = now - timedelta(days=7)
            one_day_ago = now - timedelta(days=1)

            # Utilisateurs connectés aujourd'hui (données en temps réel)
            users_logged_today = DailyLoginTracker.get_users_logged_today()
            total_logins_today = DailyLoginTracker.get_total_logins_today()

            # Utilisateurs actifs (connectés dans les différentes périodes)
            active_users_30d = User.objects(last_login__gte=thirty_days_ago).count()
            active_users_7d = User.objects(last_login__gte=seven_days_ago).count()
            active_users_1d = User.objects(last_login__gte=one_day_ago).count()

            # Utilisateurs inactifs (basé sur les connexions d'aujourd'hui)
            inactive_users_today = total_users - users_logged_today
            inactive_users = total_users - active_users_30d

            # Utilisateurs qui ne se sont jamais connectés
            never_logged_in = User.objects(last_login__exists=False).count()

            logger.info(f"Utilisateurs connectés aujourd'hui: {users_logged_today}, Total connexions: {total_logins_today}")
            logger.info(f"Utilisateurs actifs (30j): {active_users_30d}, Inactifs aujourd'hui: {inactive_users_today}")

            # 3. DISTRIBUTION DES UTILISATEURS PAR RÔLE
            users_by_role = {}
            roles = ['super_admin', 'admin', 'employee', 'client']

            for role in roles:
                count = User.objects(role=role).count()
                users_by_role[role] = count
                logger.info(f"Utilisateurs {role}: {count}")

            # 4. STATISTIQUES D'ACTIVITÉ DÉTAILLÉES EN TEMPS RÉEL
            activity_stats = {
                'total_users': total_users,
                'users_logged_today': users_logged_today,  # Utilisateurs uniques connectés aujourd'hui (DailyTracker)
                'total_logins_today': total_logins_today,  # Total des connexions aujourd'hui (peut être > users_logged_today)
                'inactive_users_today': inactive_users_today,  # Non connectés aujourd'hui
                'active_users': {
                    'today': users_logged_today,  # Même source que users_logged_today pour cohérence
                    'last_24h': active_users_1d,  # Basé sur last_login (peut différer de today)
                    'last_7_days': active_users_7d,
                    'last_30_days': active_users_30d
                },
                'inactive_users': inactive_users,  # Inactifs sur 30 jours
                'never_logged_in': never_logged_in,
                'activity_rate': {
                    'today': safe_percentage(users_logged_today, total_users),
                    'last_24h': safe_percentage(active_users_1d, total_users),
                    'last_7_days': safe_percentage(active_users_7d, total_users),
                    'last_30_days': safe_percentage(active_users_30d, total_users)
                },
                'data_sources': {
                    'users_logged_today': 'DailyLoginTracker',
                    'total_logins_today': 'DailyLoginTracker',
                    'active_users_periods': 'User.last_login'
                }
            }

            # 5. CALCULS RÉELS DES TENDANCES depuis la base de données
            # Calculer les tendances mensuelles et hebdomadaires réelles

            # Période de comparaison pour les tendances
            one_month_ago = now - timedelta(days=30)
            two_months_ago = now - timedelta(days=60)
            two_weeks_ago = now - timedelta(days=14)

            # Tendance mensuelle du nombre total d'utilisateurs
            users_last_month = User.objects(created_at__lt=one_month_ago).count()
            users_this_month = total_users - users_last_month
            if users_last_month > 0:
                total_users_trend_percent = round(((users_this_month / users_last_month) * 100), 1)
                total_users_trend = f"+{total_users_trend_percent}%" if total_users_trend_percent > 0 else f"{total_users_trend_percent}%"
            else:
                total_users_trend = "+100%"  # Tous les utilisateurs sont nouveaux

            # Tendance hebdomadaire des utilisateurs actifs
            active_users_last_week = User.objects(last_login__gte=two_weeks_ago, last_login__lt=seven_days_ago).count()
            if active_users_last_week > 0:
                active_trend_percent = round((((active_users_7d - active_users_last_week) / active_users_last_week) * 100), 1)
                active_users_trend = f"+{active_trend_percent}%" if active_trend_percent > 0 else f"{active_trend_percent}%"
            else:
                active_users_trend = "+100%" if active_users_7d > 0 else "0%"

            # Tendance mensuelle des utilisateurs inactifs
            inactive_users_last_month = User.objects(created_at__lt=one_month_ago).count() - User.objects(last_login__gte=one_month_ago, created_at__lt=one_month_ago).count()
            if inactive_users_last_month > 0:
                inactive_trend_percent = round((((inactive_users - inactive_users_last_month) / inactive_users_last_month) * 100), 1)
                inactive_users_trend = f"+{inactive_trend_percent}%" if inactive_trend_percent > 0 else f"{inactive_trend_percent}%"
            else:
                inactive_users_trend = "0%"

            # 6. DONNÉES POUR LES GRAPHIQUES FILTRÉES PAR PÉRIODE
            # Graphique en anneau : Utilisateurs Actifs vs Inactifs pour la période sélectionnée
            active_vs_inactive_chart = {
                'type': 'doughnut',
                'title': f'Connexions - {period_name}',
                'subtitle': f'Utilisateurs connectés vs non connectés ({period_name.lower()})',
                'data': [
                    {'name': f'Connectés ({period_name.lower()})', 'value': users_logged_period, 'color': '#10B981'},
                    {'name': f'Non connectés ({period_name.lower()})', 'value': inactive_users_period, 'color': '#EF4444'}
                ],
                'legend': [
                    {'label': f'Connectés ({period_name.lower()})', 'color': '#10B981'},
                    {'label': f'Non connectés ({period_name.lower()})', 'color': '#EF4444'}
                ],
                'period': period,
                'period_name': period_name,
                'manual_refresh': manual_refresh,
                'last_updated': now.isoformat()
            }

            # Graphique en barres : Distribution par rôle (droite)
            role_distribution_chart = {
                'type': 'bar',
                'title': 'Distribution des Utilisateurs par Rôle',
                'data': [
                    {'name': 'Super Admin', 'value': users_by_role['super_admin'], 'color': '#8B5CF6'},
                    {'name': 'Admin', 'value': users_by_role['admin'], 'color': '#3B82F6'},
                    {'name': 'Employés', 'value': users_by_role['employee'], 'color': '#10B981'},
                    {'name': 'Clients', 'value': users_by_role['client'], 'color': '#F59E0B'}
                ],
                'max_value': max(users_by_role.values()) if users_by_role.values() else 1000
            }

            # 6. MÉTRIQUES SUPPLÉMENTAIRES RÉELLES
            # Nouveaux utilisateurs (créés dans les 7 derniers jours)
            new_users_7d = User.objects(created_at__gte=seven_days_ago).count()
            new_users_30d = User.objects(created_at__gte=thirty_days_ago).count()

            # Utilisateurs par permissions avec pourcentages réels
            users_with_permissions = {}
            for role in roles:
                users_with_permissions[role] = {
                    'count': users_by_role[role],
                    'percentage': round((users_by_role[role] / total_users) * 100, 2) if total_users > 0 else 0
                }

            # Statistiques d'engagement réelles (utiliser les données du tracker pour cohérence)
            users_never_logged = User.objects(last_login__exists=False).count()

            # Vérification de cohérence des données
            logger.info(f"Vérification cohérence - DailyTracker: {users_logged_today}, last_login: {User.objects(last_login__gte=one_day_ago).count()}")

            # Calcul du taux de rétention (utilisateurs actifs sur total)
            retention_rate = round((active_users_30d / total_users) * 100, 2) if total_users > 0 else 0

            logger.info(f"Métriques calculées - Total: {total_users}, Actifs: {active_users_30d}, Nouveaux (7j): {new_users_7d}, Rétention: {retention_rate}%")

            # 7. STRUCTURE DE RÉPONSE SELON LA MAQUETTE
            response_data = {
                'timestamp': now.isoformat(),
                'is_realtime': True,

                # Cartes de métriques principales FILTRÉES PAR PÉRIODE (top de la maquette)
                'metric_cards': [
                    {
                        'title': 'Nombre total d\'utilisateurs',
                        'value': total_users,
                        'trend': total_users_trend,
                        'trend_period': 'ce mois',
                        'icon': 'users',
                        'color': '#3B82F6',  # Bleu
                        'manual_refresh': manual_refresh
                    },
                    {
                        'title': 'Utilisateurs actifs',
                        'subtitle': f'Connectés ({period_name.lower()})',
                        'value': users_logged_period,
                        'trend': f"+{round(((users_logged_period / total_users) * 100), 1)}%" if total_users > 0 else "0%",
                        'trend_period': period_name.lower(),
                        'icon': 'user-check',
                        'color': '#10B981',  # Vert
                        'manual_refresh': manual_refresh,
                        'last_updated': now.isoformat(),
                        'period': period,
                        'data_source': 'DailyLoginTracker' if period == 'today' else 'User.last_login'
                    },
                    {
                        'title': 'Utilisateurs inactifs',
                        'subtitle': f'Non connectés ({period_name.lower()})',
                        'value': inactive_users_period,
                        'trend': f"{round(((inactive_users_period / total_users) * 100), 1)}%" if total_users > 0 else "0%",
                        'trend_period': period_name.lower(),
                        'icon': 'user-x',
                        'color': '#EF4444',  # Rouge
                        'manual_refresh': manual_refresh,
                        'last_updated': now.isoformat(),
                        'period': period,
                        'data_source': 'DailyLoginTracker' if period == 'today' else 'User.last_login'
                    }
                ],

                # Graphiques (bottom de la maquette)
                'charts': {
                    'active_vs_inactive': active_vs_inactive_chart,  # Graphique gauche
                    'role_distribution': role_distribution_chart     # Graphique droite
                },

                # Données détaillées pour référence (toutes calculées depuis la base)
                'detailed_stats': {
                    'users_by_role': users_by_role,
                    'activity_stats': activity_stats,
                    'users_with_permissions': users_with_permissions,
                    'engagement_metrics': {
                        'new_users_7d': new_users_7d,
                        'new_users_30d': new_users_30d,
                        'users_logged_today': users_logged_today,
                        'users_never_logged': users_never_logged,
                        'retention_rate': retention_rate
                    },
                    'trends': {
                        'total_users_trend': total_users_trend,
                        'active_users_trend': active_users_trend,
                        'inactive_users_trend': inactive_users_trend
                    }
                },

                # Métadonnées AVEC CONTRÔLE MANUEL
                'metadata': {
                    'last_updated': now.isoformat(),
                    'data_source': 'DailyLoginTracker' if period == 'today' else 'User.last_login',
                    'refresh_mode': 'manual' if manual_refresh else 'automatic',
                    'refresh_interval': None if manual_refresh else 30,
                    'dashboard_title': f'Tableau de Bord Super Admin - {period_name}',
                    'dashboard_subtitle': f'Connexions et analyses ({period_name.lower()})',
                    'current_period': {
                        'period': period,
                        'period_name': period_name,
                        'period_start': period_start.isoformat(),
                        'manual_refresh': manual_refresh
                    },
                    'available_periods': [
                        {'value': 'today', 'label': 'Aujourd\'hui'},
                        {'value': '1h', 'label': 'Dernière heure'},
                        {'value': '24h', 'label': 'Dernières 24h'},
                        {'value': '7d', 'label': 'Derniers 7 jours'},
                        {'value': '30d', 'label': 'Derniers 30 jours'}
                    ],
                    'features': {
                        'period_filtering': True,
                        'manual_refresh': manual_refresh,
                        'real_time_data': period == 'today'
                    },
                    'data_freshness': {
                        'login_data': 'real_time' if period == 'today' else 'database_query',
                        'user_counts': 'real_time',
                        'activity_stats': 'real_time'
                    }
                }
            }

            logger.info("Métriques super admin récupérées avec succès")
            return Response(response_data)

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des métriques super admin: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_super_admin_metrics(self):
        """
        Récupère les métriques pour les super admins en temps réel
        """
        try:
            # Forcer la mise à jour des métriques d'activité des utilisateurs
            from ..tasks.bi_tasks import update_user_activity_metrics
            update_user_activity_metrics()

            # Nombre total d'utilisateurs
            total_users = User.objects.count()

            # Log pour le débogage
            logger.info(f"Récupération des métriques en temps réel - Nombre total d'utilisateurs: {total_users}")

            # Définir différentes périodes pour l'analyse d'activité
            now = datetime.now(timezone.utc)
            one_day_ago = now - timedelta(days=1)
            seven_days_ago = now - timedelta(days=7)
            thirty_days_ago = now - timedelta(days=30)
            ninety_days_ago = now - timedelta(days=90)

            # Nombre d'utilisateurs actifs (connectés au cours des dernières périodes)
            active_users_24h = User.objects(last_login__gte=one_day_ago).count()
            active_users_7d = User.objects(last_login__gte=seven_days_ago).count()
            active_users_30d = User.objects(last_login__gte=thirty_days_ago).count()
            active_users_90d = User.objects(last_login__gte=ninety_days_ago).count()

            # Log pour le débogage
            logger.info(f"Utilisateurs actifs en temps réel - 24h: {active_users_24h}, 7j: {active_users_7d}, 30j: {active_users_30d}, 90j: {active_users_90d}")

            # Calcul des pourcentages d'utilisateurs actifs
            active_percentage_24h = round((active_users_24h / total_users) * 100, 1) if total_users > 0 else 0.0
            active_percentage_7d = round((active_users_7d / total_users) * 100, 1) if total_users > 0 else 0.0
            active_percentage_30d = round((active_users_30d / total_users) * 100, 1) if total_users > 0 else 0.0
            active_percentage_90d = round((active_users_90d / total_users) * 100, 1) if total_users > 0 else 0.0

            # Répartition des utilisateurs par rôle - Récupération en temps réel
            # Forcer le comptage direct depuis la base de données pour garantir l'exactitude
            users_by_role = {
                'super_admin': User.objects(role='super_admin').count(),
                'admin': User.objects(role='admin').count(),
                'employee': User.objects(role='employee').count(),
                'client': User.objects(role='client').count()
            }

            # Log pour le débogage
            logger.info(f"Répartition des utilisateurs par rôle en temps réel - Super Admin: {users_by_role['super_admin']}, Admin: {users_by_role['admin']}, Employé: {users_by_role['employee']}, Client: {users_by_role['client']}")

            # Analyse de l'activité par rôle (taux d'utilisation des permissions)
            active_by_role_24h = {
                'super_admin': User.objects(role='super_admin', last_login__gte=one_day_ago).count(),
                'admin': User.objects(role='admin', last_login__gte=one_day_ago).count(),
                'employee': User.objects(role='employee', last_login__gte=one_day_ago).count(),
                'client': User.objects(role='client', last_login__gte=one_day_ago).count()
            }

            active_by_role_7d = {
                'super_admin': User.objects(role='super_admin', last_login__gte=seven_days_ago).count(),
                'admin': User.objects(role='admin', last_login__gte=seven_days_ago).count(),
                'employee': User.objects(role='employee', last_login__gte=seven_days_ago).count(),
                'client': User.objects(role='client', last_login__gte=seven_days_ago).count()
            }

            active_by_role_30d = {
                'super_admin': User.objects(role='super_admin', last_login__gte=thirty_days_ago).count(),
                'admin': User.objects(role='admin', last_login__gte=thirty_days_ago).count(),
                'employee': User.objects(role='employee', last_login__gte=thirty_days_ago).count(),
                'client': User.objects(role='client', last_login__gte=thirty_days_ago).count()
            }

            # Calculer les taux d'activité par rôle (pourcentage d'utilisateurs actifs par rôle)
            activity_rate_by_role = {}
            for role in ['super_admin', 'admin', 'employee', 'client']:
                total_role = users_by_role[role]
                if total_role > 0:
                    activity_rate_by_role[role] = {
                        '24h': round((active_by_role_24h[role] / total_role) * 100, 1),
                        '7d': round((active_by_role_7d[role] / total_role) * 100, 1),
                        '30d': round((active_by_role_30d[role] / total_role) * 100, 1)
                    }
                else:
                    activity_rate_by_role[role] = {'24h': 0, '7d': 0, '30d': 0}

            # Log pour le débogage des taux d'activité
            logger.info(f"Taux d'activité par rôle (7j) en temps réel - Super Admin: {activity_rate_by_role['super_admin']['7d']}%, Admin: {activity_rate_by_role['admin']['7d']}%, Employé: {activity_rate_by_role['employee']['7d']}%, Client: {activity_rate_by_role['client']['7d']}%")

            # Nombre d'équipes
            total_teams = Team.objects.count()

            # Nombre d'événements et tâches
            total_events = Event.objects.count()
            total_personal_events = PersonalEvent.objects.count()
            total_team_tasks = TeamTask.objects.count()
            total_personal_tasks = PersonalTask.objects.count()

            # Log pour le débogage
            logger.info(f"Statistiques de la plateforme en temps réel - Équipes: {total_teams}, Événements: {total_events}, Événements personnels: {total_personal_events}, Tâches d'équipe: {total_team_tasks}, Tâches personnelles: {total_personal_tasks}")

            # Métriques d'activité
            metrics = {
                'user_activity': {
                    'total_users': total_users,
                    'active_users': {
                        '24h': active_users_24h,
                        '7d': active_users_7d,
                        '30d': active_users_30d,
                        '90d': active_users_90d
                    },
                    'active_percentage': {
                        '24h': active_percentage_24h,
                        '7d': active_percentage_7d,
                        '30d': active_percentage_30d,
                        '90d': active_percentage_90d
                    },
                    'users_by_role': users_by_role,
                    'active_by_role': {
                        '24h': active_by_role_24h,
                        '7d': active_by_role_7d,
                        '30d': active_by_role_30d
                    },
                    'activity_rate_by_role': activity_rate_by_role
                },
                'platform_activity': {
                    'total_teams': total_teams,
                    'total_events': total_events,
                    'total_personal_events': total_personal_events,
                    'total_team_tasks': total_team_tasks,
                    'total_personal_tasks': total_personal_tasks
                },
                'timestamp': now.isoformat(),  # Ajouter un horodatage pour indiquer quand les données ont été récupérées
                'is_realtime': True  # Indicateur que les données sont en temps réel
            }

            # Enregistrer les métriques dans la base de données pour le suivi historique
            self._save_metrics_snapshot('super_admin', metrics)

            # Créer également une entrée dans l'ancien format pour la compatibilité avec le frontend
            self._save_legacy_metrics(metrics)

            return Response(metrics)
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des métriques super admin en temps réel: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _save_metrics_snapshot(self, dashboard_type, metrics):
        """
        Sauvegarde un instantané des métriques pour le suivi historique
        Utilise le nouveau modèle BiMetricSnapshot pour éviter les conflits
        """
        try:
            now = datetime.now(timezone.utc)

            # Créer un instantané en temps réel
            real_time_metric = BiMetricSnapshot(
                dashboard_type=dashboard_type,
                metrics=metrics,
                timestamp=now,
                metric_type="real_time"
            )
            real_time_metric.save()

            # Vérifier si nous avons déjà un instantané quotidien pour aujourd'hui
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)

            daily_exists = BiMetricSnapshot.objects(
                dashboard_type=dashboard_type,
                metric_type="daily",
                timestamp__gte=today_start,
                timestamp__lt=today_end
            ).count() > 0

            # Si nous n'avons pas encore d'instantané quotidien pour aujourd'hui, en créer un
            if not daily_exists:
                daily_metric = BiMetricSnapshot(
                    dashboard_type=dashboard_type,
                    metrics=metrics,
                    timestamp=now,
                    metric_type="daily",
                    period_start=today_start,
                    period_end=today_end
                )
                daily_metric.save()
                logger.info(f"Instantané quotidien des métriques {dashboard_type} sauvegardé avec succès")

            # Vérifier si nous avons besoin de créer un instantané hebdomadaire
            # (le premier jour de la semaine ou si aucun instantané hebdomadaire n'existe pour cette semaine)
            week_start = today_start - timedelta(days=today_start.weekday())
            week_end = week_start + timedelta(days=7)

            weekly_exists = BiMetricSnapshot.objects(
                dashboard_type=dashboard_type,
                metric_type="weekly",
                period_start=week_start,
                period_end=week_end
            ).count() > 0

            if not weekly_exists:
                weekly_metric = BiMetricSnapshot(
                    dashboard_type=dashboard_type,
                    metrics=metrics,
                    timestamp=now,
                    metric_type="weekly",
                    period_start=week_start,
                    period_end=week_end
                )
                weekly_metric.save()
                logger.info(f"Instantané hebdomadaire des métriques {dashboard_type} sauvegardé avec succès")

            # Vérifier si nous avons besoin de créer un instantané mensuel
            month_start = today_start.replace(day=1)
            if month_start.month == 12:
                month_end = month_start.replace(year=month_start.year + 1, month=1)
            else:
                month_end = month_start.replace(month=month_start.month + 1)

            monthly_exists = BiMetricSnapshot.objects(
                dashboard_type=dashboard_type,
                metric_type="monthly",
                period_start=month_start,
                period_end=month_end
            ).count() > 0

            if not monthly_exists:
                monthly_metric = BiMetricSnapshot(
                    dashboard_type=dashboard_type,
                    metrics=metrics,
                    timestamp=now,
                    metric_type="monthly",
                    period_start=month_start,
                    period_end=month_end
                )
                monthly_metric.save()
                logger.info(f"Instantané mensuel des métriques {dashboard_type} sauvegardé avec succès")

            logger.info(f"Instantané en temps réel des métriques {dashboard_type} sauvegardé avec succès")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des métriques {dashboard_type}: {str(e)}")
            # Ne pas lever d'exception pour ne pas perturber la réponse API

    def _save_legacy_metrics(self, metrics):
        """
        Sauvegarde les métriques dans l'ancien format pour la compatibilité avec le frontend
        """
        try:
            # Extraire les données d'activité des utilisateurs
            user_activity = metrics['user_activity']

            # Créer un nouvel enregistrement de métriques avec l'ancien modèle
            metric = BiMetric(
                metric_type='user_activity',
                data={
                    'total_users': user_activity['total_users'],
                    'active_users': user_activity['active_users'],
                    'active_percentage': user_activity['active_percentage'],
                    'users_by_role': user_activity['users_by_role']
                }
            )
            metric.save()
            logger.info("Métriques d'activité des utilisateurs sauvegardées dans l'ancien format pour compatibilité")

            # Ne pas sauvegarder les métriques de plateforme car ce n'est pas un type valide
            # dans le modèle BiMetric (voir les choix dans le modèle)
            logger.info("Métriques d'activité de la plateforme non sauvegardées dans l'ancien format")

        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des métriques dans l'ancien format: {str(e)}")
            # Ne pas lever d'exception pour ne pas perturber la réponse API

    def _get_admin_metrics(self, user):
        """
        Récupère les métriques pour les admins
        """
        # Récupérer les équipes dont l'utilisateur est responsable
        teams = Team.objects(responsable=str(user.id))

        # Métriques pour chaque équipe
        team_metrics = []

        for team in teams:
            team_id = str(team.id)

            # Tâches d'équipe
            team_tasks = TeamTask.objects(team_id=team_id)
            total_tasks = team_tasks.count()

            # Répartition des tâches par statut
            tasks_by_status = {
                'a_faire': team_tasks.filter(status='a_faire').count(),
                'en_cours': team_tasks.filter(status='en_cours').count(),
                'en_revision': team_tasks.filter(status='en_revision').count(),
                'achevee': team_tasks.filter(status='achevee').count(),
                'archived': team_tasks.filter(status='archived').count()
            }

            # Événements d'équipe
            team_events = Event.objects(team_id=team_id)
            total_events = team_events.count()

            # Répartition des événements par statut
            events_by_status = {
                'pending': team_events.filter(status='pending').count(),
                'completed': team_events.filter(status='completed').count(),
                'archived': team_events.filter(status='archived').count()
            }

            # Métriques de l'équipe
            team_metrics.append({
                'team_id': team_id,
                'team_name': team.name,
                'members_count': len(team.members),
                'tasks': {
                    'total': total_tasks,
                    'by_status': tasks_by_status,
                    'completion_rate': round((tasks_by_status['achevee'] / total_tasks) * 100, 2) if total_tasks > 0 else 0
                },
                'events': {
                    'total': total_events,
                    'by_status': events_by_status,
                    'completion_rate': round((events_by_status['completed'] / total_events) * 100, 2) if total_events > 0 else 0
                }
            })

        return Response({
            'teams_count': len(team_metrics),
            'teams': team_metrics
        })

    def _get_user_metrics(self, user):
        """
        Récupère les métriques pour les employés et clients
        """
        user_id = str(user.id)

        # Tâches personnelles
        personal_tasks = PersonalTask.objects(created_by=user_id)
        total_personal_tasks = personal_tasks.count()

        # Répartition des tâches personnelles par statut
        personal_tasks_by_status = {
            'a_faire': personal_tasks.filter(status='a_faire').count(),
            'en_cours': personal_tasks.filter(status='en_cours').count(),
            'en_revision': personal_tasks.filter(status='en_revision').count(),
            'achevee': personal_tasks.filter(status='achevee').count(),
            'archived': personal_tasks.filter(status='archived').count()
        }

        # Événements personnels
        personal_events = PersonalEvent.objects(created_by=user_id)
        total_personal_events = personal_events.count()

        # Répartition des événements personnels par statut
        personal_events_by_status = {
            'pending': personal_events.filter(status='pending').count(),
            'completed': personal_events.filter(status='completed').count(),
            'archived': personal_events.filter(status='archived').count()
        }

        # Métriques Pomodoro
        try:
            pomodoro_settings = PomodoroSettings.objects.get(user_id=user_id)
            pomodoro_sessions = len(pomodoro_settings.session_history) if pomodoro_settings.session_history else 0
        except PomodoroSettings.DoesNotExist:
            pomodoro_sessions = 0

        # Métriques de l'utilisateur
        metrics = {
            'personal_tasks': {
                'total': total_personal_tasks,
                'by_status': personal_tasks_by_status,
                'completion_rate': round((personal_tasks_by_status['achevee'] / total_personal_tasks) * 100, 2) if total_personal_tasks > 0 else 0
            },
            'personal_events': {
                'total': total_personal_events,
                'by_status': personal_events_by_status,
                'completion_rate': round((personal_events_by_status['completed'] / total_personal_events) * 100, 2) if total_personal_events > 0 else 0
            },
            'pomodoro': {
                'total_sessions': pomodoro_sessions
            }
        }

        # Si l'utilisateur est un employé, ajouter les métriques des équipes
        if user.role == 'employee':
            # Récupérer les équipes dont l'utilisateur est membre
            teams = Team.objects(members=user_id)

            # Tâches d'équipe assignées à l'utilisateur
            team_tasks = TeamTask.objects(member_id=user_id)
            total_team_tasks = team_tasks.count()

            # Répartition des tâches d'équipe par statut
            team_tasks_by_status = {
                'a_faire': team_tasks.filter(status='a_faire').count(),
                'en_cours': team_tasks.filter(status='en_cours').count(),
                'en_revision': team_tasks.filter(status='en_revision').count(),
                'achevee': team_tasks.filter(status='achevee').count(),
                'archived': team_tasks.filter(status='archived').count()
            }

            # Événements d'équipe assignés à l'utilisateur
            team_events = Event.objects(member_id=user_id)
            total_team_events = team_events.count()

            # Répartition des événements d'équipe par statut
            team_events_by_status = {
                'pending': team_events.filter(status='pending').count(),
                'completed': team_events.filter(status='completed').count(),
                'archived': team_events.filter(status='archived').count()
            }

            # Ajouter les métriques d'équipe
            metrics['team_tasks'] = {
                'total': total_team_tasks,
                'by_status': team_tasks_by_status,
                'completion_rate': round((team_tasks_by_status['achevee'] / total_team_tasks) * 100, 2) if total_team_tasks > 0 else 0
            }
            metrics['team_events'] = {
                'total': total_team_events,
                'by_status': team_events_by_status,
                'completion_rate': round((team_events_by_status['completed'] / total_team_events) * 100, 2) if total_team_events > 0 else 0
            }
            metrics['teams_count'] = len(teams)

        return Response(metrics)

class EmployeeDashboardView(APIView):
    """
    Vue spécialisée pour le tableau de bord en temps réel des employés
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Récupère les métriques pour le tableau de bord employé avec filtrage par période
        Paramètres de requête:
        - period: '1h', '24h', '7d', '30d' (défaut: 'today')
        - manual_refresh: true/false (défaut: true)
        """
        try:
            # Vérifier que l'utilisateur est un employé ou client
            user = request.user
            if user.role not in ['employee', 'client']:
                return Response({"error": "Accès non autorisé"}, status=403)

            # Récupérer les paramètres de requête
            period = request.query_params.get('period', 'today')
            manual_refresh = request.query_params.get('manual_refresh', 'true').lower() == 'true'

            logger.info(f"Récupération des métriques pour l'employé/client {user.email} - Période: {period}, Mise à jour manuelle: {manual_refresh}")

            # Timestamp de la requête
            now = datetime.now(timezone.utc)
            user_id = str(user.id)

            # 1. MÉTRIQUES DES TÂCHES PERSONNELLES
            personal_tasks = PersonalTask.objects(created_by=user_id)
            total_personal_tasks = personal_tasks.count()

            personal_tasks_by_status = {
                'a_faire': personal_tasks.filter(status='a_faire').count(),
                'en_cours': personal_tasks.filter(status='en_cours').count(),
                'en_revision': personal_tasks.filter(status='en_revision').count(),
                'achevee': personal_tasks.filter(status='achevee').count(),
                'archived': personal_tasks.filter(status='archived').count()
            }

            # 2. MÉTRIQUES DES ÉVÉNEMENTS PERSONNELS
            personal_events = PersonalEvent.objects(created_by=user_id)
            total_personal_events = personal_events.count()

            personal_events_by_status = {
                'pending': personal_events.filter(status='pending').count(),
                'completed': personal_events.filter(status='completed').count(),
                'archived': personal_events.filter(status='archived').count()
            }

            # 3. MÉTRIQUES DES TÂCHES D'ÉQUIPE (pour les employés uniquement)
            team_tasks_metrics = {}
            team_events_metrics = {}
            teams_count = 0

            if user.role == 'employee':
                # Récupérer les équipes de l'employé
                user_teams = Team.objects(members__contains=user_id)
                teams_count = user_teams.count()

                # Tâches d'équipe assignées à l'employé
                team_tasks = TeamTask.objects(member_id=user_id)
                total_team_tasks = team_tasks.count()

                team_tasks_by_status = {
                    'a_faire': team_tasks.filter(status='a_faire').count(),
                    'en_cours': team_tasks.filter(status='en_cours').count(),
                    'en_revision': team_tasks.filter(status='en_revision').count(),
                    'achevee': team_tasks.filter(status='achevee').count(),
                    'archived': team_tasks.filter(status='archived').count()
                }

                team_tasks_metrics = {
                    'total': total_team_tasks,
                    'by_status': team_tasks_by_status,
                    'completion_rate': safe_percentage(team_tasks_by_status['achevee'], total_team_tasks)
                }

                # Événements d'équipe assignés à l'employé
                team_events = Event.objects(member_id=user_id)
                total_team_events = team_events.count()

                team_events_by_status = {
                    'pending': team_events.filter(status='pending').count(),
                    'completed': team_events.filter(status='completed').count(),
                    'archived': team_events.filter(status='archived').count()
                }

                team_events_metrics = {
                    'total': total_team_events,
                    'by_status': team_events_by_status,
                    'completion_rate': safe_percentage(team_events_by_status['completed'], total_team_events)
                }

            # 4. GRAPHIQUES EN SECTEURS (PIE CHARTS)

            # Graphique 1: Distribution des tâches personnelles par statut
            personal_tasks_chart = {
                'type': 'pie',
                'title': 'Distribution des tâches personnelles par statut',
                'data': [
                    {'name': 'À faire', 'value': personal_tasks_by_status['a_faire'], 'color': '#3B82F6'},  # Bleu clair
                    {'name': 'En cours', 'value': personal_tasks_by_status['en_cours'], 'color': '#F59E0B'},  # Jaune
                    {'name': 'Terminées', 'value': personal_tasks_by_status['achevee'], 'color': '#10B981'},  # Vert foncé
                ],
                'legend': [
                    {'label': 'À faire', 'color': '#3B82F6'},
                    {'label': 'En cours', 'color': '#F59E0B'},
                    {'label': 'Terminées', 'color': '#10B981'}
                ],
                'total': total_personal_tasks,
                'completion_rate': safe_percentage(personal_tasks_by_status['achevee'], total_personal_tasks)
            }

            # Graphique 2: Distribution des événements personnels par statut
            personal_events_chart = {
                'type': 'pie',
                'title': 'Distribution des événements personnels par statut',
                'data': [
                    {'name': 'À faire', 'value': personal_events_by_status['pending'], 'color': '#3B82F6'},  # Bleu clair
                    {'name': 'En cours', 'value': 0, 'color': '#F59E0B'},  # Jaune (événements n'ont pas de statut "en cours")
                    {'name': 'Terminés', 'value': personal_events_by_status['completed'], 'color': '#10B981'},  # Vert foncé
                ],
                'legend': [
                    {'label': 'À faire', 'color': '#3B82F6'},
                    {'label': 'En cours', 'color': '#F59E0B'},
                    {'label': 'Terminés', 'color': '#10B981'}
                ],
                'total': total_personal_events,
                'completion_rate': safe_percentage(personal_events_by_status['completed'], total_personal_events)
            }

            # Graphiques pour les employés (tâches et événements d'équipe)
            team_tasks_chart = {}
            team_events_chart = {}

            if user.role == 'employee' and team_tasks_metrics:
                # Graphique 3: Distribution des tâches d'équipe par statut
                team_tasks_chart = {
                    'type': 'pie',
                    'title': 'Distribution des tâches d\'équipe par statut',
                    'data': [
                        {'name': 'À faire', 'value': team_tasks_by_status['a_faire'], 'color': '#3B82F6'},  # Bleu clair
                        {'name': 'En cours', 'value': team_tasks_by_status['en_cours'], 'color': '#F59E0B'},  # Jaune
                        {'name': 'Terminées', 'value': team_tasks_by_status['achevee'], 'color': '#10B981'},  # Vert foncé
                    ],
                    'legend': [
                        {'label': 'À faire', 'color': '#3B82F6'},
                        {'label': 'En cours', 'color': '#F59E0B'},
                        {'label': 'Terminées', 'color': '#10B981'}
                    ],
                    'total': team_tasks_metrics['total'],
                    'completion_rate': team_tasks_metrics['completion_rate']
                }

                # Graphique 4: Distribution des événements d'équipe par statut
                team_events_chart = {
                    'type': 'pie',
                    'title': 'Distribution des événements d\'équipe par statut',
                    'data': [
                        {'name': 'À faire', 'value': team_events_by_status['pending'], 'color': '#3B82F6'},  # Bleu clair
                        {'name': 'En cours', 'value': 0, 'color': '#F59E0B'},  # Jaune
                        {'name': 'Terminés', 'value': team_events_by_status['completed'], 'color': '#10B981'},  # Vert foncé
                    ],
                    'legend': [
                        {'label': 'À faire', 'color': '#3B82F6'},
                        {'label': 'En cours', 'color': '#F59E0B'},
                        {'label': 'Terminés', 'color': '#10B981'}
                    ],
                    'total': team_events_metrics['total'],
                    'completion_rate': team_events_metrics['completion_rate']
                }

            # 5. STRUCTURE DE RÉPONSE POUR LE DASHBOARD EMPLOYÉ
            response_data = {
                'timestamp': now.isoformat(),
                'is_realtime': True,
                'user_info': {
                    'user_id': user_id,
                    'user_name': user.name,
                    'user_role': user.role,
                    'teams_count': teams_count
                },

                # Cartes de métriques principales
                'metric_cards': [
                    {
                        'title': 'Tâches personnelles',
                        'value': total_personal_tasks,
                        'completion_rate': safe_percentage(personal_tasks_by_status['achevee'], total_personal_tasks),
                        'icon': 'clipboard-list',
                        'color': '#3B82F6',
                        'subtitle': f"{personal_tasks_by_status['achevee']} terminées sur {total_personal_tasks}"
                    },
                    {
                        'title': 'Événements personnels',
                        'value': total_personal_events,
                        'completion_rate': safe_percentage(personal_events_by_status['completed'], total_personal_events),
                        'icon': 'calendar',
                        'color': '#10B981',
                        'subtitle': f"{personal_events_by_status['completed']} terminés sur {total_personal_events}"
                    }
                ],

                # Graphiques en secteurs
                'charts': {
                    'personal_tasks': personal_tasks_chart,
                    'personal_events': personal_events_chart
                },

                # Données détaillées
                'detailed_stats': {
                    'personal_tasks': {
                        'total': total_personal_tasks,
                        'by_status': personal_tasks_by_status,
                        'completion_rate': safe_percentage(personal_tasks_by_status['achevee'], total_personal_tasks)
                    },
                    'personal_events': {
                        'total': total_personal_events,
                        'by_status': personal_events_by_status,
                        'completion_rate': safe_percentage(personal_events_by_status['completed'], total_personal_events)
                    }
                },

                # Métadonnées
                'metadata': {
                    'last_updated': now.isoformat(),
                    'refresh_mode': 'manual' if manual_refresh else 'automatic',
                    'refresh_interval': None if manual_refresh else 30,
                    'dashboard_title': f'Tableau de Bord - {user.name}',
                    'dashboard_subtitle': f'Suivi personnel ({user.role})',
                    'period': period,
                    'manual_refresh': manual_refresh
                }
            }

            # Ajouter les métriques d'équipe pour les employés
            if user.role == 'employee' and team_tasks_metrics:
                # Ajouter les cartes d'équipe
                response_data['metric_cards'].extend([
                    {
                        'title': 'Tâches d\'équipe',
                        'value': team_tasks_metrics['total'],
                        'completion_rate': team_tasks_metrics['completion_rate'],
                        'icon': 'users',
                        'color': '#8B5CF6',
                        'subtitle': f"{team_tasks_by_status['achevee']} terminées sur {team_tasks_metrics['total']}"
                    },
                    {
                        'title': 'Événements d\'équipe',
                        'value': team_events_metrics['total'],
                        'completion_rate': team_events_metrics['completion_rate'],
                        'icon': 'calendar-days',
                        'color': '#F59E0B',
                        'subtitle': f"{team_events_by_status['completed']} terminés sur {team_events_metrics['total']}"
                    }
                ])

                # Ajouter les graphiques d'équipe
                response_data['charts']['team_tasks'] = team_tasks_chart
                response_data['charts']['team_events'] = team_events_chart

                # Ajouter les données détaillées d'équipe
                response_data['detailed_stats']['team_tasks'] = team_tasks_metrics
                response_data['detailed_stats']['team_events'] = team_events_metrics

            logger.info(f"Métriques employé/client récupérées avec succès pour {user.email}")
            return Response(response_data)

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des métriques employé/client: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class BiDashboardView(APIView):
    """
    Vue pour gérer les tableaux de bord BI
    """
    permission_classes = [IsAuthenticated]  # Tous les utilisateurs authentifiés peuvent accéder

    def get(self, request):
        """
        Récupère le tableau de bord BI de l'utilisateur
        """
        try:
            # Vérifier que l'utilisateur est bien authentifié
            if not request.user.is_authenticated:
                logger.warning("Tentative d'accès au tableau de bord BI sans authentification")
                return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

            user = request.user
            user_id = str(user.id)
            logger.info(f"Récupération du tableau de bord BI pour: {user.email}, rôle: {user.role}")

            # Récupérer le tableau de bord de l'utilisateur
            try:
                dashboard = BiDashboard.objects.get(user_id=user_id)
                logger.info(f"Tableau de bord existant trouvé pour: {user.email}")
                return Response(self._format_dashboard(dashboard))
            except BiDashboard.DoesNotExist:
                # Créer un tableau de bord par défaut
                logger.info(f"Création d'un tableau de bord par défaut pour: {user.email}")
                dashboard_type = user.role
                dashboard = self._create_default_dashboard(user, dashboard_type)
                return Response(self._format_dashboard(dashboard))
        except Exception as e:
            logger.error(f"Erreur lors de la récupération du tableau de bord BI: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """
        Met à jour le tableau de bord BI de l'utilisateur
        """
        user = request.user
        user_id = str(user.id)
        data = request.data

        # Vérifier les données
        if not data.get('title') or not data.get('layout') or not data.get('metrics'):
            return Response({'error': 'Données invalides'}, status=status.HTTP_400_BAD_REQUEST)

        # Récupérer ou créer le tableau de bord
        try:
            dashboard = BiDashboard.objects.get(user_id=user_id)
        except BiDashboard.DoesNotExist:
            dashboard = BiDashboard(
                user_id=user_id,
                dashboard_type=user.role,
                title=data.get('title'),
                layout=data.get('layout'),
                metrics=data.get('metrics')
            )

        # Mettre à jour le tableau de bord
        dashboard.title = data.get('title')
        dashboard.layout = data.get('layout')
        dashboard.metrics = data.get('metrics')
        dashboard.save()

        return Response(self._format_dashboard(dashboard))

    def _format_dashboard(self, dashboard):
        """
        Formate le tableau de bord pour la réponse
        """
        return {
            'id': dashboard.id,
            'title': dashboard.title,
            'dashboard_type': dashboard.dashboard_type,
            'layout': dashboard.layout,
            'metrics': dashboard.metrics,
            'created_at': dashboard.created_at,
            'updated_at': dashboard.updated_at
        }

    def _create_default_dashboard(self, user, dashboard_type):
        """
        Crée un tableau de bord par défaut pour l'utilisateur
        """
        title = f"Tableau de bord de {user.name}"

        # Configuration par défaut en fonction du rôle
        if dashboard_type == 'super_admin':
            layout = {
                'columns': 4,  # Augmenter le nombre de colonnes pour plus de graphiques
                'rows': 4      # Augmenter le nombre de lignes pour plus de graphiques
            }
            metrics = {
                'user_activity': {
                    'position': {'x': 0, 'y': 0, 'w': 2, 'h': 1},
                    'title': 'Activité des utilisateurs',
                    'type': 'pie_chart'
                },
                'platform_activity': {
                    'position': {'x': 2, 'y': 0, 'w': 2, 'h': 1},
                    'title': 'Activité de la plateforme',
                    'type': 'bar_chart'
                },
                'active_users_trend': {
                    'position': {'x': 0, 'y': 1, 'w': 4, 'h': 1},
                    'title': 'Tendance des utilisateurs actifs',
                    'type': 'line_chart',
                    'data_source': 'active_users_history'
                },
                'role_usage': {
                    'position': {'x': 0, 'y': 2, 'w': 4, 'h': 1},
                    'title': 'Taux d\'utilisation par rôle',
                    'type': 'line_chart',
                    'data_source': 'role_usage_history'
                },
                'active_inactive_users': {
                    'position': {'x': 0, 'y': 3, 'w': 2, 'h': 1},
                    'title': 'Utilisateurs actifs/inactifs',
                    'type': 'pie_chart',
                    'data_source': 'active_inactive_users'
                },
                'role_distribution': {
                    'position': {'x': 2, 'y': 3, 'w': 2, 'h': 1},
                    'title': 'Distribution des rôles',
                    'type': 'pie_chart',
                    'data_source': 'role_distribution'
                }
            }
        elif dashboard_type == 'admin':
            layout = {
                'columns': 2,
                'rows': 2
            }
            metrics = {
                'team_tasks': {
                    'position': {'x': 0, 'y': 0, 'w': 1, 'h': 1},
                    'title': 'Tâches d\'équipe',
                    'type': 'pie_chart'
                },
                'team_events': {
                    'position': {'x': 1, 'y': 0, 'w': 1, 'h': 1},
                    'title': 'Événements d\'équipe',
                    'type': 'pie_chart'
                }
            }
        else:  # employee ou client
            layout = {
                'columns': 2,
                'rows': 2
            }
            metrics = {
                'personal_tasks': {
                    'position': {'x': 0, 'y': 0, 'w': 1, 'h': 1},
                    'title': 'Tâches personnelles',
                    'type': 'pie_chart'
                },
                'personal_events': {
                    'position': {'x': 1, 'y': 0, 'w': 1, 'h': 1},
                    'title': 'Événements personnels',
                    'type': 'pie_chart'
                }
            }

            # Ajouter les métriques d'équipe pour les employés
            if dashboard_type == 'employee':
                metrics['team_tasks'] = {
                    'position': {'x': 0, 'y': 1, 'w': 1, 'h': 1},
                    'title': 'Tâches d\'équipe',
                    'type': 'pie_chart'
                }
                metrics['team_events'] = {
                    'position': {'x': 1, 'y': 1, 'w': 1, 'h': 1},
                    'title': 'Événements d\'équipe',
                    'type': 'pie_chart'
                }

        # Créer et sauvegarder le tableau de bord
        dashboard = BiDashboard(
            user_id=str(user.id),
            dashboard_type=dashboard_type,
            title=title,
            layout=layout,
            metrics=metrics
        )
        dashboard.save()

        return dashboard


class RealTimeLoginStatsView(APIView):
    """
    Vue spécialisée pour les statistiques de connexion en temps réel
    """
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        """
        Récupère les statistiques de connexion en temps réel
        """
        try:
            logger.info("Récupération des statistiques de connexion en temps réel")

            # Timestamp de la requête
            now = datetime.now(timezone.utc)

            # Données de connexion en temps réel
            users_logged_today = DailyLoginTracker.get_users_logged_today()
            total_logins_today = DailyLoginTracker.get_total_logins_today()
            total_users = User.objects.count()
            inactive_users_today = total_users - users_logged_today

            # Calculer les pourcentages
            active_percentage_today = round((users_logged_today / total_users) * 100, 2) if total_users > 0 else 0
            inactive_percentage_today = round((inactive_users_today / total_users) * 100, 2) if total_users > 0 else 0

            # Données pour le graphique en temps réel
            realtime_data = {
                'timestamp': now.isoformat(),
                'is_realtime': True,
                'refresh_interval': 10,  # Actualisation toutes les 10 secondes

                # Métriques principales
                'metrics': {
                    'total_users': total_users,
                    'users_logged_today': users_logged_today,
                    'total_logins_today': total_logins_today,
                    'inactive_users_today': inactive_users_today,
                    'active_percentage_today': active_percentage_today,
                    'inactive_percentage_today': inactive_percentage_today
                },

                # Données pour le graphique en anneau
                'chart_data': {
                    'type': 'doughnut',
                    'title': 'Connexions Aujourd\'hui',
                    'data': [
                        {
                            'name': 'Connectés aujourd\'hui',
                            'value': users_logged_today,
                            'percentage': active_percentage_today,
                            'color': '#10B981'
                        },
                        {
                            'name': 'Non connectés aujourd\'hui',
                            'value': inactive_users_today,
                            'percentage': inactive_percentage_today,
                            'color': '#EF4444'
                        }
                    ],
                    'legend': [
                        {'label': 'Connectés aujourd\'hui', 'color': '#10B981'},
                        {'label': 'Non connectés aujourd\'hui', 'color': '#EF4444'}
                    ]
                },

                # Indicateurs visuels
                'indicators': {
                    'realtime_status': 'active',
                    'last_update': now.isoformat(),
                    'data_freshness': 'live',
                    'update_frequency': '10 secondes'
                }
            }

            logger.info(f"Statistiques en temps réel - Connectés: {users_logged_today}, Total: {total_users}")
            return Response(realtime_data)

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des statistiques en temps réel: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LoginDataDebugView(APIView):
    """
    Vue de débogage pour vérifier la cohérence des données de connexion
    """
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        """
        Affiche les données de connexion pour débogage
        """
        try:
            now = datetime.now(timezone.utc)
            one_day_ago = now - timedelta(days=1)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)

            # Données du DailyLoginTracker
            users_logged_today_tracker = DailyLoginTracker.get_users_logged_today()
            total_logins_today_tracker = DailyLoginTracker.get_total_logins_today()

            # Données basées sur last_login
            users_logged_24h_lastlogin = User.objects(last_login__gte=one_day_ago).count()
            users_logged_today_lastlogin = User.objects(last_login__gte=today_start).count()

            # Données détaillées du tracker
            try:
                tracker = DailyLoginTracker.objects.get(date=today_start)
                tracker_details = {
                    'date': tracker.date.isoformat(),
                    'users_logged_today': tracker.users_logged_today,
                    'unique_users_today': tracker.unique_users_today,
                    'total_logins_today': tracker.total_logins_today,
                    'created_at': tracker.created_at.isoformat(),
                    'updated_at': tracker.updated_at.isoformat()
                }
            except DailyLoginTracker.DoesNotExist:
                tracker_details = None

            # Total des utilisateurs
            total_users = User.objects.count()

            debug_data = {
                'timestamp': now.isoformat(),
                'today_start': today_start.isoformat(),
                'one_day_ago': one_day_ago.isoformat(),

                'data_comparison': {
                    'daily_tracker': {
                        'users_logged_today': users_logged_today_tracker,
                        'total_logins_today': total_logins_today_tracker
                    },
                    'last_login_based': {
                        'users_logged_24h': users_logged_24h_lastlogin,
                        'users_logged_today': users_logged_today_lastlogin
                    }
                },

                'calculations': {
                    'total_users': total_users,
                    'inactive_users_today_tracker': total_users - users_logged_today_tracker,
                    'inactive_users_today_lastlogin': total_users - users_logged_today_lastlogin
                },

                'tracker_details': tracker_details,

                'recommendations': {
                    'use_tracker_for_today': 'Utiliser DailyLoginTracker pour les connexions d\'aujourd\'hui',
                    'use_lastlogin_for_periods': 'Utiliser last_login pour les périodes (24h, 7j, 30j)',
                    'difference_explanation': 'La différence peut venir du fait que last_login est mis à jour à chaque connexion, tandis que le tracker compte les utilisateurs uniques par jour'
                }
            }

            return Response(debug_data)

        except Exception as e:
            logger.error(f"Erreur lors du débogage des données de connexion: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class BiHistoricalDataView(APIView):
    """
    Vue pour récupérer les données historiques pour les graphiques BI
    """
    permission_classes = [IsAuthenticated]  # Tous les utilisateurs authentifiés peuvent accéder

    def get(self, request):
        """
        Récupère les données historiques pour les graphiques BI
        """
        try:
            # Vérifier que l'utilisateur est bien authentifié
            if not request.user.is_authenticated:
                logger.warning("Tentative d'accès aux données historiques BI sans authentification")
                return Response({"error": "Authentication required"}, status=status.HTTP_401_UNAUTHORIZED)

            user = request.user
            logger.info(f"Récupération des données historiques BI pour: {user.email}, rôle: {user.role}")

            # Vérifier que l'utilisateur est un super admin
            if user.role != 'super_admin':
                logger.warning(f"Tentative d'accès non autorisé aux données historiques BI par: {user.email}, rôle: {user.role}")
                return Response({"error": "Accès non autorisé"}, status=status.HTTP_403_FORBIDDEN)

            # Récupérer les paramètres de la requête
            data_type = request.query_params.get('data_type', 'active_users')
            period = request.query_params.get('period', '30d')  # 7d, 30d, 90d, 365d
            logger.info(f"Paramètres de requête - data_type: {data_type}, period: {period}")

            # Déterminer la période de temps
            now = datetime.now(timezone.utc)
            if period == '7d':
                start_date = now - timedelta(days=7)
                metric_type = 'daily'
            elif period == '30d':
                start_date = now - timedelta(days=30)
                metric_type = 'daily'
            elif period == '90d':
                start_date = now - timedelta(days=90)
                metric_type = 'daily'
            elif period == '365d':
                start_date = now - timedelta(days=365)
                metric_type = 'weekly'
            else:
                logger.warning(f"Période invalide spécifiée: {period}")
                return Response({"error": "Période invalide"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # Récupérer les instantanés de métriques pour la période spécifiée
                snapshots = BiMetricSnapshot.objects(
                    dashboard_type='super_admin',
                    metric_type=metric_type,
                    timestamp__gte=start_date
                ).order_by('timestamp')

                logger.info(f"Nombre d'instantanés récupérés: {len(snapshots)}")

                # Préparer les données pour le graphique
                chart_data = self._prepare_chart_data(snapshots, data_type)
                logger.info(f"Données du graphique préparées avec succès pour: {data_type}")

                return Response(chart_data)
            except Exception as query_error:
                logger.error(f"Erreur lors de la récupération des instantanés: {str(query_error)}")
                return Response({"error": str(query_error)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la récupération des données historiques: {str(e)}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _prepare_chart_data(self, snapshots, data_type):
        """
        Prépare les données pour le graphique en fonction du type de données demandé
        """
        if data_type == 'active_users':
            return self._prepare_active_users_data(snapshots)
        elif data_type == 'role_usage':
            return self._prepare_role_usage_data(snapshots)
        elif data_type == 'active_inactive_users':
            return self._prepare_active_inactive_users_data(snapshots)
        elif data_type == 'role_distribution':
            return self._prepare_role_distribution_data(snapshots)
        else:
            return {"error": "Type de données invalide"}

    def _prepare_active_users_data(self, snapshots):
        """
        Prépare les données pour le graphique de tendance des utilisateurs actifs
        Utilise les snapshots pour l'historique mais ajoute les données en temps réel pour le point actuel
        """
        labels = []
        active_24h = []
        active_7d = []
        active_30d = []

        # Récupérer les données historiques à partir des snapshots
        for snapshot in snapshots:
            # Formater la date pour l'affichage
            date_str = snapshot.timestamp.strftime('%d/%m/%Y')
            labels.append(date_str)

            # Extraire les données d'activité des utilisateurs
            user_activity = snapshot.metrics.get('user_activity', {})
            active_users = user_activity.get('active_users', {})

            # Ajouter les données aux séries
            active_24h.append(active_users.get('24h', 0))
            active_7d.append(active_users.get('7d', 0))
            active_30d.append(active_users.get('30d', 0))

        # Ajouter les données en temps réel pour le point actuel
        try:
            now = datetime.now(timezone.utc)
            one_day_ago = now - timedelta(days=1)
            seven_days_ago = now - timedelta(days=7)
            thirty_days_ago = now - timedelta(days=30)

            # Compter les utilisateurs actifs pour chaque période
            active_users_24h = User.objects(last_login__gte=one_day_ago).count()
            active_users_7d = User.objects(last_login__gte=seven_days_ago).count()
            active_users_30d = User.objects(last_login__gte=thirty_days_ago).count()

            # Ajouter la date actuelle et les données en temps réel
            current_date = now.strftime('%d/%m/%Y')

            # Vérifier si la date actuelle est déjà dans les labels (pour éviter les doublons)
            if not labels or labels[-1] != current_date:
                labels.append(current_date)
                active_24h.append(active_users_24h)
                active_7d.append(active_users_7d)
                active_30d.append(active_users_30d)
            else:
                # Mettre à jour le dernier point avec les données en temps réel
                active_24h[-1] = active_users_24h
                active_7d[-1] = active_users_7d
                active_30d[-1] = active_users_30d

            # Log pour le débogage
            logger.info(f"Utilisateurs actifs en temps réel - 24h: {active_users_24h}, 7j: {active_users_7d}, 30j: {active_users_30d}")

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des utilisateurs actifs en temps réel: {str(e)}")

        return {
            'labels': labels,
            'datasets': [
                {
                    'label': 'Actifs (24h)',
                    'data': active_24h,
                    'borderColor': '#FF6384',
                    'backgroundColor': 'rgba(255, 99, 132, 0.2)'
                },
                {
                    'label': 'Actifs (7j)',
                    'data': active_7d,
                    'borderColor': '#36A2EB',
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)'
                },
                {
                    'label': 'Actifs (30j)',
                    'data': active_30d,
                    'borderColor': '#4BC0C0',
                    'backgroundColor': 'rgba(75, 192, 192, 0.2)'
                }
            ]
        }

    def _prepare_role_usage_data(self, snapshots):
        """
        Prépare les données pour le graphique de taux d'utilisation par rôle
        Utilise les snapshots pour l'historique mais ajoute les données en temps réel pour le point actuel
        """
        labels = []
        super_admin_rate = []
        admin_rate = []
        employee_rate = []
        client_rate = []

        # Récupérer les données historiques à partir des snapshots
        for snapshot in snapshots:
            # Formater la date pour l'affichage
            date_str = snapshot.timestamp.strftime('%d/%m/%Y')
            labels.append(date_str)

            # Extraire les données d'activité par rôle
            user_activity = snapshot.metrics.get('user_activity', {})
            activity_rate = user_activity.get('activity_rate_by_role', {})

            # Ajouter les données aux séries (taux d'activité sur 7 jours)
            super_admin_rate.append(activity_rate.get('super_admin', {}).get('7d', 0))
            admin_rate.append(activity_rate.get('admin', {}).get('7d', 0))
            employee_rate.append(activity_rate.get('employee', {}).get('7d', 0))
            client_rate.append(activity_rate.get('client', {}).get('7d', 0))

        # Ajouter les données en temps réel pour le point actuel
        try:
            now = datetime.now(timezone.utc)
            seven_days_ago = now - timedelta(days=7)

            # Compter le nombre total d'utilisateurs par rôle
            total_super_admin = User.objects(role='super_admin').count()
            total_admin = User.objects(role='admin').count()
            total_employee = User.objects(role='employee').count()
            total_client = User.objects(role='client').count()

            # Compter les utilisateurs actifs par rôle au cours des 7 derniers jours
            active_super_admin = User.objects(role='super_admin', last_login__gte=seven_days_ago).count()
            active_admin = User.objects(role='admin', last_login__gte=seven_days_ago).count()
            active_employee = User.objects(role='employee', last_login__gte=seven_days_ago).count()
            active_client = User.objects(role='client', last_login__gte=seven_days_ago).count()

            # Calculer les taux d'activité
            super_admin_activity_rate = round((active_super_admin / total_super_admin) * 100, 1) if total_super_admin > 0 else 0
            admin_activity_rate = round((active_admin / total_admin) * 100, 1) if total_admin > 0 else 0
            employee_activity_rate = round((active_employee / total_employee) * 100, 1) if total_employee > 0 else 0
            client_activity_rate = round((active_client / total_client) * 100, 1) if total_client > 0 else 0

            # Ajouter la date actuelle et les données en temps réel
            current_date = now.strftime('%d/%m/%Y')

            # Vérifier si la date actuelle est déjà dans les labels (pour éviter les doublons)
            if not labels or labels[-1] != current_date:
                labels.append(current_date)
                super_admin_rate.append(super_admin_activity_rate)
                admin_rate.append(admin_activity_rate)
                employee_rate.append(employee_activity_rate)
                client_rate.append(client_activity_rate)
            else:
                # Mettre à jour le dernier point avec les données en temps réel
                super_admin_rate[-1] = super_admin_activity_rate
                admin_rate[-1] = admin_activity_rate
                employee_rate[-1] = employee_activity_rate
                client_rate[-1] = client_activity_rate

            # Log pour le débogage
            logger.info(f"Taux d'utilisation par rôle en temps réel - Super Admin: {super_admin_activity_rate}%, Admin: {admin_activity_rate}%, Employé: {employee_activity_rate}%, Client: {client_activity_rate}%")

        except Exception as e:
            logger.error(f"Erreur lors de la récupération des taux d'utilisation par rôle en temps réel: {str(e)}")

        return {
            'labels': labels,
            'datasets': [
                {
                    'label': 'Super Admin',
                    'data': super_admin_rate,
                    'borderColor': '#FF6384',
                    'backgroundColor': 'rgba(255, 99, 132, 0.2)'
                },
                {
                    'label': 'Admin',
                    'data': admin_rate,
                    'borderColor': '#36A2EB',
                    'backgroundColor': 'rgba(54, 162, 235, 0.2)'
                },
                {
                    'label': 'Employé',
                    'data': employee_rate,
                    'borderColor': '#4BC0C0',
                    'backgroundColor': 'rgba(75, 192, 192, 0.2)'
                },
                {
                    'label': 'Client',
                    'data': client_rate,
                    'borderColor': '#FFCE56',
                    'backgroundColor': 'rgba(255, 206, 86, 0.2)'
                }
            ]
        }

    def _prepare_active_inactive_users_data(self, snapshots):
        """
        Prépare les données pour le graphique circulaire des utilisateurs actifs/inactifs
        Récupère les données en temps réel directement depuis la base de données
        """
        try:
            # Récupérer les données en temps réel depuis la base de données
            now = datetime.now(timezone.utc)
            thirty_days_ago = now - timedelta(days=30)

            # Compter le nombre total d'utilisateurs
            total_users = User.objects.count()

            # Compter les utilisateurs actifs au cours des 30 derniers jours
            active_users = User.objects(last_login__gte=thirty_days_ago).count()

            # Calculer le nombre d'utilisateurs inactifs
            inactive_users = total_users - active_users

            # Log pour le débogage
            logger.info(f"Utilisateurs actifs/inactifs en temps réel - Total: {total_users}, Actifs (30j): {active_users}, Inactifs: {inactive_users}")

            return {
                'labels': ['Actifs (30j)', 'Inactifs'],
                'datasets': [{
                    'data': [active_users, inactive_users],
                    'backgroundColor': ['#36A2EB', '#FF6384']
                }]
            }
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des utilisateurs actifs/inactifs: {str(e)}")
            # Fallback sur les données des snapshots si disponibles
            if not snapshots:
                return {
                    'labels': ['Actifs', 'Inactifs'],
                    'datasets': [{
                        'data': [0, 0],
                        'backgroundColor': ['#36A2EB', '#FF6384']
                    }]
                }

            latest_snapshot = snapshots[len(snapshots) - 1]
            user_activity = latest_snapshot.metrics.get('user_activity', {})

            total_users = user_activity.get('total_users', 0)
            active_users = user_activity.get('active_users', {}).get('30d', 0)
            inactive_users = total_users - active_users

            return {
                'labels': ['Actifs (30j)', 'Inactifs'],
                'datasets': [{
                    'data': [active_users, inactive_users],
                    'backgroundColor': ['#36A2EB', '#FF6384']
                }]
            }

    def _prepare_role_distribution_data(self, snapshots):
        """
        Prépare les données pour le graphique circulaire de la distribution des rôles
        Récupère les données en temps réel directement depuis la base de données
        """
        # Forcer la mise à jour des métriques BI avant de récupérer les données
        from ..tasks.bi_tasks import update_user_activity_metrics
        update_user_activity_metrics()

        # Récupérer les données en temps réel directement depuis la base de données
        try:
            # Compter les utilisateurs par rôle
            super_admin_count = User.objects(role='super_admin').count()
            admin_count = User.objects(role='admin').count()
            employee_count = User.objects(role='employee').count()
            client_count = User.objects(role='client').count()

            # Log pour le débogage
            logger.info(f"Distribution des rôles en temps réel - Super Admin: {super_admin_count}, Admin: {admin_count}, Employé: {employee_count}, Client: {client_count}")

            # Préparer les données pour le graphique
            return {
                'labels': ['Super Admin', 'Admin', 'Employé', 'Client'],
                'datasets': [{
                    'data': [super_admin_count, admin_count, employee_count, client_count],
                    'backgroundColor': ['#FF6384', '#36A2EB', '#4BC0C0', '#FFCE56']
                }]
            }
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de la distribution des rôles: {str(e)}")
            # Fallback sur les données des snapshots si disponibles
            if not snapshots:
                return {
                    'labels': ['Super Admin', 'Admin', 'Employé', 'Client'],
                    'datasets': [{
                        'data': [0, 0, 0, 0],
                        'backgroundColor': ['#FF6384', '#36A2EB', '#4BC0C0', '#FFCE56']
                    }]
                }

            latest_snapshot = snapshots[len(snapshots) - 1]
            user_activity = latest_snapshot.metrics.get('user_activity', {})
            users_by_role = user_activity.get('users_by_role', {})

            super_admin_count = users_by_role.get('super_admin', 0)
            admin_count = users_by_role.get('admin', 0)
            employee_count = users_by_role.get('employee', 0)
            client_count = users_by_role.get('client', 0)

            return {
                'labels': ['Super Admin', 'Admin', 'Employé', 'Client'],
                'datasets': [{
                    'data': [super_admin_count, admin_count, employee_count, client_count],
                    'backgroundColor': ['#FF6384', '#36A2EB', '#4BC0C0', '#FFCE56']
                }]
            }


class AdminDashboardView(APIView):
    """
    Tableau de bord BI pour les admins avec analyses de leurs activités d'équipe
    """
    permission_classes = [IsAuthenticated]

    @admin_required
    def get(self, request):
        """
        Récupère les métriques d'activité pour un admin avec filtrage par période
        Paramètres de requête:
        - period: '1h', '24h', '7d', '30d', 'today' (défaut: 'today')
        - manual_refresh: true/false (défaut: true)
        """
        try:
            # Récupérer les paramètres de requête
            period = request.query_params.get('period', 'today')
            manual_refresh = request.query_params.get('manual_refresh', 'true').lower() == 'true'
            admin_id = str(request.user.id)

            logger.info(f"Récupération des métriques admin pour {admin_id} - Période: {period}")

            # Timestamp de la requête
            now = datetime.now(timezone.utc)

            # Obtenir les statistiques de l'admin
            admin_stats = AdminActivityTracker.update_admin_stats(admin_id, period)

            if not admin_stats:
                return Response({'error': 'Impossible de récupérer les statistiques'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # Définir le nom de la période
            period_names = {
                'today': 'Aujourd\'hui',
                '1h': 'Dernière heure',
                '24h': 'Dernières 24h',
                '7d': 'Derniers 7 jours',
                '30d': 'Derniers 30 jours'
            }
            period_name = period_names.get(period, 'Aujourd\'hui')

            # 1. CARTES DE MÉTRIQUES PRINCIPALES
            metric_cards = [
                {
                    'title': 'Équipes gérées',
                    'subtitle': f'Total des équipes sous votre responsabilité',
                    'value': admin_stats.total_teams,
                    'trend': f"{admin_stats.total_teams} équipe{'s' if admin_stats.total_teams > 1 else ''}",
                    'trend_period': 'total',
                    'icon': 'users',
                    'color': '#3B82F6',
                    'period': period,
                    'manual_refresh': manual_refresh,
                    'last_updated': now.isoformat()
                },
                {
                    'title': 'Membres d\'équipe',
                    'subtitle': f'Total des employés dans vos équipes',
                    'value': admin_stats.total_team_members,
                    'trend': f"{admin_stats.total_team_members} membre{'s' if admin_stats.total_team_members > 1 else ''}",
                    'trend_period': 'total',
                    'icon': 'user-group',
                    'color': '#10B981',
                    'period': period,
                    'manual_refresh': manual_refresh,
                    'last_updated': now.isoformat()
                },
                {
                    'title': 'Progression moyenne',
                    'subtitle': f'Progression de vos équipes',
                    'value': f"{admin_stats.team_progress_average}%",
                    'trend': f"{admin_stats.team_progress_average}% de progression",
                    'trend_period': 'moyenne',
                    'icon': 'trending-up',
                    'color': '#8B5CF6',
                    'period': period,
                    'manual_refresh': manual_refresh,
                    'last_updated': now.isoformat()
                }
            ]

            # 2. GRAPHIQUES D'ANALYSE

            # Graphique 1: Distribution des événements d'équipe par statut (statuts corrects)
            # Calculer les valeurs sécurisées pour éviter les NaN
            events_pending = safe_int(admin_stats.team_events_pending)
            events_completed = safe_int(admin_stats.team_events_completed)
            events_archived = safe_int(admin_stats.team_events_archived) if hasattr(admin_stats, 'team_events_archived') else 0
            events_total = safe_int(admin_stats.team_events_total)

            events_chart = {
                'type': 'pie',
                'title': f'Distribution des Événements d\'Équipe par Statut - {period_name}',
                'subtitle': f'Répartition des événements ({period_name.lower()})',
                'data': [
                    {
                        'name': 'En attente',
                        'value': events_pending,
                        'color': '#3B82F6',
                        'percentage': safe_percentage(events_pending, events_total)
                    },
                    {
                        'name': 'Terminés',
                        'value': events_completed,
                        'color': '#10B981',
                        'percentage': safe_percentage(events_completed, events_total)
                    },
                    {
                        'name': 'Archivés',
                        'value': events_archived,
                        'color': '#6B7280',
                        'percentage': safe_percentage(events_archived, events_total)
                    }
                ],
                'legend': [
                    {'label': 'En attente', 'color': '#3B82F6'},
                    {'label': 'Terminés', 'color': '#10B981'},
                    {'label': 'Archivés', 'color': '#6B7280'}
                ],
                'period': period,
                'period_name': period_name,
                'manual_refresh': manual_refresh,
                'last_updated': now.isoformat(),
                'total_count': events_total
            }

            # Graphique 2: Distribution des tâches d'équipe par statut (statuts corrects)
            # Calculer les valeurs sécurisées pour éviter les NaN
            tasks_pending = safe_int(admin_stats.team_tasks_pending)  # a_faire
            tasks_in_progress = safe_int(admin_stats.team_tasks_in_progress) if hasattr(admin_stats, 'team_tasks_in_progress') else 0  # en_cours
            tasks_in_revision = safe_int(admin_stats.team_tasks_in_revision) if hasattr(admin_stats, 'team_tasks_in_revision') else 0  # en_revision
            tasks_completed = safe_int(admin_stats.team_tasks_completed)  # achevee
            tasks_archived = safe_int(admin_stats.team_tasks_archived) if hasattr(admin_stats, 'team_tasks_archived') else 0  # archived
            tasks_total = safe_int(admin_stats.team_tasks_total)

            tasks_chart = {
                'type': 'pie',
                'title': f'Distribution des Tâches d\'Équipe par Statut - {period_name}',
                'subtitle': f'Répartition des tâches ({period_name.lower()})',
                'data': [
                    {
                        'name': 'À faire',
                        'value': tasks_pending,
                        'color': '#3B82F6',
                        'percentage': safe_percentage(tasks_pending, tasks_total)
                    },
                    {
                        'name': 'En cours',
                        'value': tasks_in_progress,
                        'color': '#F59E0B',
                        'percentage': safe_percentage(tasks_in_progress, tasks_total)
                    },
                    {
                        'name': 'En révision',
                        'value': tasks_in_revision,
                        'color': '#8B5CF6',
                        'percentage': safe_percentage(tasks_in_revision, tasks_total)
                    },
                    {
                        'name': 'Terminées',
                        'value': tasks_completed,
                        'color': '#10B981',
                        'percentage': safe_percentage(tasks_completed, tasks_total)
                    },
                    {
                        'name': 'Archivées',
                        'value': tasks_archived,
                        'color': '#6B7280',
                        'percentage': safe_percentage(tasks_archived, tasks_total)
                    }
                ],
                'legend': [
                    {'label': 'À faire', 'color': '#3B82F6'},
                    {'label': 'En cours', 'color': '#F59E0B'},
                    {'label': 'En révision', 'color': '#8B5CF6'},
                    {'label': 'Terminées', 'color': '#10B981'},
                    {'label': 'Archivées', 'color': '#6B7280'}
                ],
                'period': period,
                'period_name': period_name,
                'manual_refresh': manual_refresh,
                'last_updated': now.isoformat(),
                'total_count': tasks_total
            }

            # 3. STATISTIQUES DÉTAILLÉES (avec calculs sécurisés)
            detailed_stats = {
                'team_management': {
                    'total_teams': safe_int(admin_stats.total_teams),
                    'total_team_members': safe_int(admin_stats.total_team_members),
                    'teams_managed': admin_stats.teams_managed if admin_stats.teams_managed else [],
                    'most_active_team': {
                        'id': admin_stats.most_active_team_id,
                        'name': admin_stats.most_active_team_name
                    } if admin_stats.most_active_team_id else None,
                    'average_progress': safe_float(admin_stats.team_progress_average)
                },
                'events_activity': {
                    'total': events_total,
                    'created_in_period': safe_int(admin_stats.team_events_created),
                    'completed_in_period': events_completed,
                    'pending': events_pending,
                    'archived': events_archived,
                    'completion_rate': safe_percentage(events_completed, events_total),
                    'by_status': {
                        'pending': events_pending,
                        'completed': events_completed,
                        'archived': events_archived
                    }
                },
                'tasks_activity': {
                    'total': tasks_total,
                    'created_in_period': safe_int(admin_stats.team_tasks_created),
                    'completed_in_period': tasks_completed,
                    'pending': tasks_pending,
                    'in_progress': tasks_in_progress,
                    'in_revision': tasks_in_revision,
                    'archived': tasks_archived,
                    'completion_rate': safe_percentage(tasks_completed, tasks_total),
                    'by_status': {
                        'a_faire': tasks_pending,
                        'en_cours': tasks_in_progress,
                        'en_revision': tasks_in_revision,
                        'achevee': tasks_completed,
                        'archived': tasks_archived
                    }
                }
            }

            # 4. RÉPONSE COMPLÈTE
            dashboard_data = {
                'timestamp': now.isoformat(),
                'admin_id': admin_id,
                'admin_name': request.user.name,
                'is_team_leader': admin_stats.total_teams > 0,

                # Cartes de métriques
                'metric_cards': metric_cards,

                # Graphiques
                'charts': {
                    'events_distribution': events_chart,
                    'tasks_distribution': tasks_chart
                },

                # Statistiques détaillées
                'detailed_stats': detailed_stats,

                # Métadonnées
                'metadata': {
                    'last_updated': now.isoformat(),
                    'data_source': 'AdminActivityTracker',
                    'refresh_mode': 'manual' if manual_refresh else 'automatic',
                    'refresh_interval': None if manual_refresh else 30,
                    'dashboard_title': f'Tableau de Bord Admin - {period_name}',
                    'dashboard_subtitle': f'Analyses de vos activités d\'équipe ({period_name.lower()})',
                    'current_period': {
                        'period': period,
                        'period_name': period_name,
                        'manual_refresh': manual_refresh
                    },
                    'available_periods': [
                        {'value': 'today', 'label': 'Aujourd\'hui'},
                        {'value': '1h', 'label': 'Dernière heure'},
                        {'value': '24h', 'label': 'Dernières 24h'},
                        {'value': '7d', 'label': 'Derniers 7 jours'},
                        {'value': '30d', 'label': 'Derniers 30 jours'}
                    ],
                    'features': {
                        'period_filtering': True,
                        'manual_refresh': manual_refresh,
                        'team_analytics': True,
                        'real_time_data': period == 'today'
                    }
                }
            }

            logger.info(f"Tableau de bord admin généré pour {admin_id} - {safe_int(admin_stats.total_teams)} équipes, {safe_int(admin_stats.total_team_members)} membres")
            return Response(dashboard_data)

        except Exception as e:
            logger.error(f"Erreur lors de la génération du tableau de bord admin: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AdminActivityDebugView(APIView):
    """
    Vue de débogage pour vérifier les activités des admins
    """
    permission_classes = [IsAuthenticated]

    @admin_required
    def get(self, request):
        """
        Affiche les données d'activité admin pour débogage
        """
        try:
            admin_id = str(request.user.id)
            now = datetime.now(timezone.utc)

            # Récupérer les équipes gérées par l'admin
            admin_teams = Team.objects(responsable=admin_id)
            team_ids = [str(team.id) for team in admin_teams]

            # Statistiques détaillées par équipe
            teams_details = []
            for team in admin_teams:
                team_id = str(team.id)

                # Événements de l'équipe (statuts corrects)
                team_events_total = safe_int(Event.objects(team_id=team_id).count())
                team_events_completed = safe_int(Event.objects(team_id=team_id, status='completed').count())
                team_events_pending = safe_int(Event.objects(team_id=team_id, status='pending').count())

                # Tâches de l'équipe (statuts corrects)
                team_tasks_total = safe_int(TeamTask.objects(team_id=team_id).count())
                team_tasks_completed = safe_int(TeamTask.objects(team_id=team_id, status='achevee').count())  # Statut correct
                team_tasks_pending = safe_int(TeamTask.objects(team_id=team_id, status='a_faire').count())  # Statut correct

                # Progression de l'équipe
                total_items = safe_int(team_events_total + team_tasks_total)
                completed_items = safe_int(team_events_completed + team_tasks_completed)
                progress = safe_percentage(completed_items, total_items)

                teams_details.append({
                    'team_id': team_id,
                    'team_name': team.name,
                    'members_count': safe_int(len(team.members)),
                    'events': {
                        'total': team_events_total,
                        'completed': team_events_completed,
                        'pending': team_events_pending
                    },
                    'tasks': {
                        'total': team_tasks_total,
                        'completed': team_tasks_completed,
                        'pending': team_tasks_pending
                    },
                    'progress_percentage': safe_float(progress)
                })

            # Statistiques globales
            total_teams = safe_int(len(admin_teams))
            total_members = safe_int(sum(len(team.members) for team in admin_teams))
            total_events = safe_int(Event.objects(team_id__in=team_ids).count())
            total_tasks = safe_int(TeamTask.objects(team_id__in=team_ids).count())

            # Données du tracker pour aujourd'hui
            today_stats = AdminActivityTracker.get_admin_stats(admin_id, 'today')

            debug_data = {
                'timestamp': now.isoformat(),
                'admin_info': {
                    'admin_id': admin_id,
                    'admin_name': request.user.name,
                    'admin_email': request.user.email
                },
                'teams_summary': {
                    'total_teams': total_teams,
                    'total_members': total_members,
                    'total_events': total_events,
                    'total_tasks': total_tasks
                },
                'teams_details': teams_details,
                'tracker_data': {
                    'total_teams': safe_int(today_stats.total_teams if today_stats else 0),
                    'total_team_members': safe_int(today_stats.total_team_members if today_stats else 0),
                    'team_events_total': safe_int(today_stats.team_events_total if today_stats else 0),
                    'team_tasks_total': safe_int(today_stats.team_tasks_total if today_stats else 0),
                    'team_progress_average': safe_float(today_stats.team_progress_average if today_stats else 0),
                    'most_active_team': {
                        'id': today_stats.most_active_team_id if today_stats else None,
                        'name': today_stats.most_active_team_name if today_stats else None
                    }
                },
                'data_consistency': {
                    'teams_match': total_teams == (safe_int(today_stats.total_teams) if today_stats else 0),
                    'members_match': total_members == (safe_int(today_stats.total_team_members) if today_stats else 0),
                    'events_match': total_events == (safe_int(today_stats.team_events_total) if today_stats else 0),
                    'tasks_match': total_tasks == (safe_int(today_stats.team_tasks_total) if today_stats else 0)
                },
                'recommendations': {
                    'is_team_leader': total_teams > 0,
                    'has_active_teams': any(team['progress_percentage'] > 0 for team in teams_details),
                    'needs_attention': [team['team_name'] for team in teams_details if team['progress_percentage'] < 20]
                }
            }

            return Response(debug_data)

        except Exception as e:
            logger.error(f"Erreur lors du débogage des activités admin: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
