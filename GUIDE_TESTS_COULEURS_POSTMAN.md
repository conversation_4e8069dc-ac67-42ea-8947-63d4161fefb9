# 🎨 Guide de Tests Postman - Système de Couleurs pour Événements

## 🎯 Objectif
Tester le nouveau système de couleurs personnalisables pour les événements personnels et d'équipe avec une palette prédéfinie depuis le backend.

## 🆕 Nouvelles Fonctionnalités

### ✅ **Palette de Couleurs Prédéfinies**
- 12 couleurs avec noms et descriptions
- Couleurs organisées par catégories (urgence, statut, type, personnel)
- Suggestions automatiques basées sur le titre
- Validation des couleurs personnalisées

### ✅ **Endpoints Ajoutés**
1. `GET /api/colors/palette/` - Récupérer la palette complète
2. `GET /api/colors/categories/` - Récupérer les couleurs par catégories
3. `POST /api/colors/suggest/` - Suggérer une couleur basée sur le titre
4. `POST /api/colors/validate/` - Valider une couleur

## 🧪 Tests Postman à Effectuer

### **Prérequis**
```
# Se connecter en tant qu'admin ou employé
POST http://localhost:8000/api/login/
Content-Type: application/json

{
    "email": "[EMAIL]",
    "password": "[PASSWORD]"
}

# Récupérer le token pour les tests suivants
Authorization: Bearer [TOKEN]
```

---

## 📋 **1. Tests des Endpoints de Couleurs**

### **Test 1.1 : Récupérer la Palette Complète**
```
GET http://localhost:8000/api/colors/palette/
Authorization: Bearer [TOKEN]
```

**Résultat Attendu :**
```json
{
    "success": true,
    "data": {
        "palette": {
            "bleu_principal": {
                "name": "Bleu Principal",
                "hex": "#3788d8",
                "description": "Couleur par défaut - Bleu professionnel"
            },
            "rouge_urgent": {
                "name": "Rouge Urgent",
                "hex": "#e74c3c",
                "description": "Pour les tâches urgentes et importantes"
            }
            // ... autres couleurs
        },
        "categories": {
            "urgence": ["rouge_urgent", "orange_attention", "jaune_rappel"],
            "statut": ["vert_succes", "gris_archive", "noir_important"]
            // ... autres catégories
        },
        "recommended": {
            "reunion": "marron_reunion",
            "formation": "indigo_formation"
            // ... autres recommandations
        },
        "default_color": "#3788d8"
    }
}
```

### **Test 1.2 : Récupérer les Couleurs par Catégories**
```
GET http://localhost:8000/api/colors/categories/
Authorization: Bearer [TOKEN]
```

**Résultat Attendu :**
```json
{
    "success": true,
    "data": {
        "categories": {
            "urgence": [
                {
                    "name": "Rouge Urgent",
                    "hex": "#e74c3c",
                    "description": "Pour les tâches urgentes et importantes",
                    "key": "rouge_urgent"
                }
                // ... autres couleurs de la catégorie
            ]
        }
    }
}
```

### **Test 1.3 : Suggérer une Couleur**
```
POST http://localhost:8000/api/colors/suggest/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Réunion urgente avec l'équipe"
}
```

**Résultat Attendu :**
```json
{
    "success": true,
    "data": {
        "suggested_color": "#e74c3c",
        "color_name": "Rouge Urgent",
        "title_analyzed": "Réunion urgente avec l'équipe"
    }
}
```

### **Test 1.4 : Valider une Couleur**
```
POST http://localhost:8000/api/colors/validate/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "color": "#ff5733"
}
```

**Résultat Attendu :**
```json
{
    "success": true,
    "data": {
        "is_valid": true,
        "message": "Code hexadécimal valide",
        "color_input": "#ff5733",
        "hex_code": "#ff5733"
    }
}
```

---

## 🎨 **2. Tests de Création d'Événements avec Couleurs**

### **Test 2.1 : Créer un Événement avec Couleur de la Palette**
```
POST http://localhost:8000/api/events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Formation DevOps",
    "description": "Session de formation sur les outils DevOps",
    "start_date": "2025-05-30T09:00:00Z",
    "end_date": "2025-05-30T17:00:00Z",
    "start_time": "09:00",
    "end_time": "17:00",
    "color": "indigo_formation",
    "team_id": "[TEAM_ID]"
}
```

**Vérifications :**
- ✅ L'événement est créé avec `color: "#3f51b5"`
- ✅ La couleur est convertie du nom vers le code hex

### **Test 2.2 : Créer un Événement avec Code Hex Personnalisé**
```
POST http://localhost:8000/api/events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Événement spécial",
    "description": "Événement avec couleur personnalisée",
    "start_date": "2025-05-31T10:00:00Z",
    "end_date": "2025-05-31T12:00:00Z",
    "start_time": "10:00",
    "end_time": "12:00",
    "color": "#ff6b35"
}
```

**Vérifications :**
- ✅ L'événement est créé avec la couleur personnalisée
- ✅ La couleur est validée avant création

### **Test 2.3 : Créer un Événement sans Couleur (Suggestion Automatique)**
```
POST http://localhost:8000/api/events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Réunion d'équipe urgente",
    "description": "Discussion sur les priorités",
    "start_date": "2025-05-29T14:00:00Z",
    "end_date": "2025-05-29T15:00:00Z",
    "start_time": "14:00",
    "end_time": "15:00",
    "team_id": "[TEAM_ID]"
}
```

**Vérifications :**
- ✅ Une couleur est suggérée automatiquement basée sur "urgent" dans le titre
- ✅ L'événement est créé avec la couleur suggérée

### **Test 2.4 : Tentative avec Couleur Invalide**
```
POST http://localhost:8000/api/events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test couleur invalide",
    "description": "Test de validation",
    "start_date": "2025-05-29T16:00:00Z",
    "end_date": "2025-05-29T17:00:00Z",
    "start_time": "16:00",
    "end_time": "17:00",
    "color": "couleur_inexistante"
}
```

**Résultat Attendu :**
```json
{
    "error": "Couleur invalide: Couleur non reconnue",
    "provided_color": "couleur_inexistante"
}
```

---

## 🔄 **3. Tests de Mise à Jour d'Événements**

### **Test 3.1 : Changer la Couleur d'un Événement**
```
PUT http://localhost:8000/api/events/[EVENT_ID]/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "color": "vert_succes"
}
```

**Vérifications :**
- ✅ La couleur est mise à jour vers `#27ae60`
- ✅ La validation fonctionne lors de la mise à jour

### **Test 3.2 : Mise à Jour avec Couleur Hex**
```
PUT http://localhost:8000/api/events/[EVENT_ID]/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "color": "#9c27b0"
}
```

**Vérifications :**
- ✅ La couleur hex personnalisée est acceptée
- ✅ L'événement est mis à jour correctement

---

## 📊 **4. Tests de Récupération d'Événements**

### **Test 4.1 : Vérifier les Couleurs dans la Liste**
```
GET http://localhost:8000/api/events/
Authorization: Bearer [TOKEN]
```

**Vérifications :**
- ✅ Chaque événement a un champ `color` avec un code hex valide
- ✅ Les couleurs correspondent aux choix effectués

### **Test 4.2 : Vérifier les Couleurs dans les Détails**
```
GET http://localhost:8000/api/events/[EVENT_ID]/
Authorization: Bearer [TOKEN]
```

**Vérifications :**
- ✅ Le champ `color` est présent et correct
- ✅ La couleur est au format hexadécimal

---

## 🎨 **5. Tests avec Différents Types d'Utilisateurs**

### **Test 5.1 : Admin - Création d'Événement d'Équipe**
```
# Se connecter en tant qu'admin
POST http://localhost:8000/api/events/
Authorization: Bearer [ADMIN_TOKEN]
Content-Type: application/json

{
    "title": "Sprint Planning",
    "color": "turquoise_equipe",
    "team_id": "[TEAM_ID]",
    // ... autres champs
}
```

### **Test 5.2 : Employé - Création d'Événement Personnel**
```
# Se connecter en tant qu'employé
POST http://localhost:8000/api/personal-events/
Authorization: Bearer [EMPLOYEE_TOKEN]
Content-Type: application/json

{
    "title": "Pause déjeuner",
    "color": "rose_personnel",
    // ... autres champs
}
```

---

## 📋 **Résumé des Validations**

### ✅ **Fonctionnalités à Valider**
1. **Palette de couleurs** accessible via API
2. **Suggestion automatique** basée sur le titre
3. **Validation des couleurs** personnalisées
4. **Conversion automatique** nom → code hex
5. **Couleurs par défaut** si aucune couleur fournie
6. **Gestion d'erreurs** pour couleurs invalides
7. **Mise à jour** des couleurs existantes
8. **Compatibilité** avec événements personnels et d'équipe

### 🎯 **Points Clés**
- Les couleurs sont stockées en format hexadécimal dans la base
- La palette est extensible et configurable
- La validation empêche les couleurs invalides
- Les suggestions améliorent l'expérience utilisateur

**Prêt pour l'implémentation frontend !** 🚀
