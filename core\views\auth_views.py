from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import status
from datetime import datetime, timedelta
from ..mongo_models import User, BlacklistedToken
from ..utils.auth_utils import generate_reset_token, send_reset_password_email
import logging
import json

logger = logging.getLogger(__name__)

class MongoTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Sérialiseur personnalisé pour les tokens JWT avec MongoDB
    """
    @classmethod
    def get_token(cls, user):
        """
        Méthode pour créer un token JWT avec les informations de l'utilisateur
        """
        # Créer un token RefreshToken directement
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken()

        # Ajouter les claims personnalisés
        refresh['user_id'] = str(user.id)
        refresh['name'] = user.name
        refresh['email'] = user.email
        refresh['role'] = user.role

        return refresh

class RegisterView(APIView):
    permission_classes = []

    def post(self, request):
        try:
            data = json.loads(request.body)
            email = data.get('email')
            name = data.get('name')
            password = data.get('password')
            role = data.get('role', 'client')  # Par défaut, les nouveaux utilisateurs sont des clients

            # Validation des champs requis
            if not email or not name or not password:
                return Response({
                    'error': 'Email, name and password are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation du format de l'email
            if '@' not in email or '.' not in email:
                return Response({
                    'error': 'Invalid email format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation du nom (minimum 2 caractères)
            if len(name.strip()) < 2:
                return Response({
                    'error': 'Name must be at least 2 characters long'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation du mot de passe
            password_validation = self.validate_password(password)
            if not password_validation['valid']:
                return Response({
                    'error': password_validation['message']
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validation du rôle (seuls client, employee sont autorisés pour l'auto-inscription)
            if role not in ['client', 'employee']:
                return Response({
                    'error': 'Invalid role. Only "client" and "employee" are allowed for self-registration'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Vérifier si l'email existe déjà
            if User.objects(email=email).first():
                return Response({
                    'error': 'Email already exists'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Créer l'utilisateur avec son mot de passe personnel
            user = User(
                name=name.strip(),
                email=email.lower(),  # Normaliser l'email en minuscules
                role=role,
                temp_password_required=False
            )
            user.set_password(password)
            user.save()

            logger.info(f"Nouvel utilisateur inscrit: {email}, rôle: {role}")
            return Response({
                'message': 'User registered successfully. You can now login with your credentials.',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'name': user.name,
                    'role': user.role
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Erreur lors de l'inscription: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def validate_password(self, password):
        """
        Valide la complexité du mot de passe
        """
        if len(password) < 8:
            return {
                'valid': False,
                'message': 'Password must be at least 8 characters long'
            }

        if len(password) > 128:
            return {
                'valid': False,
                'message': 'Password must be less than 128 characters long'
            }

        # Vérifier qu'il contient au moins une lettre minuscule
        if not any(c.islower() for c in password):
            return {
                'valid': False,
                'message': 'Password must contain at least one lowercase letter'
            }

        # Vérifier qu'il contient au moins une lettre majuscule
        if not any(c.isupper() for c in password):
            return {
                'valid': False,
                'message': 'Password must contain at least one uppercase letter'
            }

        # Vérifier qu'il contient au moins un chiffre
        if not any(c.isdigit() for c in password):
            return {
                'valid': False,
                'message': 'Password must contain at least one digit'
            }

        # Vérifier qu'il contient au moins un caractère spécial
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return {
                'valid': False,
                'message': 'Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)'
            }

        return {'valid': True, 'message': 'Password is valid'}

class LoginView(APIView):
    permission_classes = []

    def post(self, request):
        try:
            # Récupérer les données de la requête
            email = request.data.get('email')
            password = request.data.get('password')

            # Vérifier que les champs requis sont présents
            if not email or not password:
                logger.warning(f"Tentative de connexion sans email ou mot de passe")
                return Response({'error': 'Email and password required'}, status=400)

            # Rechercher l'utilisateur par email
            user = User.objects.filter(email=email).first()

            # Vérifier que l'utilisateur existe
            if not user:
                logger.warning(f"Tentative de connexion avec un email inexistant: {email}")
                return Response({'error': 'Invalid credentials'}, status=400)

            # Vérifier le mot de passe
            if not user.check_password(password):
                logger.warning(f"Mot de passe incorrect pour: {email}")
                return Response({'error': 'Identifiants invalides'}, status=400)

            # Mettre à jour la date de dernière connexion
            user.update_last_login()

            # Enregistrer la connexion dans le tracker quotidien
            from ..models.bi_model import DailyLoginTracker
            try:
                DailyLoginTracker.add_user_login(str(user.id))
                logger.info(f"Connexion enregistrée dans le tracker quotidien pour: {email}")
            except Exception as tracker_error:
                logger.error(f"Erreur lors de l'enregistrement dans le tracker quotidien: {str(tracker_error)}")
                # Ne pas bloquer la connexion si le tracker échoue

            logger.info(f"Connexion réussie pour: {email}, rôle: {user.role}")

            # Gestion des mots de passe temporaires
            if user.temp_password_required:
                logger.info(f"Utilisateur avec mot de passe temporaire: {user.email}, rôle: {user.role}")

                # Vérifier si le mot de passe temporaire a déjà été utilisé (sauf pour super_admin)
                if user.temp_password_used and user.role != 'super_admin':
                    logger.warning(f"Mot de passe temporaire déjà utilisé pour: {user.email}")
                    return Response({
                        'error': 'Ce mot de passe temporaire a déjà été utilisé. Veuillez réinitialiser votre mot de passe.',
                        'should_reset': True
                    }, status=400)

                # Marquer le mot de passe temporaire comme utilisé
                user.temp_password_used = True
                user.save()
                temp_password_required = True
                logger.info(f"Connexion avec mot de passe temporaire réussie pour: {user.email}")
            else:
                temp_password_required = False
                logger.info(f"Connexion standard réussie pour: {user.email}")

            # Créer un token JWT avec les informations de l'utilisateur
            try:
                # Créer directement un token avec notre méthode personnalisée
                refresh = MongoTokenObtainPairSerializer.get_token(user)

                # Ajouter le flag temp_password_required
                refresh['temp_password_required'] = temp_password_required

                # Créer la réponse avec les tokens et les informations de l'utilisateur
                response_data = {
                    'access': str(refresh.access_token),
                    'refresh': str(refresh),
                    'user': {
                        'id': str(user.id),
                        'email': user.email,
                        'name': user.name,
                        'role': user.role,
                        'temp_password_required': temp_password_required
                    }
                }

                logger.info(f"Token généré avec succès pour: {user.email}")
                return Response(response_data)
            except Exception as token_error:
                logger.error(f"Erreur lors de la génération du token pour {user.email}: {str(token_error)}")
                return Response({'error': 'Erreur lors de la génération du token'}, status=500)
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la connexion: {str(e)}")
            return Response({'error': str(e)}, status=500)

class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            return Response({'message': 'Successfully logged out'}, status=200)
        except Exception as e:
            return Response({'error': str(e)}, status=400)

class RefreshTokenView(APIView):
    permission_classes = []

    def post(self, request):
        try:
            # Récupérer le refresh token de la requête
            refresh_token = request.data.get('refresh')

            # Vérifier que le refresh token est présent
            if not refresh_token:
                logger.warning("Tentative de rafraîchissement sans token")
                return Response({'error': 'Refresh token required'}, status=400)

            try:
                # Valider le refresh token
                token = RefreshToken(refresh_token)

                # Vérifier si le token est blacklisté
                if BlacklistedToken.objects.filter(token=refresh_token).exists():
                    logger.warning(f"Tentative d'utilisation d'un token blacklisté")
                    return Response({'error': 'Token has been blacklisted'}, status=400)

                # Récupérer l'ID de l'utilisateur à partir du token
                user_id = token.payload.get('user_id')

                if not user_id:
                    logger.warning("Token de rafraîchissement sans user_id")
                    return Response({'error': 'Invalid token payload'}, status=400)

                # Vérifier que l'utilisateur existe toujours
                try:
                    user = User.objects.get(id=user_id)
                    logger.info(f"Rafraîchissement de token pour: {user.email}")
                except User.DoesNotExist:
                    logger.warning(f"Tentative de rafraîchissement pour un utilisateur inexistant: {user_id}")
                    return Response({'error': 'User not found'}, status=400)

                # Créer un nouveau token avec notre méthode personnalisée
                new_refresh = MongoTokenObtainPairSerializer.get_token(user)

                # Générer un nouveau access token
                access_token = str(new_refresh.access_token)
                logger.info(f"Token rafraîchi avec succès pour: {user.email}")

                return Response({'access': access_token})
            except Exception as token_error:
                logger.error(f"Erreur lors de la validation du refresh token: {str(token_error)}")
                return Response({'error': 'Invalid refresh token'}, status=400)
        except Exception as e:
            logger.error(f"Erreur inattendue lors du rafraîchissement du token: {str(e)}")
            return Response({'error': str(e)}, status=500)

class ProtectedView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user_id = request.auth.payload.get('user_id')
            if not user_id:
                return Response({'error': 'Invalid token payload'}, status=400)

            user = User.objects.get(id=user_id)

            return Response({
                'message': 'Authenticated!',
                'user': {
                    'name': user.name,
                    'email': user.email,
                    'role': user.role
                }
            })
        except User.DoesNotExist:
            return Response({'error': 'User not found in database'}, status=404)
        except Exception as e:
            logger.error(f"ProtectedView error: {str(e)}", exc_info=True)
            return Response({'error': 'Internal server error'}, status=500)

class RequestPasswordResetView(APIView):
    permission_classes = []

    def post(self, request):
        try:
            email = request.data.get('email')
            if not email:
                return Response({'error': 'Email is required'}, status=400)

            # Valider le format de l'email
            if '@' not in email or '.' not in email:
                return Response({'error': 'Invalid email format'}, status=400)

            user = User.objects.filter(email=email).first()
            if not user:
                logger.warning(f"Tentative de réinitialisation pour un email inexistant: {email}")
                # Pour des raisons de sécurité, ne pas révéler si l'email existe ou non
                return Response({'message': 'If this email is associated with an account, a reset link will be sent'}, status=200)

            # Vérifier si l'utilisateur est un super_admin
            if user.role == 'super_admin':
                logger.info(f"Demande de réinitialisation pour un super_admin: {email}")
                # Le super_admin peut réinitialiser son mot de passe comme les autres utilisateurs

            # Générer un token de réinitialisation
            reset_token = generate_reset_token()
            user.password_reset_token = reset_token
            user.password_reset_expires = datetime.now() + timedelta(hours=24)
            user.save()

            # Envoyer l'email avec le lien de réinitialisation de manière asynchrone
            def send_email_async(email, token, name):
                try:
                    send_reset_password_email(email, token, name)
                    logger.info(f"Email de réinitialisation envoyé à: {email}")
                except Exception as email_error:
                    logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation: {str(email_error)}")

            # Lancer l'envoi d'email en arrière-plan
            import threading
            email_thread = threading.Thread(
                target=send_email_async,
                args=(email, reset_token, user.name)
            )
            email_thread.daemon = True
            email_thread.start()

            # Répondre immédiatement sans attendre l'envoi de l'email
            return Response({'message': 'If this email is associated with an account, a reset link will be sent'}, status=200)

        except Exception as e:
            logger.error(f"Erreur lors de la demande de réinitialisation: {str(e)}")
            return Response({'error': 'An error occurred while processing your request'}, status=500)

class ResetPasswordView(APIView):
    permission_classes = []

    def post(self, request, token):
        try:
            logger.info(f"Tentative de réinitialisation du mot de passe avec le token: {token}")

            new_password = request.data.get('new_password')
            if not new_password:
                logger.warning("Tentative de réinitialisation sans nouveau mot de passe")
                return Response({'error': 'Le nouveau mot de passe est requis'}, status=400)

            # Vérifier la complexité du mot de passe
            if len(new_password) < 8:
                return Response({'error': 'Le mot de passe doit contenir au moins 8 caractères'}, status=400)

            # Rechercher l'utilisateur avec le token fourni
            user = User.objects.filter(password_reset_token=token).first()
            if not user:
                logger.warning(f"Token de réinitialisation invalide: {token}")
                return Response({'error': 'Token de réinitialisation invalide ou expiré'}, status=400)

            logger.info(f"Réinitialisation de mot de passe pour: {user.email}, rôle: {user.role}")

            # Vérifier si l'utilisateur est un super_admin
            if user.role == 'super_admin':
                logger.info(f"Réinitialisation de mot de passe pour un super_admin: {user.email}")
                # Le super_admin peut réinitialiser son mot de passe comme les autres utilisateurs

            # Vérifier si le token a expiré
            if not user.password_reset_expires or user.password_reset_expires < datetime.now():
                logger.warning(f"Token expiré pour l'utilisateur: {user.email}")
                # Réinitialiser le token expiré
                user.password_reset_token = None
                user.password_reset_expires = None
                user.save()
                return Response({'error': 'Le token de réinitialisation a expiré. Veuillez demander un nouveau lien.'}, status=400)

            try:
                # Mettre à jour le mot de passe et réinitialiser les champs associés
                user.set_password(new_password)
                user.password_reset_token = None
                user.password_reset_expires = None
                user.temp_password_required = False
                user.temp_password_used = False

                # Sauvegarder les modifications
                user.save()

                logger.info(f"Mot de passe réinitialisé avec succès pour: {user.email}")
                return Response({
                    'message': 'Mot de passe réinitialisé avec succès',
                    'user_email': user.email  # Retourner l'email pour faciliter la connexion
                })

            except Exception as save_error:
                logger.error(f"Erreur lors de la sauvegarde du nouveau mot de passe: {str(save_error)}")
                return Response({'error': 'Erreur lors de la réinitialisation du mot de passe'}, status=500)

        except Exception as e:
            logger.error(f"Erreur inattendue lors de la réinitialisation du mot de passe: {str(e)}")
            return Response({'error': str(e)}, status=500)