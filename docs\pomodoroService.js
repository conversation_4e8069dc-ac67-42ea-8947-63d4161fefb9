/**
 * Service de gestion du mode Pomodoro pour le frontend
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/src/services/
 */

import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Configuration des en-têtes avec le token d'authentification
const getConfig = () => {
  const token = localStorage.getItem('access_token');
  return {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };
};

/**
 * Récupère les paramètres Pomodoro de l'utilisateur
 * @returns {Promise} - Promise contenant les paramètres Pomodoro
 */
export const getPomodoroSettings = async () => {
  try {
    const response = await axios.get(`${API_URL}/pomodoro/settings/`, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des paramètres Pomodoro:', error);
    throw error;
  }
};

/**
 * Met à jour les paramètres Pomodoro de l'utilisateur
 * @param {Object} settings - Nouveaux paramètres Pomodoro
 * @returns {Promise} - Promise contenant les paramètres mis à jour
 */
export const updatePomodoroSettings = async (settings) => {
  try {
    const response = await axios.put(`${API_URL}/pomodoro/settings/`, settings, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise à jour des paramètres Pomodoro:', error);
    throw error;
  }
};

/**
 * Démarre une nouvelle session Pomodoro
 * @returns {Promise} - Promise contenant les informations de la session
 */
export const startPomodoroSession = async () => {
  try {
    const response = await axios.post(`${API_URL}/pomodoro/control/start/`, {}, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors du démarrage de la session Pomodoro:', error);
    throw error;
  }
};

/**
 * Met en pause la session Pomodoro en cours
 * @returns {Promise} - Promise contenant les informations de la session
 */
export const pausePomodoroSession = async () => {
  try {
    const response = await axios.post(`${API_URL}/pomodoro/control/pause/`, {}, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la mise en pause de la session Pomodoro:', error);
    throw error;
  }
};

/**
 * Reprend la session Pomodoro en pause
 * @returns {Promise} - Promise contenant les informations de la session
 */
export const resumePomodoroSession = async () => {
  try {
    const response = await axios.post(`${API_URL}/pomodoro/control/resume/`, {}, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la reprise de la session Pomodoro:', error);
    throw error;
  }
};

/**
 * Termine la session Pomodoro en cours
 * @returns {Promise} - Promise contenant les informations de la session
 */
export const completePomodoroSession = async () => {
  try {
    const response = await axios.post(`${API_URL}/pomodoro/control/complete/`, {}, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la finalisation de la session Pomodoro:', error);
    throw error;
  }
};

/**
 * Réinitialise la session Pomodoro en cours
 * @returns {Promise} - Promise contenant les informations de la session
 */
export const resetPomodoroSession = async () => {
  try {
    const response = await axios.post(`${API_URL}/pomodoro/control/reset/`, {}, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la réinitialisation de la session Pomodoro:', error);
    throw error;
  }
};

/**
 * Utilitaire pour formater le temps en minutes:secondes
 * @param {number} seconds - Nombre de secondes
 * @returns {string} - Temps formaté (MM:SS)
 */
export const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

/**
 * Utilitaire pour calculer le pourcentage de progression
 * @param {number} elapsed - Temps écoulé en secondes
 * @param {number} total - Temps total en secondes
 * @returns {number} - Pourcentage de progression (0-100)
 */
export const calculateProgress = (elapsed, total) => {
  if (total === 0) return 0;
  return Math.min(100, Math.max(0, (elapsed / total) * 100));
};

/**
 * Utilitaire pour obtenir le type de session suivante
 * @param {number} sessionCount - Nombre de sessions complétées
 * @param {number} sessionsBeforeLongBreak - Nombre de sessions avant une pause longue
 * @returns {string} - Type de session ('focus', 'short_break', 'long_break')
 */
export const getNextSessionType = (sessionCount, sessionsBeforeLongBreak = 4) => {
  if (sessionCount % (sessionsBeforeLongBreak * 2) === sessionsBeforeLongBreak * 2 - 1) {
    return 'long_break';
  } else if (sessionCount % 2 === 0) {
    return 'focus';
  } else {
    return 'short_break';
  }
};

/**
 * Utilitaire pour jouer un son de notification
 * @param {string} soundType - Type de son ('start', 'pause', 'complete')
 */
export const playNotificationSound = (soundType = 'complete') => {
  try {
    // Créer un contexte audio simple
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Différentes fréquences selon le type de notification
    switch (soundType) {
      case 'start':
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        break;
      case 'pause':
        oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
        break;
      case 'complete':
        oscillator.frequency.setValueAtTime(1000, audioContext.currentTime);
        break;
      default:
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
    }
    
    oscillator.type = 'sine';
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  } catch (error) {
    console.log('Impossible de jouer le son de notification:', error);
  }
};

/**
 * Utilitaire pour envoyer une notification du navigateur
 * @param {string} title - Titre de la notification
 * @param {string} body - Corps de la notification
 * @param {string} icon - URL de l'icône (optionnel)
 */
export const sendBrowserNotification = (title, body, icon = null) => {
  if ('Notification' in window && Notification.permission === 'granted') {
    new Notification(title, {
      body,
      icon,
      badge: icon
    });
  } else if ('Notification' in window && Notification.permission !== 'denied') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        new Notification(title, {
          body,
          icon,
          badge: icon
        });
      }
    });
  }
};

/**
 * Demande la permission pour les notifications du navigateur
 * @returns {Promise<string>} - Permission accordée ou refusée
 */
export const requestNotificationPermission = async () => {
  if ('Notification' in window) {
    return await Notification.requestPermission();
  }
  return 'denied';
};
