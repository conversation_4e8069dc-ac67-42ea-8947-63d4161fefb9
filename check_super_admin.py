#!/usr/bin/env python
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from core.mongo_models import User

def check_super_admin():
    """Vérifier le super admin et son mot de passe"""
    super_admin = User.objects.filter(email="<EMAIL>").first()
    
    if super_admin:
        print(f"Super admin trouvé: {super_admin.name}")
        print(f"Email: {super_admin.email}")
        print(f"Role: {super_admin.role}")
        print(f"Temp password required: {super_admin.temp_password_required}")
        
        # Testons quelques mots de passe possibles
        passwords_to_test = [
            "SuperAdmin123!",
            "syrinenotora123",
            "admin123",
            "password123",
            "Syrine123!",
            "SyrineNotora123!",
            "superadmin",
            "123456789"
        ]
        
        print("\nTest des mots de passe...")
        for pwd in passwords_to_test:
            if super_admin.check_password(pwd):
                print(f"✅ Mot de passe trouvé: {pwd}")
                return pwd
        
        print("❌ Aucun des mots de passe testés ne fonctionne")
        print("Le super admin existe mais nous ne connaissons pas son mot de passe.")
        
        # Proposer de réinitialiser le mot de passe
        reset = input("\nVoulez-vous réinitialiser le mot de passe du super admin? (oui/non): ")
        if reset.lower() in ['oui', 'o', 'yes', 'y']:
            new_password = input("Entrez le nouveau mot de passe: ")
            super_admin.set_password(new_password)
            super_admin.temp_password_required = False
            super_admin.save()
            print(f"✅ Mot de passe du super admin réinitialisé: {new_password}")
            return new_password
    else:
        print("❌ Super admin non trouvé")
        
        # Proposer de créer le super admin
        create = input("Voulez-vous créer le super admin? (oui/non): ")
        if create.lower() in ['oui', 'o', 'yes', 'y']:
            name = input("Nom du super admin (défaut: Syrine Notora): ") or "Syrine Notora"
            password = input("Mot de passe du super admin: ")
            
            super_admin = User(
                name=name,
                email="<EMAIL>",
                role="super_admin",
                temp_password_required=False
            )
            super_admin.set_password(password)
            super_admin.save()
            print(f"✅ Super admin créé avec succès: {super_admin.email}")
            return password
    
    return None

if __name__ == "__main__":
    check_super_admin()
