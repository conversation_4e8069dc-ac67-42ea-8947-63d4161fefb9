from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import User, Team
from ..models.event_model import Event
import logging

logger = logging.getLogger(__name__)

class EventUnarchiveView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, event_id):
        """Désarchiver un événement (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer l'événement
            event = Event.objects.get(id=event_id)
            user = request.user
            
            # Vérifier si l'admin a le droit de gérer cet événement
            # ou si l'admin est celui qui a créé l'événement
            if not event.can_manage_event(user) and event.created_by != str(user.id):
                return Response({
                    "error": "Vous n'êtes pas autorisé à désarchiver cet événement. Seul l'administrateur responsable de l'équipe associée peut le faire."
                }, status=403)
            
            # Vérifier que l'événement est bien archivé
            if event.status != 'archived':
                return Response({
                    "error": "Cet événement n'est pas archivé et ne peut donc pas être désarchivé."
                }, status=400)
            
            # Désarchiver l'événement (remettre à l'état "pending")
            event.status = 'pending'
            event.save()
            
            return Response({
                "message": "Événement désarchivé avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "status": event.status,
                    "updated_at": event.updated_at
                }
            })
            
        except Event.DoesNotExist:
            return Response({"error": "Événement non trouvé"}, status=404)
        except Exception as e:
            logger.error(f"Error in EventUnarchiveView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors du désarchivage de l'événement"}, status=500)
