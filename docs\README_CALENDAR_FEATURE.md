# Fonctionnalité de Calendrier et Événements

Ce document décrit l'implémentation de la fonctionnalité de calendrier et d'événements pour le sprint 3 de l'application.

## Architecture

### Backend

#### Modèle de données

Le modèle `Event` a été créé avec les champs suivants :
- `id` : ObjectId (identifiant unique)
- `title` : Titre de l'événement (obligatoire)
- `description` : Description d<PERSON>tail<PERSON> (optionnel)
- `start_date` et `end_date` : Dates de début et de fin (obligatoires)
- `start_time` et `end_time` : Heures de début et de fin (obligatoires)
- `note` : Notes additionnelles (optionnel)
- `status` : Statut de l'événement (pending, completed, archived)
- `team_id` : ID de l'équipe assignée (si applicable)
- `member_id` : ID du membre assigné (si applicable)
- `created_by` : ID de l'admin qui a créé l'événement
- `created_at` et `updated_at` : Dates de création et de mise à jour

#### API REST

Les endpoints suivants ont été implémentés :

1. **Gestion des événements (admin uniquement)**
   - `GET /api/events/` : Liste de tous les événements
   - `POST /api/events/` : Création d'un nouvel événement
   - `GET /api/events/{id}/` : Détails d'un événement spécifique
   - `PUT /api/events/{id}/` : Mise à jour d'un événement
   - `DELETE /api/events/{id}/` : Suppression d'un événement
   - `PUT /api/events/{id}/archive/` : Archivage d'un événement

2. **Fonctionnalités pour les membres**
   - `GET /api/events/` : Liste des événements assignés au membre ou à ses équipes
   - `GET /api/events/{id}/` : Détails d'un événement assigné
   - `PUT /api/events/{id}/status/` : Mise à jour du statut d'un événement (completed/pending)

#### Système de permissions

Les permissions suivantes ont été implémentées :

- **Administrateurs** : Peuvent créer, modifier, supprimer et archiver tous les événements
- **Membres (employés)** : Peuvent consulter les événements qui leur sont assignés ou assignés à leurs équipes, et mettre à jour le statut de ces événements

### Frontend

#### Composants React

1. **CalendarView** : Composant principal qui affiche le calendrier avec les événements
   - Utilise la bibliothèque `react-big-calendar` pour l'affichage
   - Affiche les événements avec des couleurs différentes selon leur statut
   - Permet aux admins de créer, modifier et supprimer des événements
   - Permet aux membres de mettre à jour le statut des événements

2. **EventModal** : Modal pour afficher, créer ou modifier un événement
   - Affiche différentes options selon le rôle de l'utilisateur
   - Pour les admins : formulaire complet d'édition
   - Pour les membres : vue détaillée avec option de mise à jour du statut

#### Contexte React

Un contexte `EventContext` a été créé pour gérer l'état global des événements :
- Chargement des événements
- Création, modification et suppression d'événements
- Mise à jour du statut
- Gestion des erreurs et du chargement

#### Service API

Le service `eventService.js` fournit des fonctions pour interagir avec l'API backend :
- `getEvents()` : Récupère tous les événements
- `getEventById(id)` : Récupère un événement spécifique
- `createEvent(eventData)` : Crée un nouvel événement
- `updateEvent(id, eventData)` : Met à jour un événement
- `deleteEvent(id)` : Supprime un événement
- `updateEventStatus(id, status)` : Met à jour le statut d'un événement
- `archiveEvent(id)` : Archive un événement

## Intégration

### Dans le backend

1. Le modèle `Event` a été ajouté aux modèles MongoDB
2. Les vues pour gérer les événements ont été créées
3. Les routes API ont été ajoutées au fichier urls.py

### Dans le frontend

1. Ajouter le service `eventService.js` dans le dossier `services`
2. Ajouter le contexte `EventContext.jsx` dans le dossier `contexts`
3. Ajouter le composant `CalendarView.jsx` dans le dossier `components/calendar`
4. Intégrer le calendrier dans les interfaces admin et membre

## Tests

Un document de test Postman a été créé pour tester toutes les fonctionnalités de l'API d'événements. Il inclut :

1. Tests d'authentification
2. Tests de création, lecture, mise à jour et suppression d'événements (CRUD)
3. Tests de mise à jour de statut
4. Tests de permissions (admin vs membre)
5. Scénarios de test complets

Consultez le fichier `calendar_events_tests.md` pour les détails des tests à effectuer.

## Prochaines étapes

1. Ajouter des notifications pour les événements à venir
2. Implémenter des filtres pour afficher les événements par équipe ou par statut
3. Ajouter des fonctionnalités de récurrence pour les événements périodiques
4. Intégrer un système de rappels par email