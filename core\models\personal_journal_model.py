from mongoengine import Document, <PERSON><PERSON>ield, DateTimeField, <PERSON><PERSON>an<PERSON>ield
from datetime import datetime, timezone
from bson import ObjectId

class PersonalJournal(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    content = StringField(required=True)
    entry_date = DateTimeField(required=True)  # Date de l'entrée du journal
    created_by = StringField(required=True)  # ID de l'utilisateur qui a créé l'entrée (employé ou client)
    created_by_name = StringField(required=False)  # Nom de l'utilisateur qui a créé l'entrée
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))
    is_archived = BooleanField(default=False)  # Indique si l'entrée est archivée
    
    meta = {
        'collection': 'personal_journals',
        'indexes': [
            {'fields': ['created_by']},
            {'fields': ['entry_date']},
            {'fields': ['created_at']},
            {'fields': ['is_archived']}
        ]
    }
    
    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(PersonalJournal, self).save(*args, **kwargs)
    
    def can_manage_journal(self, user):
        """Vérifie si un utilisateur peut gérer cette entrée de journal personnelle"""
        # Un utilisateur ne peut gérer que ses propres entrées de journal
        return str(user.id) == self.created_by
