from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import User, Team
from ..models.team_task_model import TeamTask
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class TeamTaskListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request):
        """Créer une nouvelle tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de la tâche est requis"}, status=400)
            if not data.get('start_date') or not data.get('end_date'):
                return Response({"error": "Les dates de début et de fin sont requises"}, status=400)
            if not data.get('team_id'):
                return Response({"error": "L'ID de l'équipe est requis"}, status=400)
            if not data.get('responsable'):
                return Response({"error": "Le responsable de la tâche est requis"}, status=400)

            # Vérifier si une tâche avec le même titre existe déjà pour la même équipe
            if TeamTask.objects(title=data['title'], team_id=data['team_id']).first():
                return Response({"error": "Une tâche avec ce titre existe déjà pour cette équipe"}, status=400)

            # Vérifier que l'admin est responsable de l'équipe assignée
            try:
                team = Team.objects.get(id=data['team_id'])
                if team.responsable != str(user.id):
                    return Response({"error": "Vous n'êtes pas autorisé à créer une tâche pour cette équipe. Seul l'administrateur responsable de l'équipe peut le faire."}, status=403)
            except Team.DoesNotExist:
                return Response({"error": "Équipe non trouvée"}, status=404)
            except Exception as e:
                logger.error(f"Erreur lors de la vérification de l'équipe: {str(e)}")
                return Response({"error": "Une erreur est survenue lors de la vérification de l'équipe"}, status=500)

            # Vérifier que le responsable est un admin valide
            try:
                responsable = User.objects.get(id=data['responsable'])
                if responsable.role != 'admin':
                    return Response({"error": "Le responsable doit être un administrateur"}, status=400)
            except User.DoesNotExist:
                return Response({"error": "Responsable non trouvé"}, status=404)
            except Exception as e:
                logger.error(f"Erreur lors de la vérification du responsable: {str(e)}")
                return Response({"error": "Une erreur est survenue lors de la vérification du responsable"}, status=500)

            # Vérifier que les dates sont valides (pas dans le passé)
            try:
                start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
                now = datetime.now(timezone.utc)

                if start_date.date() < now.date():
                    return Response({"error": "La date de début ne peut pas être dans le passé"}, status=400)
                if end_date.date() < start_date.date():
                    return Response({"error": "La date de fin ne peut pas être antérieure à la date de début"}, status=400)
            except ValueError:
                return Response({"error": "Format de date invalide"}, status=400)

            # Créer la tâche d'équipe
            task = TeamTask(
                title=data['title'],
                description=data.get('description', ''),
                start_date=start_date,
                end_date=end_date,
                status='a_faire',
                priority=data.get('priority', 'moyenne'),
                team_id=data['team_id'],
                team_name=team.name,
                member_id=data.get('member_id'),
                responsable=data['responsable'],
                responsable_name=responsable.name,
                created_by=str(user.id),
                created_by_name=user.name,
                display_mode=data.get('display_mode', 'list')
            )
            task.save()

            return Response({
                "message": "Tâche d'équipe créée avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "description": task.description,
                    "start_date": task.start_date,
                    "end_date": task.end_date,
                    "status": task.status,
                    "priority": task.priority,
                    "team_id": task.team_id,
                    "team_name": task.team_name,
                    "member_id": task.member_id,
                    "responsable": task.responsable,
                    "responsable_name": task.responsable_name,
                    "created_by": task.created_by,
                    "created_by_name": task.created_by_name,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,
                    "display_mode": task.display_mode
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in TeamTaskListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de la tâche d'équipe"}, status=500)

    def get(self, request):
        """Liste des tâches d'équipe (filtrée selon le rôle et les permissions)"""
        try:
            user = request.user
            status_filter = request.query_params.get('status')

            # Filtrer les tâches selon le rôle de l'utilisateur
            if user.role == 'admin':
                # Les admins voient uniquement les tâches des équipes dont ils sont responsables
                admin_teams = Team.objects(responsable=str(user.id)).values_list('id')
                admin_team_ids = [str(team_id) for team_id in admin_teams]

                # Récupérer les membres de ces équipes
                team_members = []
                for team_id in admin_team_ids:
                    try:
                        team = Team.objects.get(id=team_id)
                        team_members.extend(list(team.members.keys()))
                    except Exception:
                        continue

                # Filtrer les tâches
                if admin_team_ids:
                    tasks = TeamTask.objects().filter(__raw__={'$or': [
                        {'team_id': {'$in': admin_team_ids}},
                        {'member_id': {'$in': team_members}}
                    ]})
                else:
                    # Si l'admin n'est responsable d'aucune équipe, il ne voit aucune tâche
                    tasks = TeamTask.objects().none()
            elif user.role == 'employee':
                # Les employés voient les tâches qui leur sont assignées ou assignées à leurs équipes
                user_id = str(user.id)

                # Récupérer les équipes dont l'utilisateur est membre
                user_teams = Team.objects(members__has_key=user_id).values_list('id')
                team_ids = [str(team_id) for team_id in user_teams]

                # Filtrer les tâches
                tasks = TeamTask.objects()
                if team_ids:
                    tasks = tasks.filter(__raw__={'$or': [
                        {'member_id': user_id},
                        {'team_id': {'$in': team_ids}}
                    ]})
                else:
                    tasks = tasks.filter(member_id=user_id)
            else:
                # Les autres rôles n'ont pas accès aux tâches d'équipe
                return Response({"error": "Vous n'avez pas les droits pour voir les tâches d'équipe"}, status=403)

            # Filtrer par statut si spécifié
            if status_filter:
                tasks = tasks.filter(status=status_filter)

            # Formater les tâches
            tasks_data = []
            for task in tasks:
                tasks_data.append({
                    'id': str(task.id),
                    'title': task.title,
                    'description': task.description,
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'status': task.status,
                    'priority': task.priority,
                    'team_id': task.team_id,
                    'team_name': task.team_name,
                    'member_id': task.member_id,
                    'responsable': task.responsable,
                    'responsable_name': task.responsable_name,
                    'created_by': task.created_by,
                    'created_by_name': task.created_by_name,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'display_mode': task.display_mode,
                    'can_manage': task.can_manage_task(user),
                    'can_update_status': task.can_update_status(user)
                })

            return Response(tasks_data)

        except Exception as e:
            logger.error(f"Error in TeamTaskListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class TeamTaskDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, task_id):
        """Récupérer les détails d'une tâche d'équipe"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier les droits d'accès
            if user.role == 'admin':
                # Les admins ont accès uniquement aux tâches des équipes dont ils sont responsables
                if task.team_id:
                    try:
                        team = Team.objects.get(id=task.team_id)
                        if str(user.id) != team.responsable:
                            return Response({"error": "Vous n'avez pas les droits pour voir cette tâche. Seul l'administrateur responsable de l'équipe peut y accéder."}, status=403)
                    except Team.DoesNotExist:
                        return Response({"error": "Équipe non trouvée"}, status=404)
                else:
                    # Si la tâche n'est associée à aucune équipe, l'admin ne peut pas y accéder
                    return Response({"error": "Vous n'avez pas les droits pour voir cette tâche."}, status=403)
            elif user.role == 'employee':
                # Les employés ont accès aux tâches qui leur sont assignées ou assignées à leurs équipes
                user_id = str(user.id)

                # Si la tâche est assignée à un membre spécifique
                if task.member_id and task.member_id != user_id:
                    return Response({"error": "Vous n'avez pas les droits pour voir cette tâche"}, status=403)

                # Si la tâche est assignée à une équipe
                if task.team_id:
                    team = Team.objects.get(id=task.team_id)
                    if user_id not in team.members:
                        return Response({"error": "Vous n'avez pas les droits pour voir cette tâche"}, status=403)
            else:
                # Les autres rôles n'ont pas accès aux tâches d'équipe
                return Response({"error": "Vous n'avez pas les droits pour voir les tâches d'équipe"}, status=403)

            # Retourner les détails de la tâche
            return Response({
                'id': str(task.id),
                'title': task.title,
                'description': task.description,
                'start_date': task.start_date,
                'end_date': task.end_date,
                'status': task.status,
                'priority': task.priority,
                'team_id': task.team_id,
                'team_name': task.team_name,
                'member_id': task.member_id,
                'responsable': task.responsable,
                'responsable_name': task.responsable_name,
                'created_by': task.created_by,
                'created_by_name': task.created_by_name,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'display_mode': task.display_mode,
                'can_manage': task.can_manage_task(user),
                'can_update_status': task.can_update_status(user)
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la récupération des détails de la tâche d'équipe"}, status=500)

    @admin_required
    def put(self, request, task_id):
        """Modifier une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            data = request.data

            # Mettre à jour les champs
            if 'title' in data:
                # Vérifier si une autre tâche avec ce titre existe déjà pour la même équipe
                if data['title'] != task.title and TeamTask.objects(title=data['title'], team_id=task.team_id).first():
                    return Response({"error": "Une tâche avec ce titre existe déjà pour cette équipe"}, status=400)
                task.title = data['title']
            if 'description' in data:
                task.description = data['description']
            if 'start_date' in data:
                task.start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
            if 'end_date' in data:
                task.end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
            if 'priority' in data and data['priority'] in ['faible', 'moyenne', 'haute']:
                task.priority = data['priority']
            if 'team_id' in data:
                # Vérifier que l'admin est responsable de la nouvelle équipe
                try:
                    team = Team.objects.get(id=data['team_id'])
                    if team.responsable != str(user.id):
                        return Response({"error": "Vous n'êtes pas autorisé à assigner cette tâche à cette équipe. Seul l'administrateur responsable de l'équipe peut le faire."}, status=403)
                    task.team_id = data['team_id']
                    task.team_name = team.name
                except Team.DoesNotExist:
                    return Response({"error": "Équipe non trouvée"}, status=404)
            if 'member_id' in data:
                task.member_id = data['member_id']
            if 'responsable' in data:
                # Vérifier que le nouveau responsable est un admin valide
                try:
                    responsable = User.objects.get(id=data['responsable'])
                    if responsable.role != 'admin':
                        return Response({"error": "Le responsable doit être un administrateur"}, status=400)
                    task.responsable = data['responsable']
                    task.responsable_name = responsable.name
                except User.DoesNotExist:
                    return Response({"error": "Responsable non trouvé"}, status=404)
            if 'display_mode' in data and data['display_mode'] in ['list', 'card', 'kanban']:
                task.display_mode = data['display_mode']

            # Sauvegarder les modifications
            task.save()

            return Response({
                "message": "Tâche d'équipe mise à jour avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "description": task.description,
                    "start_date": task.start_date,
                    "end_date": task.end_date,
                    "status": task.status,
                    "priority": task.priority,
                    "team_id": task.team_id,
                    "team_name": task.team_name,
                    "member_id": task.member_id,
                    "responsable": task.responsable,
                    "responsable_name": task.responsable_name,
                    "created_by": task.created_by,
                    "created_by_name": task.created_by_name,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,
                    "display_mode": task.display_mode
                }
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour de la tâche d'équipe"}, status=500)

    def patch(self, request, task_id):
        """Mettre à jour le statut d'une tâche d'équipe (membre assigné uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'utilisateur a le droit de mettre à jour le statut
            if not task.can_update_status(user):
                return Response({"error": "Vous n'êtes pas autorisé à mettre à jour le statut de cette tâche. Seul le membre assigné ou un membre de l'équipe peut le faire."}, status=403)

            data = request.data

            # Mettre à jour le statut
            if 'status' in data and data['status'] in ['a_faire', 'en_cours', 'en_revision', 'achevee']:
                task.status = data['status']
                task.save()

                return Response({
                    "message": "Statut de la tâche d'équipe mis à jour avec succès",
                    "status": task.status
                })
            else:
                return Response({"error": "Statut invalide. Les valeurs autorisées sont: a_faire, en_cours, en_revision, achevee"}, status=400)

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.patch: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour du statut de la tâche d'équipe"}, status=500)

    @admin_required
    def delete(self, request, task_id):
        """Supprimer une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            # ou si l'admin est celui qui a créé la tâche
            if not task.can_manage_task(user) and task.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Supprimer la tâche
            task.delete()

            return Response({"message": "Tâche d'équipe supprimée avec succès"}, status=200)

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.delete: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression de la tâche d'équipe"}, status=500)

class TeamTaskArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, task_id):
        """Archiver une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            # ou si l'admin est celui qui a créé la tâche
            if not task.can_manage_task(user) and task.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Archiver la tâche
            task.status = 'archived'
            task.save()

            return Response({
                "message": "Tâche d'équipe archivée avec succès",
                "status": task.status
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskArchiveView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de l'archivage de la tâche d'équipe"}, status=500)