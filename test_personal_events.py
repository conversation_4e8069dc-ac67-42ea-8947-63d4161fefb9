#!/usr/bin/env python
"""
Script de test pour les événements personnels avec les bonnes dates
"""

import requests
import json
from datetime import datetime, timedelta

def test_personal_events():
    """Test complet des événements personnels"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test des événements personnels ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Générer des dates futures
    today = datetime.now()
    tomorrow = today + timedelta(days=1)
    next_week = today + timedelta(days=7)
    
    # 3. Test 1 : Événement basique
    print("\n2. Test 1 : Création d'un événement basique...")
    event_data_1 = {
        "title": "Rendez-vous médical",
        "start_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
        "end_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
        "start_time": "09:00",
        "end_time": "10:30"
    }
    
    print(f"📅 Date utilisée: {event_data_1['start_date']}")
    
    response_1 = requests.post(
        f"{base_url}/personal-events/",
        json=event_data_1,
        headers=headers
    )
    
    print(f"📡 Statut: {response_1.status_code}")
    if response_1.status_code == 201:
        data_1 = response_1.json()
        print("✅ Événement créé avec succès")
        print(f"   ID: {data_1['event']['id']}")
        print(f"   Titre: {data_1['event']['title']}")
        event_id_1 = data_1['event']['id']
    else:
        print(f"❌ Échec: {response_1.json()}")
        return
    
    # 4. Test 2 : Événement complet
    print("\n3. Test 2 : Création d'un événement complet...")
    event_data_2 = {
        "title": "Formation personnelle en développement",
        "description": "Session de formation sur les nouvelles technologies React et Node.js",
        "start_date": next_week.strftime("%Y-%m-%dT00:00:00Z"),
        "end_date": next_week.strftime("%Y-%m-%dT00:00:00Z"),
        "start_time": "14:00",
        "end_time": "17:00",
        "note": "Apporter un ordinateur portable et prendre des notes",
        "color": "#FF6B6B"
    }
    
    response_2 = requests.post(
        f"{base_url}/personal-events/",
        json=event_data_2,
        headers=headers
    )
    
    print(f"📡 Statut: {response_2.status_code}")
    if response_2.status_code == 201:
        data_2 = response_2.json()
        print("✅ Événement complet créé avec succès")
        print(f"   ID: {data_2['event']['id']}")
        print(f"   Couleur: {data_2['event']['color']}")
        event_id_2 = data_2['event']['id']
    else:
        print(f"❌ Échec: {response_2.json()}")
    
    # 5. Test 3 : Récupérer la liste des événements
    print("\n4. Test 3 : Récupération de la liste des événements...")
    list_response = requests.get(
        f"{base_url}/personal-events/",
        headers=headers
    )
    
    print(f"📡 Statut: {list_response.status_code}")
    if list_response.status_code == 200:
        events_list = list_response.json()
        print(f"✅ Liste récupérée: {len(events_list)} événement(s)")
        for event in events_list:
            print(f"   - {event['title']} ({event['status']})")
    else:
        print(f"❌ Échec: {list_response.json()}")
    
    # 6. Test 4 : Récupérer un événement spécifique
    print("\n5. Test 4 : Récupération d'un événement spécifique...")
    detail_response = requests.get(
        f"{base_url}/personal-events/{event_id_1}/",
        headers=headers
    )
    
    print(f"📡 Statut: {detail_response.status_code}")
    if detail_response.status_code == 200:
        event_detail = detail_response.json()
        print("✅ Détails récupérés avec succès")
        print(f"   Titre: {event_detail['title']}")
        print(f"   Peut gérer: {event_detail['can_manage']}")
    else:
        print(f"❌ Échec: {detail_response.json()}")
    
    # 7. Test 5 : Mettre à jour le statut
    print("\n6. Test 5 : Mise à jour du statut...")
    status_update = {"status": "completed"}
    
    status_response = requests.patch(
        f"{base_url}/personal-events/{event_id_1}/",
        json=status_update,
        headers=headers
    )
    
    print(f"📡 Statut: {status_response.status_code}")
    if status_response.status_code == 200:
        status_data = status_response.json()
        print("✅ Statut mis à jour avec succès")
        print(f"   Nouveau statut: {status_data['status']}")
    else:
        print(f"❌ Échec: {status_response.json()}")
    
    # 8. Test 6 : Test d'erreur - date dans le passé
    print("\n7. Test 6 : Test d'erreur - date dans le passé...")
    past_date = today - timedelta(days=1)
    error_event = {
        "title": "Événement passé",
        "start_date": past_date.strftime("%Y-%m-%dT00:00:00Z"),
        "end_date": past_date.strftime("%Y-%m-%dT00:00:00Z"),
        "start_time": "09:00",
        "end_time": "10:00"
    }
    
    error_response = requests.post(
        f"{base_url}/personal-events/",
        json=error_event,
        headers=headers
    )
    
    print(f"📡 Statut: {error_response.status_code}")
    if error_response.status_code == 400:
        error_data = error_response.json()
        print("✅ Gestion d'erreur correcte")
        print(f"   Message: {error_data['error']}")
    else:
        print(f"❌ Gestion d'erreur incorrecte: {error_response.json()}")
    
    print("\n" + "="*50)
    print("🎯 Tests terminés avec succès !")
    print("="*50)

if __name__ == "__main__":
    test_personal_events()
