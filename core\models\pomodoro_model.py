from mongoengine import Document, <PERSON><PERSON>ield, IntField, DateTimeField, BooleanField, EmbeddedDocument, EmbeddedDocumentField, ListField
from datetime import datetime, timezone
from bson import ObjectId

class PomodoroSession(EmbeddedDocument):
    session_id = StringField(default=lambda: str(ObjectId()))
    start_time = DateTimeField()
    end_time = DateTimeField()
    focus_duration = IntField()  # en minutes
    break_duration = IntField()  # en minutes
    completed = BooleanField(default=False)
    paused_at = DateTimeField()
    total_pause_time = IntField(default=0)  # en secondes

class PomodoroSettings(Document):
    user_id = StringField(required=True, unique=True)
    is_active = BooleanField(default=False)
    focus_duration = IntField(default=25)  # en minutes
    short_break_duration = IntField(default=5)  # en minutes
    long_break_duration = IntField(default=15)  # en minutes
    sessions_before_long_break = IntField(default=4)
    current_session = EmbeddedDocumentField(PomodoroSession)
    session_history = ListField(EmbeddedDocumentField(PomodoroSession))
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'pomodoro_settings',
        'indexes': [
            {'fields': ['user_id'], 'unique': True}
        ]
    }

    def save(self, *args, **kwargs):
        self.updated_at = datetime.now(timezone.utc)
        return super(PomodoroSettings, self).save(*args, **kwargs)

    def start_session(self):
        """Démarre une nouvelle session Pomodoro"""
        now = datetime.now(timezone.utc)

        # Si une session est déjà en cours, la terminer
        if self.current_session and not self.current_session.completed:
            # Assurez-vous que les dates ont un fuseau horaire
            if self.current_session.start_time and self.current_session.start_time.tzinfo is None:
                self.current_session.start_time = self.current_session.start_time.replace(tzinfo=timezone.utc)
            if self.current_session.paused_at and self.current_session.paused_at.tzinfo is None:
                self.current_session.paused_at = self.current_session.paused_at.replace(tzinfo=timezone.utc)

            self.complete_session()

        # Créer une nouvelle session
        self.current_session = PomodoroSession(
            start_time=now,
            focus_duration=self.focus_duration,
            break_duration=self.short_break_duration if len(self.session_history) % self.sessions_before_long_break != 0 else self.long_break_duration
        )
        self.is_active = True
        self.save()

        return self.current_session

    def pause_session(self):
        """Met en pause la session Pomodoro en cours"""
        if not self.current_session or self.current_session.completed:
            return None

        now = datetime.now(timezone.utc)
        self.current_session.paused_at = now
        self.save()

        return self.current_session

    def resume_session(self):
        """Reprend la session Pomodoro en pause"""
        if not self.current_session or not self.current_session.paused_at:
            return None

        now = datetime.now(timezone.utc)

        # Assurez-vous que paused_at a un fuseau horaire
        paused_at = self.current_session.paused_at
        if paused_at.tzinfo is None:
            paused_at = paused_at.replace(tzinfo=timezone.utc)

        pause_duration = (now - paused_at).total_seconds()
        self.current_session.total_pause_time += int(pause_duration)
        self.current_session.paused_at = None
        self.save()

        return self.current_session

    def complete_session(self):
        """Termine la session Pomodoro en cours"""
        if not self.current_session or self.current_session.completed:
            return None

        now = datetime.now(timezone.utc)

        # Assurez-vous que les dates ont un fuseau horaire
        if self.current_session.start_time and self.current_session.start_time.tzinfo is None:
            self.current_session.start_time = self.current_session.start_time.replace(tzinfo=timezone.utc)
        if self.current_session.paused_at and self.current_session.paused_at.tzinfo is None:
            self.current_session.paused_at = self.current_session.paused_at.replace(tzinfo=timezone.utc)

        self.current_session.end_time = now
        self.current_session.completed = True

        # Ajouter la session à l'historique
        self.session_history.append(self.current_session)

        # Limiter l'historique à 100 sessions
        if len(self.session_history) > 100:
            self.session_history = self.session_history[-100:]

        self.is_active = False
        self.save()

        return self.current_session

    def reset_session(self):
        """Réinitialise la session Pomodoro en cours"""
        if not self.current_session:
            return None

        self.current_session = None
        self.is_active = False
        self.save()

        return True

    def get_session_status(self):
        """Retourne le statut de la session Pomodoro en cours"""
        if not self.current_session:
            return {
                "active": False,
                "status": "inactive",
                "remaining_time": 0
            }

        now = datetime.now(timezone.utc)

        # Si la session est terminée
        if self.current_session.completed:
            return {
                "active": False,
                "status": "completed",
                "remaining_time": 0
            }

        # Si la session est en pause
        if self.current_session.paused_at:
            # Assurez-vous que les dates ont un fuseau horaire
            paused_at = self.current_session.paused_at
            start_time = self.current_session.start_time

            if paused_at.tzinfo is None:
                paused_at = paused_at.replace(tzinfo=timezone.utc)

            if start_time.tzinfo is None:
                start_time = start_time.replace(tzinfo=timezone.utc)

            elapsed_time = (paused_at - start_time).total_seconds() - self.current_session.total_pause_time
            remaining_time = max(0, self.current_session.focus_duration * 60 - elapsed_time)

            return {
                "active": True,
                "status": "paused",
                "remaining_time": int(remaining_time),
                "elapsed_time": int(elapsed_time),
                "focus_duration": self.current_session.focus_duration,
                "break_duration": self.current_session.break_duration
            }

        # Si la session est active
        # Assurez-vous que start_time a un fuseau horaire
        start_time = self.current_session.start_time
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)

        elapsed_time = (now - start_time).total_seconds() - self.current_session.total_pause_time
        remaining_time = max(0, self.current_session.focus_duration * 60 - elapsed_time)

        return {
            "active": True,
            "status": "active",
            "remaining_time": int(remaining_time),
            "elapsed_time": int(elapsed_time),
            "focus_duration": self.current_session.focus_duration,
            "break_duration": self.current_session.break_duration
        }
