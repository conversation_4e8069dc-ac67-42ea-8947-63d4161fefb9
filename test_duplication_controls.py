#!/usr/bin/env python
"""
Script de test pour les contrôles de duplication
"""

import requests
import json

def test_duplication_controls():
    """Test des contrôles de duplication pour tous les éléments"""
    base_url = "http://localhost:8000/api"

    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }

    print("=== Test des contrôles de duplication ===\n")

    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )

    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return

    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")

    # 2. Test des événements personnels
    print("\n2. Test de duplication - Événements personnels...")

    event_data = {
        "title": "Test Événement Duplication",
        "description": "Test de duplication",
        "start_date": "2024-12-25T10:00:00Z",
        "end_date": "2024-12-25T12:00:00Z",
        "start_time": "10:00",
        "end_time": "12:00",
        "color": "#ff0000"
    }

    # Première création
    first_event = requests.post(
        f"{base_url}/personal-events/",
        json=event_data,
        headers=headers
    )

    if first_event.status_code == 201:
        print("✅ Premier événement créé avec succès")

        # Tentative de duplication
        duplicate_event = requests.post(
            f"{base_url}/personal-events/",
            json=event_data,
            headers=headers
        )

        if duplicate_event.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les événements personnels")
            print(f"📄 Message: {duplicate_event.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les événements personnels")
    else:
        print(f"❌ Échec de création du premier événement: {first_event.json()}")

    # 3. Test des tâches personnelles
    print("\n3. Test de duplication - Tâches personnelles...")

    task_data = {
        "title": "Test Tâche Duplication",
        "description": "Test de duplication",
        "start_date": "2024-12-25T10:00:00Z",
        "end_date": "2024-12-25T12:00:00Z",
        "priority": "medium"
    }

    # Première création
    first_task = requests.post(
        f"{base_url}/personal-tasks/",
        json=task_data,
        headers=headers
    )

    if first_task.status_code == 201:
        print("✅ Première tâche créée avec succès")

        # Tentative de duplication
        duplicate_task = requests.post(
            f"{base_url}/personal-tasks/",
            json=task_data,
            headers=headers
        )

        if duplicate_task.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les tâches personnelles")
            print(f"📄 Message: {duplicate_task.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les tâches personnelles")
    else:
        print(f"❌ Échec de création de la première tâche: {first_task.json()}")

    # 4. Test des notes personnelles
    print("\n4. Test de duplication - Notes personnelles...")

    note_data = {
        "title": "Test Note Duplication",
        "content": "Contenu de test pour la duplication"
    }

    # Première création
    first_note = requests.post(
        f"{base_url}/personal-notes/",
        json=note_data,
        headers=headers
    )

    if first_note.status_code == 201:
        print("✅ Première note créée avec succès")

        # Tentative de duplication
        duplicate_note = requests.post(
            f"{base_url}/personal-notes/",
            json=note_data,
            headers=headers
        )

        if duplicate_note.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les notes personnelles")
            print(f"📄 Message: {duplicate_note.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les notes personnelles")
    else:
        print(f"❌ Échec de création de la première note: {first_note.json()}")

    # 5. Test des journaux personnels
    print("\n5. Test de duplication - Journaux personnels...")

    journal_data = {
        "title": "Test Journal Duplication",
        "content": "Contenu de test pour la duplication du journal",
        "entry_date": "2024-12-25T10:00:00Z"
    }

    # Première création
    first_journal = requests.post(
        f"{base_url}/personal-journals/",
        json=journal_data,
        headers=headers
    )

    if first_journal.status_code == 201:
        print("✅ Première entrée de journal créée avec succès")

        # Tentative de duplication
        duplicate_journal = requests.post(
            f"{base_url}/personal-journals/",
            json=journal_data,
            headers=headers
        )

        if duplicate_journal.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les journaux personnels")
            print(f"📄 Message: {duplicate_journal.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les journaux personnels")
    else:
        print(f"❌ Échec de création de la première entrée de journal: {first_journal.json()}")

    # 6. Test avec titres différents (doit fonctionner)
    print("\n6. Test avec titres différents (doit fonctionner)...")

    different_event_data = {
        "title": "Test Événement Différent",
        "description": "Test avec titre différent",
        "start_date": "2024-12-26T10:00:00Z",
        "end_date": "2024-12-26T12:00:00Z",
        "start_time": "10:00",
        "end_time": "12:00",
        "color": "#00ff00"
    }

    different_event = requests.post(
        f"{base_url}/personal-events/",
        json=different_event_data,
        headers=headers
    )

    if different_event.status_code == 201:
        print("✅ Événement avec titre différent créé avec succès")
    else:
        print(f"❌ Échec de création d'événement avec titre différent: {different_event.json()}")

    # 7. Test des événements d'équipe (nécessite un admin)
    print("\n7. Test de duplication - Événements d'équipe...")
    print("ℹ️  Note: Ce test nécessite un compte admin avec une équipe assignée")

    # 8. Test des tâches d'équipe (nécessite un admin)
    print("\n8. Test de duplication - Tâches d'équipe...")
    print("ℹ️  Note: Ce test nécessite un compte admin avec une équipe assignée")

    print("\n" + "="*60)
    print("🎯 TEST DES CONTRÔLES DE DUPLICATION TERMINÉ")
    print("="*60)
    print("\n📋 Résumé des contrôles implémentés:")
    print("✅ Événements personnels - Par utilisateur")
    print("✅ Tâches personnelles - Par utilisateur")
    print("✅ Notes personnelles - Par utilisateur")
    print("✅ Journaux personnels - Par utilisateur")
    print("✅ Événements d'équipe - Par équipe/membre")
    print("✅ Tâches d'équipe - Par équipe")

if __name__ == "__main__":
    test_duplication_controls()
