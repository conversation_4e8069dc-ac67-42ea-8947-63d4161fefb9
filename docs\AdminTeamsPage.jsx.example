/**
 * Exemple de page d'administration des équipes avec gestion des permissions
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/pages/
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext'; // Ajustez le chemin selon votre structure
import { checkTeamPermissions, hasPermission } from '../../services/permissionService'; // Ajustez le chemin selon votre structure
import TeamPermissionGate from '../../components/teams/TeamPermissionGate'; // Ajustez le chemin selon votre structure

// Composant fictif pour l'exemple
const TeamCard = ({ team, onEdit, onDelete, onManageMembers }) => {
  return (
    <div className="team-card bg-white p-4 rounded-lg shadow-md mb-4">
      <h3 className="text-xl font-semibold">{team.name}</h3>
      <p className="text-gray-600 mb-2">{team.description}</p>
      <p className="text-sm text-gray-500">Responsable: {team.responsable.name}</p>
      <p className="text-sm text-gray-500">Membres: {team.members?.length || 0}</p>
      
      <div className="mt-4 flex gap-2">
        <TeamPermissionGate team={team} permissionType="canManage">
          <button 
            onClick={() => onEdit(team)} 
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Modifier
          </button>
        </TeamPermissionGate>
        
        <TeamPermissionGate team={team} permissionType="canManage">
          <button 
            onClick={() => onDelete(team)} 
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Supprimer
          </button>
        </TeamPermissionGate>
        
        <TeamPermissionGate team={team} permissionType="canAddMembers">
          <button 
            onClick={() => onManageMembers(team)} 
            className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Gérer les membres
          </button>
        </TeamPermissionGate>
      </div>
    </div>
  );
};

const AdminTeamsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState({ name: '', description: '' });
  
  // Vérifier si l'utilisateur a les permissions nécessaires
  useEffect(() => {
    if (!user || user.role !== 'admin') {
      navigate('/unauthorized');
      return;
    }
    
    if (!hasPermission(user, 'manage_teams')) {
      navigate('/unauthorized', { 
        state: { message: "Vous n'avez pas les permissions nécessaires pour gérer les équipes" } 
      });
      return;
    }
    
    // Charger les équipes
    fetchTeams();
  }, [user, navigate]);
  
  const fetchTeams = async () => {
    try {
      setLoading(true);
      // Simulation d'un appel API
      // Dans une implémentation réelle, remplacez par un appel à votre API
      setTimeout(() => {
        const mockTeams = [
          {
            id: '1',
            name: 'Équipe Marketing',
            description: 'Équipe responsable du marketing',
            responsable: { id: user.id, name: user.name },
            members: [{ id: '101', name: 'Jean Dupont' }]
          },
          {
            id: '2',
            name: 'Équipe Développement',
            description: 'Équipe responsable du développement',
            responsable: { id: 'autre-admin-id', name: 'Admin B' },
            members: [{ id: '102', name: 'Marie Martin' }]
          }
        ];
        setTeams(mockTeams);
        setLoading(false);
      }, 1000);
    } catch (err) {
      setError("Erreur lors du chargement des équipes");
      setLoading(false);
    }
  };
  
  const handleCreateTeam = () => {
    // Vérifier les permissions
    if (!hasPermission(user, 'manage_teams')) {
      setError("Vous n'avez pas les permissions nécessaires pour créer une équipe");
      return;
    }
    
    // Validation du formulaire
    if (!formData.name.trim()) {
      setError("Le nom de l'équipe est requis");
      return;
    }
    
    // Simulation de création d'équipe
    const newTeam = {
      id: Date.now().toString(),
      name: formData.name,
      description: formData.description,
      responsable: { id: user.id, name: user.name },
      members: []
    };
    
    setTeams([...teams, newTeam]);
    setFormData({ name: '', description: '' });
    setShowCreateForm(false);
    setError(null);
  };
  
  const handleEditTeam = (team) => {
    // Vérifier si l'utilisateur peut gérer cette équipe
    const permissions = checkTeamPermissions(user, team);
    if (!permissions.canManage) {
      setError("Vous n'avez pas les permissions nécessaires pour modifier cette équipe");
      return;
    }
    
    // Dans une implémentation réelle, naviguer vers la page d'édition
    navigate(`/teams/${team.id}/edit`);
  };
  
  const handleDeleteTeam = (team) => {
    // Vérifier si l'utilisateur peut gérer cette équipe
    const permissions = checkTeamPermissions(user, team);
    if (!permissions.canManage) {
      setError("Vous n'avez pas les permissions nécessaires pour supprimer cette équipe");
      return;
    }
    
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'équipe ${team.name} ?`)) {
      // Simulation de suppression
      setTeams(teams.filter(t => t.id !== team.id));
    }
  };
  
  const handleManageMembers = (team) => {
    // Vérifier si l'utilisateur peut gérer les membres de cette équipe
    const permissions = checkTeamPermissions(user, team);
    if (!permissions.canAddMembers) {
      setError("Vous n'avez pas les permissions nécessaires pour gérer les membres de cette équipe");
      return;
    }
    
    // Dans une implémentation réelle, naviguer vers la page de gestion des membres
    navigate(`/teams/${team.id}/members`);
  };
  
  if (loading) return <div className="text-center py-8">Chargement...</div>;
  
  return (
    <div className="admin-teams-page p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gestion des Équipes</h1>
        
        {/* Bouton de création d'équipe - visible uniquement si l'utilisateur a la permission */}
        {hasPermission(user, 'manage_teams') && (
          <button 
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            {showCreateForm ? 'Annuler' : 'Créer une équipe'}
          </button>
        )}
      </div>
      
      {/* Message d'erreur */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Formulaire de création d'équipe */}
      {showCreateForm && (
        <div className="bg-gray-100 p-4 rounded-lg mb-6">
          <h2 className="text-xl font-semibold mb-4">Créer une nouvelle équipe</h2>
          <div className="mb-4">
            <label className="block text-gray-700 mb-2">Nom de l'équipe *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border rounded"
              placeholder="Nom de l'équipe"
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 mb-2">Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border rounded"
              placeholder="Description de l'équipe"
              rows="3"
            />
          </div>
          <button
            onClick={handleCreateTeam}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Créer l'équipe
          </button>
        </div>
      )}
      
      {/* Liste des équipes */}
      <div className="teams-list">
        {teams.length === 0 ? (
          <p className="text-gray-500 text-center py-8">Aucune équipe trouvée</p>
        ) : (
          teams.map(team => (
            <TeamCard
              key={team.id}
              team={team}
              onEdit={handleEditTeam}
              onDelete={handleDeleteTeam}
              onManageMembers={handleManageMembers}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default AdminTeamsPage;