from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.decorators import api_view, permission_classes
from ..mongo_models import User
import logging

logger = logging.getLogger(__name__)

# Nouvelle approche avec une fonction basée sur les vues
@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def change_password_view(request):
    """
    Vue fonctionnelle dédiée au changement de mot de passe utilisateur.
    Cette approche contourne les problèmes potentiels avec les vues basées sur les classes.
    """
    if request.method == 'GET':
        logger.info(f"Requête GET reçue sur change_password_view - Utilisateur: {request.user.email}")
        return Response({
            'message': 'Password change API is accessible',
            'user': request.user.email,
            'role': request.user.role
        })

    elif request.method == 'POST':
        try:
            logger.info(f"Requête POST reçue sur change_password_view - URL: {request.path}")
            user = request.user
            logger.info(f"Utilisateur authentifié: {user.email}, rôle: {user.role}")

            # Afficher les données reçues (sans le mot de passe)
            data_keys = list(request.data.keys()) if hasattr(request, 'data') else []
            logger.info(f"Données reçues - clés: {data_keys}")

            current_password = request.data.get('current_password')
            new_password = request.data.get('new_password')

            if not current_password or not new_password:
                logger.warning(f"Échec: mot de passe actuel ou nouveau manquant pour {user.email}")
                return Response({'error': 'Both current and new passwords are required'}, status=400)

            # Vérification de la complexité du mot de passe
            if len(new_password) < 8:
                logger.warning(f"Échec: nouveau mot de passe trop court pour {user.email}")
                return Response({'error': 'Le mot de passe doit contenir au moins 8 caractères'}, status=400)

            # Vérification du mot de passe actuel
            if not user.check_password(current_password):
                logger.warning(f"Échec: mot de passe actuel incorrect pour {user.email}")
                return Response({'error': 'Current password is incorrect'}, status=400)

            logger.info(f"Mot de passe actuel validé pour {user.email}, application du nouveau mot de passe")

            # Mise à jour du mot de passe
            user.set_password(new_password)
            user.temp_password_required = False
            user.temp_password_used = False

            # Sauvegarde des modifications
            user.save()

            logger.info(f"Mot de passe changé avec succès pour {user.email}")
            return Response({'message': 'Password changed successfully'})
        except Exception as e:
            logger.error(f"Erreur lors du changement de mot de passe: {str(e)}")
            return Response({'error': str(e)}, status=500)

# Garder la classe originale pour la compatibilité avec le code existant
@method_decorator(csrf_exempt, name='dispatch')
class UserChangePasswordView(APIView):
    """
    Vue dédiée au changement de mot de passe utilisateur.
    Cette vue est simplifiée et contourne la protection CSRF pour résoudre les problèmes d'accès.
    """
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'options']  # Explicitement autoriser POST

    def get(self, request):
        """Méthode GET pour vérifier si l'API est accessible"""
        return change_password_view(request)

    def post(self, request):
        return change_password_view(request)
