from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed
from .mongo_models import User, BlacklistedToken
import logging

logger = logging.getLogger(__name__)

class MongoJWTAuthentication(JWTAuthentication):
    def authenticate(self, request):
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        try:
            validated_token = self.get_validated_token(raw_token)

            try:
                user_id = validated_token.get('user_id')

                logger.debug(f"\n=== Token Validation ===")
                logger.debug(f"Token payload: {validated_token}")
                logger.debug(f"Attempting to get user with ID: {user_id}")

                if not user_id:
                    logger.warning("Token contained no valid user identification")
                    raise InvalidToken('Token contained no valid user identification')

                # Check if token is blacklisted
                if BlacklistedToken.is_blacklisted(raw_token.decode()):
                    logger.warning(f"Token is blacklisted for user_id: {user_id}")
                    raise InvalidToken('Token is blacklisted')

                user = User.objects.get(id=user_id)

                if not user:
                    logger.warning(f"User with ID {user_id} not found despite query success")
                    raise AuthenticationFailed('User not found')

                logger.debug(f"Found user: {user.email} with role: {user.role}")
                return user, validated_token

            except User.DoesNotExist:
                logger.warning(f"User with ID {user_id} not found")
                raise AuthenticationFailed('User not found')
            except Exception as e:
                logger.error(f"Authentication error: {str(e)}")
                raise AuthenticationFailed(str(e))
        except InvalidToken as e:
            logger.warning(f"Invalid token: {str(e)}")
            raise
