from mongoengine import Document, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON>ield, <PERSON><PERSON>an<PERSON><PERSON>
from datetime import datetime, timezone
from bson import ObjectId

class PersonalEvent(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    description = StringField(required=False)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    start_time = StringField(required=True)  # Format HH:MM
    end_time = StringField(required=True)    # Format HH:MM
    note = StringField(required=False)
    status = StringField(default='pending', choices=['pending', 'completed', 'archived'])
    color = StringField(required=False, default="#3788d8")  # Couleur de l'événement au format hexadécimal
    created_by = StringField(required=True)  # ID de l'utilisateur qui a créé l'événement (employé ou client)
    created_by_name = StringField(required=False)  # Nom de l'utilisateur qui a créé l'événement
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'personal_events',
        'indexes': [
            {'fields': ['created_by']},
            {'fields': ['start_date']},
            {'fields': ['status']}
        ]
    }

    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(PersonalEvent, self).save(*args, **kwargs)

    def can_manage_event(self, user):
        """Vérifie si un utilisateur peut gérer cet événement personnel"""
        # Un utilisateur ne peut gérer que ses propres événements personnels
        return str(user.id) == self.created_by

    def can_update_status(self, user):
        """Vérifie si un utilisateur peut mettre à jour le statut de l'événement personnel"""
        # Un utilisateur ne peut mettre à jour que ses propres événements personnels
        return str(user.id) == self.created_by