"""
Palette de couleurs prédéfinies pour les événements et tâches.
"""

# Palette de couleurs prédéfinies avec noms et codes hexadécimaux
COLOR_PALETTE = {
    "bleu_principal": {
        "name": "<PERSON><PERSON><PERSON> Principal",
        "hex": "#3788d8",
        "description": "Couleur par défaut - Bleu professionnel"
    },
    "rouge_urgent": {
        "name": "Rouge Urgent",
        "hex": "#e74c3c",
        "description": "Pour les tâches urgentes et importantes"
    },
    "orange_attention": {
        "name": "Orange Attention",
        "hex": "#f39c12",
        "description": "Pour les tâches nécessitant une attention particulière"
    },
    "jaune_rappel": {
        "name": "Jaune Rappel",
        "hex": "#f1c40f",
        "description": "Pour les rappels et notifications"
    },
    "vert_succes": {
        "name": "<PERSON><PERSON>",
        "hex": "#27ae60",
        "description": "Pour les tâches terminées ou les événements positifs"
    },
    "violet_creatif": {
        "name": "Violet Créatif",
        "hex": "#9b59b6",
        "description": "Pour les sessions créatives et brainstorming"
    },
    "rose_personnel": {
        "name": "Rose Personnel",
        "hex": "#e91e63",
        "description": "Pour les événements personnels"
    },
    "turquoise_equipe": {
        "name": "Turquoise Équipe",
        "hex": "#1abc9c",
        "description": "Pour les activités d'équipe"
    },
    "indigo_formation": {
        "name": "Indigo Formation",
        "hex": "#3f51b5",
        "description": "Pour les formations et apprentissages"
    },
    "marron_reunion": {
        "name": "Marron Réunion",
        "hex": "#795548",
        "description": "Pour les réunions et meetings"
    },
    "gris_archive": {
        "name": "Gris Archive",
        "hex": "#95a5a6",
        "description": "Pour les éléments archivés"
    },
    "noir_important": {
        "name": "Noir Important",
        "hex": "#2c3e50",
        "description": "Pour les événements très importants"
    }
}

# Couleurs par catégorie pour faciliter la sélection
COLOR_CATEGORIES = {
    "urgence": ["rouge_urgent", "orange_attention", "jaune_rappel"],
    "statut": ["vert_succes", "gris_archive", "noir_important"],
    "type": ["violet_creatif", "turquoise_equipe", "indigo_formation", "marron_reunion"],
    "personnel": ["rose_personnel", "bleu_principal"]
}

# Couleurs recommandées par type d'événement
RECOMMENDED_COLORS = {
    "reunion": "marron_reunion",
    "formation": "indigo_formation",
    "deadline": "rouge_urgent",
    "team_building": "turquoise_equipe",
    "brainstorming": "violet_creatif",
    "personnel": "rose_personnel",
    "urgent": "rouge_urgent",
    "important": "noir_important",
    "completed": "vert_succes",
    "archived": "gris_archive"
}

def get_color_palette():
    """Retourne la palette complète de couleurs"""
    return COLOR_PALETTE

def get_color_categories():
    """Retourne les couleurs organisées par catégories"""
    return COLOR_CATEGORIES

def get_recommended_colors():
    """Retourne les couleurs recommandées par type"""
    return RECOMMENDED_COLORS

def validate_color(color_code):
    """Valide qu'une couleur est dans la palette ou est un code hex valide"""
    if not color_code:
        return False, "Couleur requise"
    
    # Vérifier si c'est une couleur de la palette
    for color_key, color_info in COLOR_PALETTE.items():
        if color_code == color_info["hex"] or color_code == color_key:
            return True, "Couleur valide"
    
    # Vérifier si c'est un code hexadécimal valide
    if color_code.startswith("#") and len(color_code) == 7:
        try:
            int(color_code[1:], 16)
            return True, "Code hexadécimal valide"
        except ValueError:
            return False, "Code hexadécimal invalide"
    
    return False, "Couleur non reconnue"

def get_color_hex(color_identifier):
    """Convertit un identifiant de couleur en code hexadécimal"""
    if color_identifier in COLOR_PALETTE:
        return COLOR_PALETTE[color_identifier]["hex"]
    
    # Si c'est déjà un code hex, le retourner tel quel
    if color_identifier.startswith("#"):
        is_valid, _ = validate_color(color_identifier)
        if is_valid:
            return color_identifier
    
    # Couleur par défaut si non trouvée
    return COLOR_PALETTE["bleu_principal"]["hex"]

def suggest_color_by_title(title):
    """Suggère une couleur basée sur le titre de l'événement"""
    title_lower = title.lower()
    
    # Mots-clés pour suggérer des couleurs
    keywords = {
        "urgent": "rouge_urgent",
        "important": "noir_important",
        "réunion": "marron_reunion",
        "meeting": "marron_reunion",
        "formation": "indigo_formation",
        "training": "indigo_formation",
        "équipe": "turquoise_equipe",
        "team": "turquoise_equipe",
        "créatif": "violet_creatif",
        "brainstorm": "violet_creatif",
        "personnel": "rose_personnel",
        "deadline": "rouge_urgent",
        "rappel": "jaune_rappel",
        "reminder": "jaune_rappel"
    }
    
    for keyword, color_key in keywords.items():
        if keyword in title_lower:
            return COLOR_PALETTE[color_key]["hex"]
    
    # Couleur par défaut
    return COLOR_PALETTE["bleu_principal"]["hex"]
