import os
import sys
import django

# Configurer l'environnement Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Importer les fonctions à tester
from core.utils import send_temp_password_email, send_reset_password_email, generate_temp_password, generate_reset_token

def test_temp_password_email():
    """Tester l'envoi d'un email avec mot de passe temporaire"""
    print("\n=== Test d'envoi d'email avec mot de passe temporaire ===")
    email = input("Entrez l'adresse email de test: ")
    name = input("Entrez le nom d'utilisateur: ")
    temp_password = generate_temp_password()
    print(f"Mot de passe temporaire généré: {temp_password}")
    
    try:
        send_temp_password_email(email, temp_password, name)
        print("✅ Email avec mot de passe temporaire envoyé avec succès!")
        print("📝 Vérifiez la console Django pour voir l'email (en mode développement)")
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi de l'email: {str(e)}")

def test_reset_password_email():
    """Tester l'envoi d'un email de réinitialisation de mot de passe"""
    print("\n=== Test d'envoi d'email de réinitialisation de mot de passe ===")
    email = input("Entrez l'adresse email de test: ")
    name = input("Entrez le nom d'utilisateur: ")
    reset_token = generate_reset_token()
    print(f"Token de réinitialisation généré: {reset_token}")
    
    try:
        send_reset_password_email(email, reset_token, name)
        print("✅ Email de réinitialisation de mot de passe envoyé avec succès!")
        print("📝 Vérifiez la console Django pour voir l'email (en mode développement)")
    except Exception as e:
        print(f"❌ Erreur lors de l'envoi de l'email: {str(e)}")

def main():
    """Menu principal pour tester les fonctions d'envoi d'email"""
    print("\n📧 TESTEUR DE FONCTIONS D'ENVOI D'EMAIL 📧")
    print("===========================================\n")
    print("1. Tester l'envoi d'email avec mot de passe temporaire")
    print("2. Tester l'envoi d'email de réinitialisation de mot de passe")
    print("3. Quitter")
    
    while True:
        choice = input("\nChoisissez une option (1-3): ")
        
        if choice == '1':
            test_temp_password_email()
        elif choice == '2':
            test_reset_password_email()
        elif choice == '3':
            print("\nAu revoir!")
            break
        else:
            print("Option invalide. Veuillez choisir entre 1 et 3.")

if __name__ == "__main__":
    main()