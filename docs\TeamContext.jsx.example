/**
 * Contexte pour la gestion des équipes
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/contexts/
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext'; // Ajustez le chemin selon votre structure
// Utiliser la syntaxe import ES6 au lieu de require
import { getTeams } from '../services/teamService'; // Importation avec la syntaxe ES6
import { checkTeamPermissions } from '../services/permissionService';

// Création du contexte
const TeamContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useTeam = () => useContext(TeamContext);

// Fournisseur du contexte
export const TeamProvider = ({ children }) => {
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Charger les équipes
  const loadTeams = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Utiliser la fonction importée avec ES6
      const teamsData = await getTeams();
      
      // Filtrer les équipes selon les permissions de l'utilisateur
      const filteredTeams = teamsData.filter(team => {
        const permissions = checkTeamPermissions(user, team);
        return permissions.canView;
      });
      
      setTeams(filteredTeams);
    } catch (err) {
      console.error('Error fetching teams:', err);
      setError('Erreur lors de la récupération des équipes');
    } finally {
      setLoading(false);
    }
  };

  // Charger les équipes au montage du composant ou quand l'utilisateur change
  useEffect(() => {
    if (user) {
      loadTeams();
    } else {
      setTeams([]);
      setLoading(false);
    }
  }, [user]);

  // Valeur du contexte
  const value = {
    teams,
    loading,
    error,
    refreshTeams: loadTeams
  };

  return (
    <TeamContext.Provider value={value}>
      {children}
    </TeamContext.Provider>
  );
};

export default TeamContext;