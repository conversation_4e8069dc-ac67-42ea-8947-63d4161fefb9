import unittest
from unittest.mock import patch, MagicMock
from django.test import TestCase
from core.utils import send_temp_password_email, send_reset_password_email
from core.mongo_models import User

class EmailTests(TestCase):
    def setUp(self):
        self.test_email = '<EMAIL>'
        self.test_name = 'Test User'
        self.test_password = 'TempPass123!'
        self.test_token = 'reset_token_123'

    @patch('core.utils.send_mail')
    def test_send_temp_password_email(self, mock_send_mail):
        # Configure le mock
        mock_send_mail.return_value = 1

        # Appelle la fonction
        send_temp_password_email(self.test_email, self.test_password, self.test_name)

        # Vérifie que send_mail a été appelé avec les bons arguments
        mock_send_mail.assert_called_once()
        call_args = mock_send_mail.call_args[0]
        
        # Vérifie le sujet
        self.assertEqual('Votre mot de passe temporaire', call_args[0])
        # Vérifie que le mot de passe est dans le corps du message
        self.assertIn(self.test_password, call_args[1])
        # Vérifie l'email du destinataire
        self.assertEqual([self.test_email], call_args[3])

    @patch('core.utils.send_mail')
    def test_send_reset_password_email(self, mock_send_mail):
        # Configure le mock
        mock_send_mail.return_value = 1

        # Appelle la fonction
        send_reset_password_email(self.test_email, self.test_token, self.test_name)

        # Vérifie que send_mail a été appelé avec les bons arguments
        mock_send_mail.assert_called_once()
        call_args = mock_send_mail.call_args[0]
        
        # Vérifie le sujet
        self.assertEqual('Demande de réinitialisation de mot de passe', call_args[0])
        # Vérifie que le lien de réinitialisation est dans le corps du message
        expected_link = f"http://localhost:5173/reset-password/{self.test_token}"
        self.assertIn(expected_link, call_args[1])
        # Vérifie l'email du destinataire
        self.assertEqual([self.test_email], call_args[3])