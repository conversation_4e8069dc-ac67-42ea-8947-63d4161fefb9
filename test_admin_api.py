#!/usr/bin/env python3
"""
Script de test pour l'API du tableau de bord admin
"""

import os
import sys
import django
import json
from datetime import datetime, timezone

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from rest_framework.test import APIRequestFactory
from core.mongo_models import User
from core.views.bi_views import AdminDashboardView
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_admin_dashboard_api():
    """
    Teste l'API du tableau de bord admin
    """
    print("=== Test de l'API du tableau de bord admin ===\n")

    # 1. Trouver un admin
    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé dans la base de données")
        return False

    admin = admin_users.first()
    print(f"✅ Admin trouvé: {admin.name} ({admin.email})")

    # 2. Créer une requête simulée
    factory = APIRequestFactory()
    request = factory.get('/api/bi/admin-dashboard/')
    request.user = admin

    # 3. Tester la vue
    view = AdminDashboardView()

    try:
        response = view.get(request)
        print(f"✅ Réponse API reçue avec status: {response.status_code}")

        if response.status_code == 200:
            data = response.data
            print("✅ Données reçues avec succès")

            # Vérifier la structure des données
            required_keys = ['timestamp', 'admin_id', 'metric_cards', 'charts', 'detailed_stats', 'metadata']
            for key in required_keys:
                if key in data:
                    print(f"✅ Clé '{key}' présente")
                else:
                    print(f"❌ Clé '{key}' manquante")
                    return False

            # Vérifier les cartes de métriques
            metric_cards = data.get('metric_cards', [])
            print(f"✅ Nombre de cartes de métriques: {len(metric_cards)}")

            for i, card in enumerate(metric_cards):
                title = card.get('title', 'Sans titre')
                value = card.get('value', 'N/A')
                print(f"  - Carte {i+1}: {title} = {value}")

                # Vérifier qu'il n'y a pas de NaN
                if isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf'):
                    print(f"❌ Valeur NaN détectée dans la carte: {title}")
                    return False

            # Vérifier les graphiques
            charts = data.get('charts', {})
            print(f"✅ Nombre de graphiques: {len(charts)}")

            for chart_name, chart_data in charts.items():
                print(f"  - Graphique: {chart_name}")
                chart_title = chart_data.get('title', 'Sans titre')
                chart_data_points = chart_data.get('data', [])
                total_count = chart_data.get('total_count', 0)

                print(f"    Titre: {chart_title}")
                print(f"    Points de données: {len(chart_data_points)}")
                print(f"    Total: {total_count}")

                # Vérifier chaque point de données
                for point in chart_data_points:
                    name = point.get('name', 'Sans nom')
                    value = point.get('value', 0)
                    percentage = point.get('percentage', 0)

                    print(f"      - {name}: {value} ({percentage}%)")

                    # Vérifier qu'il n'y a pas de NaN
                    if (isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf')) or \
                       (isinstance(percentage, float) and (str(percentage) == 'nan' or str(percentage) == 'inf')):
                        print(f"❌ Valeur NaN détectée dans le graphique {chart_name}, point {name}")
                        return False

            # Vérifier les statistiques détaillées
            detailed_stats = data.get('detailed_stats', {})
            print(f"✅ Statistiques détaillées présentes: {len(detailed_stats)} sections")

            for section_name, section_data in detailed_stats.items():
                print(f"  - Section: {section_name}")
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        if isinstance(value, (int, float)):
                            if isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf'):
                                print(f"❌ Valeur NaN détectée dans {section_name}.{key}")
                                return False
                            print(f"    {key}: {value}")

            print("\n✅ Toutes les vérifications sont passées!")
            print("✅ Aucune valeur NaN détectée")
            print("✅ Structure des données correcte")

            return True

        else:
            print(f"❌ Erreur API: {response.status_code}")
            if hasattr(response, 'data'):
                print(f"Détails: {response.data}")
            return False

    except Exception as e:
        print(f"❌ Exception lors du test de l'API: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_different_periods():
    """
    Teste l'API avec différentes périodes
    """
    print("\n=== Test avec différentes périodes ===\n")

    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé")
        return False

    admin = admin_users.first()
    factory = APIRequestFactory()
    view = AdminDashboardView()

    periods = ['today', '1h', '24h', '7d', '30d']

    for period in periods:
        print(f"--- Test période: {period} ---")

        request = factory.get(f'/api/bi/admin-dashboard/?period={period}&manual_refresh=true')
        request.user = admin

        try:
            response = view.get(request)

            if response.status_code == 200:
                data = response.data
                period_name = data.get('metadata', {}).get('current_period', {}).get('period_name', 'Inconnu')
                print(f"✅ Période {period} ({period_name}) - OK")

                # Vérifier qu'il n'y a pas de NaN dans les cartes
                metric_cards = data.get('metric_cards', [])
                for card in metric_cards:
                    value = card.get('value')
                    if isinstance(value, str) and ('nan' in value.lower() or 'inf' in value.lower()):
                        print(f"❌ NaN détecté dans la période {period}")
                        return False

            else:
                print(f"❌ Erreur pour la période {period}: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ Exception pour la période {period}: {str(e)}")
            return False

    print("✅ Toutes les périodes testées avec succès!")
    return True

if __name__ == "__main__":
    success1 = test_admin_dashboard_api()
    success2 = test_different_periods()

    if success1 and success2:
        print("\n🎉 Tous les tests de l'API admin sont réussis!")
        print("🎉 Les corrections du tableau de bord admin fonctionnent correctement!")
    else:
        print("\n❌ Des problèmes ont été détectés dans l'API admin.")
        sys.exit(1)
