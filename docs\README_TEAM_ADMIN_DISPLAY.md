# Affichage du Nom de l'Équipe et du Responsable

Ce document explique les modifications apportées pour afficher le nom de l'équipe et du responsable (admin) dans l'interface utilisateur et dans la base de données.

## Modifications Apportées

### 1. Mo<PERSON><PERSON><PERSON> d'Événement

Le modèle `Event` a été mis à jour pour inclure deux nouveaux champs :

- `team_name` : Stocke le nom de l'équipe associée à l'événement
- `created_by_name` : Stocke le nom de l'administrateur qui a créé l'événement

Ces champs permettent d'accéder directement aux noms sans avoir à effectuer des requêtes supplémentaires vers les collections `teams` et `users`.

### 2. Vues API

Les vues API ont été modifiées pour :

- Inclure les nouveaux champs dans les réponses JSON
- Remplir automatiquement ces champs lors de la création d'un événement
- Mettre à jour le nom de l'équipe lorsque l'ID de l'équipe est modifié

#### Création d'Événement

Lors de la création d'un événement, le nom de l'administrateur est automatiquement ajouté à partir de l'utilisateur connecté, et le nom de l'équipe est récupéré à partir de l'ID de l'équipe fourni.

#### Mise à Jour d'Événement

Lors de la mise à jour d'un événement, si l'ID de l'équipe est modifié, le nom de l'équipe est automatiquement mis à jour pour correspondre à la nouvelle équipe.

### 3. Exemples de Composants Frontend

Des exemples de composants React ont été créés pour illustrer comment afficher ces informations dans l'interface utilisateur :

- `CalendarEventCard.jsx.example` : Composant pour afficher un événement individuel avec le nom de l'équipe et du responsable
- `CalendarEventList.jsx.example` : Composant pour afficher une liste d'événements avec fonctionnalité de recherche par nom d'équipe ou de responsable

### 4. Service d'Événements

Le service d'événements (`eventService.js.example.updated`) a été mis à jour pour inclure de nouvelles fonctions permettant de filtrer les événements par nom d'équipe ou nom de responsable.

## Avantages

1. **Performance améliorée** : Les noms sont stockés directement dans le document d'événement, évitant des requêtes supplémentaires pour les récupérer
2. **Interface utilisateur plus informative** : Les utilisateurs peuvent voir immédiatement à quelle équipe appartient un événement et qui en est responsable
3. **Recherche facilitée** : Possibilité de rechercher des événements par nom d'équipe ou de responsable
4. **Meilleure traçabilité** : Identification claire de qui a créé chaque événement

## Utilisation dans le Frontend

Pour utiliser ces nouveaux champs dans l'interface utilisateur :

1. Assurez-vous que vos appels API récupèrent les données complètes des événements
2. Utilisez les propriétés `event.team_name` et `event.created_by_name` pour afficher les informations
3. Vous pouvez vous inspirer des exemples de composants fournis dans le dossier `docs`

## Migration des Données Existantes

Pour les événements existants qui n'ont pas encore ces champs remplis, vous pouvez exécuter un script de migration qui :

1. Récupère tous les événements existants
2. Pour chaque événement, récupère le nom de l'équipe et de l'administrateur correspondants
3. Met à jour l'événement avec ces informations

## Conclusion

Ces modifications permettent une meilleure expérience utilisateur en affichant clairement à quelle équipe appartient un événement et qui en est responsable, tout en optimisant les performances grâce au stockage direct de ces informations dans le document d'événement.