from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required, super_admin_required
from ..mongo_models import User, Team
from ..utils import generate_temp_password, send_temp_password_email
from mongoengine.queryset.visitor import Q
from mongoengine.errors import ValidationError, NotUniqueError
import logging
import re

logger = logging.getLogger(__name__)

class TeamMemberSearchView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def get(self, request, team_id):
        """Rechercher des employés existants pour les ajouter à l'équipe"""
        try:
            # Ajouter des logs de débogage
            logger.info(f"Recherche d'employés pour l'équipe {team_id} par l'utilisateur {request.user.id} ({request.user.name})")

            # Vérifier l'accès à l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier que l'admin est responsable de l'équipe
            if str(request.user.id) != team.responsable:
                logger.warning(f"Accès refusé: l'utilisateur {request.user.id} n'est pas le responsable de l'équipe {team_id}")
                return Response({"error": "Seul l'administrateur responsable de l'équipe peut gérer ses membres"}, status=403)

            search_query = request.GET.get('query', '')
            logger.info(f"Recherche avec la requête: '{search_query}'")

            # Récupérer d'abord les IDs des membres existants
            existing_members = set(team.members.keys())
            logger.info(f"Membres existants: {existing_members}")

            # Compter le nombre total d'employés dans le système
            total_employees = User.objects(role='employee').count()
            logger.info(f"Nombre total d'employés dans le système: {total_employees}")

            # Simplifier la requête pour le débogage
            # D'abord, récupérer tous les employés
            all_employees = User.objects(role='employee')
            logger.info(f"Nombre total d'employés: {len(all_employees)}")

            # Vérifier s'il y a des employés
            if len(all_employees) == 0:
                # Créer un employé de test si aucun n'existe
                logger.warning("Aucun employé trouvé dans la base de données. Création d'un employé de test.")
                test_email = "<EMAIL>"

                # Vérifier si l'employé de test existe déjà
                test_user = User.objects(email=test_email).first()
                if not test_user:
                    temp_password = "password123"  # Mot de passe simple pour le test
                    test_user = User(
                        name="Employé Test",
                        email=test_email,
                        role='employee',
                        temp_password_required=True
                    )
                    test_user.set_password(temp_password)
                    test_user.save()
                    logger.info(f"Employé de test créé: {test_user.id} - {test_user.name}")

                    # Récupérer à nouveau tous les employés
                    all_employees = User.objects(role='employee')

            # Lister tous les employés pour le débogage
            for user in all_employees:
                logger.info(f"Employé dans la base: {user.id} - {user.name} - {user.email}")

            # Filtrer manuellement les employés qui ne sont pas déjà membres
            users = [user for user in all_employees if str(user.id) not in existing_members]

            # Filtrer par recherche si nécessaire
            if search_query:
                users = [user for user in users if search_query.lower() in user.name.lower() or search_query.lower() in user.email.lower()]

            logger.info(f"Nombre d'employés filtrés: {len(users)}")

            # Lister les employés filtrés pour le débogage
            for user in users:
                logger.info(f"Employé filtré: {user.id} - {user.name} - {user.email}")

            return Response({
                'results': [
                    {
                        'id': str(user.id),
                        'name': user.name,
                        'email': user.email
                    } for user in users
                ],
                'count': len(users)
            })

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de la recherche d'employés: {str(e)}")
            return Response({"error": str(e)}, status=500)

class TeamMemberCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request, team_id):
        """Créer un nouvel employé et l'ajouter à l'équipe"""
        try:
            # Vérifier l'accès à l'équipe
            team = Team.objects.get(id=team_id)
            if not request.user.role == 'admin':
                return Response({"error": "Seul un administrateur peut gérer les membres d'une équipe"}, status=403)

            if str(request.user.id) != team.responsable:
                return Response({"error": "Seul l'administrateur responsable de l'équipe peut gérer ses membres"}, status=403)

            data = request.data
            name = data.get('name')
            email = data.get('email')

            # Validations du nom
            if not name:
                return Response({"error": "Le nom est requis"}, status=400)

            name = name.strip()
            if len(name) < 2:
                return Response({"error": "Le nom doit contenir au moins 2 caractères"}, status=400)

            if not re.match(r'^[a-zA-ZÀ-ÿ\s\-\']+$', name):
                return Response({"error": "Le nom contient des caractères non autorisés"}, status=400)

            if not email:
                return Response({"error": "L'email est requis"}, status=400)

            # Valider le format de l'email
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                return Response({"error": "Format d'email invalide"}, status=400)

            # Vérifier si l'email existe déjà
            if User.objects(email__iexact=email).first():
                return Response({"error": "Un utilisateur avec cet email existe déjà"}, status=400)

            # Créer le nouvel employé
            temp_password = generate_temp_password()
            user = User(
                name=name,
                email=email,
                role='employee',
                temp_password_required=True
            )
            user.set_password(temp_password)

            # Utiliser un bloc try-except pour gérer les erreurs de sauvegarde
            try:
                user.save()

                # Ajouter à l'équipe - avec gestion d'erreur améliorée
                try:
                    team.add_member(user)
                except Exception as team_error:
                    logger.error(f"Erreur lors de l'ajout à l'équipe: {str(team_error)}")
                    # Ne pas supprimer l'utilisateur, juste logger l'erreur

                # Envoyer l'email avec les identifiants en arrière-plan
                # Même si l'email échoue, l'utilisateur est créé et ajouté à l'équipe
                try:
                    # Lancer l'envoi d'email sans attendre la réponse
                    # Dans un environnement de production, utilisez une file d'attente comme Celery
                    import threading
                    email_thread = threading.Thread(
                        target=send_temp_password_email,
                        args=(email, temp_password, name)
                    )
                    email_thread.daemon = True
                    email_thread.start()
                except Exception as e:
                    logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
                    # Ne pas supprimer l'utilisateur, juste logger l'erreur
            except Exception as save_error:
                logger.error(f"Erreur lors de la sauvegarde de l'utilisateur: {str(save_error)}")
                return Response({
                    "error": "Impossible de créer l'utilisateur",
                    "detail": str(save_error)
                }, status=500)

            return Response({
                "message": "Employé créé et ajouté à l'équipe avec succès",
                "member": {
                    "id": str(user.id),
                    "name": user.name,
                    "email": user.email
                }
            }, status=201)

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except ValidationError as e:
            logger.error(f"Erreur de validation lors de la création d'un membre: {str(e)}")
            return Response({"error": "Données invalides", "detail": str(e)}, status=400)
        except NotUniqueError as e:
            logger.error(f"Erreur d'unicité lors de la création d'un membre: {str(e)}")
            return Response({"error": "Un utilisateur avec ces informations existe déjà"}, status=400)
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la création d'un membre: {str(e)}")
            return Response({"error": "Une erreur inattendue s'est produite"}, status=500)


class TeamMemberAddView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request, team_id):
        try:
            # Vérifier l'accès à l'équipe
            team = Team.objects.get(id=team_id)
            if not request.user.role == 'admin':
                return Response({"error": "Seul un administrateur peut gérer les membres d'une équipe"}, status=403)

            if str(request.user.id) != team.responsable:
                return Response({"error": "Seul l'administrateur responsable de l'équipe peut gérer ses membres"}, status=403)

            user_id = request.data.get('user_id')
            if not user_id:
                return Response({"error": "L'ID de l'utilisateur est requis"}, status=400)

            # Vérifier que l'utilisateur existe
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({"error": "Utilisateur non trouvé"}, status=404)

            # Vérifier que l'utilisateur est un employé
            if user.role != 'employee':
                return Response({"error": "Seuls les employés peuvent être ajoutés à une équipe"}, status=400)

            # Ajouter le membre à l'équipe
            try:
                team.add_member(user)
                # Récupérer les informations complètes du membre ajouté
                member_info = team.members.get(str(user.id), {})
                return Response({
                    "message": "Membre ajouté avec succès",
                    "member": {
                        "id": str(user.id),
                        "name": user.name,
                        "email": user.email,
                        "role": member_info.get('role', 'employee'),
                        "added_at": member_info.get('added_at')
                    }
                })
            except ValueError as e:
                return Response({"error": str(e)}, status=400)

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de l'ajout d'un membre: {str(e)}")
            return Response({"error": "Une erreur inattendue s'est produite"}, status=500)

class TeamMemberView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def delete(self, request, team_id, member_id):
        """Supprimer un membre de l'équipe"""
        try:
            # Vérifier que les IDs sont valides
            if not team_id or not member_id:
                return Response({"error": "Les identifiants de l'équipe et du membre sont requis"}, status=400)

            # Récupérer l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier d'abord si le membre existe dans l'équipe
            if member_id not in team.members:
                return Response({"error": "Ce membre n'appartient pas à l'équipe"}, status=404)

            # Vérifier que l'utilisateur est un admin
            if not request.user.role == 'admin':
                return Response({"error": "Seul un administrateur peut gérer les membres d'une équipe"}, status=403)

            # Vérifier que l'utilisateur est le responsable de l'équipe
            if str(request.user.id) != team.responsable:
                return Response({"error": "Seul l'administrateur responsable de l'équipe peut gérer ses membres"}, status=403)

            # Supprimer directement le membre du dictionnaire et sauvegarder
            try:
                del team.members[member_id]
                team.save()
                return Response({"message": "Membre retiré avec succès"}, status=200)
            except Exception as e:
                logger.error(f"Erreur lors de la suppression du membre: {str(e)}")
                return Response({"error": "Erreur lors de la suppression du membre"}, status=500)

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de la suppression du membre: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression"}, status=500)