# Tests Postman pour les Événements du Calendrier

Ce document décrit les tests à effectuer avec Postman pour vérifier le bon fonctionnement de l'API des événements du calendrier.

## Prérequis

1. Avoir Postman installé
2. Avoir le serveur backend en cours d'exécution
3. Avoir un compte administrateur et un compte employé pour tester les différentes permissions

## Configuration de l'environnement Postman

Créez un environnement avec les variables suivantes :

- `base_url` : URL de base de votre API (ex: http://localhost:8000/api)
- `admin_token` : Token JWT d'un utilisateur administrateur
- `employee_token` : Token JWT d'un utilisateur employé
- `event_id` : Variable qui sera utilisée pour stocker l'ID d'un événement créé

## Tests à effectuer

### 1. Authentification

#### 1.1 Connexion en tant qu'administrateur

- **Méthode** : POST
- **URL** : `{{base_url}}/login/`
- **Corps** :
```json
{
    "email": "<EMAIL>",
    "password": "votre_mot_de_passe"
}
```
- **Test** : Vérifier que la réponse contient un token JWT
- **Action** : Stocker le token dans la variable `admin_token`

#### 1.2 Connexion en tant qu'employé

- **Méthode** : POST
- **URL** : `{{base_url}}/login/`
- **Corps** :
```json
{
    "email": "<EMAIL>",
    "password": "votre_mot_de_passe"
}
```
- **Test** : Vérifier que la réponse contient un token JWT
- **Action** : Stocker le token dans la variable `employee_token`

### 2. Gestion des événements (Admin)

#### 2.1 Création d'un événement

- **Méthode** : POST
- **URL** : `{{base_url}}/events/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Corps** :
```json
{
    "title": "Réunion d'équipe",
    "description": "Discussion sur les objectifs du sprint",
    "start_date": "2023-12-01",
    "end_date": "2023-12-01",
    "start_time": "09:00",
    "end_time": "10:30",
    "note": "Préparer les rapports d'avancement",
    "team_id": "ID_DE_VOTRE_EQUIPE"
}
```
- **Test** : Vérifier que la réponse a un statut 201 et contient les détails de l'événement créé
- **Action** : Stocker l'ID de l'événement dans la variable `event_id`

#### 2.2 Récupération de tous les événements

- **Méthode** : GET
- **URL** : `{{base_url}}/events/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Test** : Vérifier que la réponse a un statut 200 et contient une liste d'événements

#### 2.3 Récupération d'un événement spécifique

- **Méthode** : GET
- **URL** : `{{base_url}}/events/{{event_id}}/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Test** : Vérifier que la réponse a un statut 200 et contient les détails de l'événement

#### 2.4 Mise à jour d'un événement

- **Méthode** : PUT
- **URL** : `{{base_url}}/events/{{event_id}}/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Corps** :
```json
{
    "title": "Réunion d'équipe (mise à jour)",
    "description": "Discussion sur les objectifs du sprint et revue des tâches",
    "start_date": "2023-12-01",
    "end_date": "2023-12-01",
    "start_time": "09:30",
    "end_time": "11:00",
    "note": "Préparer les rapports d'avancement et les démonstrations"
}
```
- **Test** : Vérifier que la réponse a un statut 200 et contient les détails mis à jour

#### 2.5 Archivage d'un événement

- **Méthode** : PUT
- **URL** : `{{base_url}}/events/{{event_id}}/archive/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Test** : Vérifier que la réponse a un statut 200 et que le statut de l'événement est "archived"

### 3. Gestion des événements (Employé)

#### 3.1 Récupération de tous les événements

- **Méthode** : GET
- **URL** : `{{base_url}}/events/`
- **Headers** : 
  - Authorization: Bearer {{employee_token}}
- **Test** : Vérifier que la réponse a un statut 200 et contient uniquement les événements auxquels l'employé a accès

#### 3.2 Récupération d'un événement spécifique

- **Méthode** : GET
- **URL** : `{{base_url}}/events/{{event_id}}/`
- **Headers** : 
  - Authorization: Bearer {{employee_token}}
- **Test** : Vérifier que la réponse a un statut 200 si l'employé a accès à cet événement, sinon 403

#### 3.3 Mise à jour du statut d'un événement

- **Méthode** : PUT
- **URL** : `{{base_url}}/events/{{event_id}}/status/`
- **Headers** : 
  - Authorization: Bearer {{employee_token}}
- **Corps** :
```json
{
    "status": "completed"
}
```
- **Test** : Vérifier que la réponse a un statut 200 si l'employé a le droit de mettre à jour le statut, sinon 403

#### 3.4 Tentative de création d'un événement (non autorisé)

- **Méthode** : POST
- **URL** : `{{base_url}}/events/`
- **Headers** : 
  - Authorization: Bearer {{employee_token}}
- **Corps** : (même corps que pour la création par l'admin)
- **Test** : Vérifier que la réponse a un statut 403 (Forbidden)

#### 3.5 Tentative de modification d'un événement (non autorisé)

- **Méthode** : PUT
- **URL** : `{{base_url}}/events/{{event_id}}/`
- **Headers** : 
  - Authorization: Bearer {{employee_token}}
- **Corps** : (même corps que pour la mise à jour par l'admin)
- **Test** : Vérifier que la réponse a un statut 403 (Forbidden)

### 4. Suppression d'un événement (Admin)

- **Méthode** : DELETE
- **URL** : `{{base_url}}/events/{{event_id}}/`
- **Headers** : 
  - Authorization: Bearer {{admin_token}}
- **Test** : Vérifier que la réponse a un statut 200 et contient un message de succès

## Scénarios de test complets

### Scénario 1: Cycle de vie complet d'un événement

1. L'admin crée un événement pour une équipe
2. L'admin vérifie que l'événement apparaît dans la liste
3. L'employé (membre de l'équipe) vérifie qu'il peut voir l'événement
4. L'employé met à jour le statut de l'événement à "completed"
5. L'admin vérifie que le statut a bien été mis à jour
6. L'admin archive l'événement
7. L'admin et l'employé vérifient que l'événement apparaît comme archivé

### Scénario 2: Gestion des permissions

1. L'admin crée un événement pour un membre spécifique (pas une équipe entière)
2. Un employé qui n'est pas le membre assigné tente d'accéder à l'événement (doit recevoir 403)
3. Le membre assigné accède à l'événement (doit réussir)
4. Le membre assigné met à jour le statut (doit réussir)
5. Un autre employé tente de mettre à jour le statut (doit recevoir 403)

## Notes importantes

- Assurez-vous de remplacer les valeurs comme `ID_DE_VOTRE_EQUIPE` par des valeurs réelles de votre base de données
- Les tests doivent être exécutés dans l'ordre indiqué pour garantir que les dépendances entre les tests sont respectées
- Si un test échoue, vérifiez les logs du serveur pour plus d'informations sur l'erreur