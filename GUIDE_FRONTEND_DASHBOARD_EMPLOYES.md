# 📊 Guide Frontend - Dashboard Employés/Clients

## 🎯 Vue d'Ensemble

Guide complet pour implémenter les dashboards en temps réel pour les employés et clients avec des graphiques en secteurs (pie charts) qui importent correctement les données de la base.

## 🔗 **ENDPOINT BACKEND**

### **URL de Base**
```javascript
const API_BASE_URL = 'http://localhost:8000/api/bi/employee/dashboard/';
```

### **Authentification**
```javascript
const headers = {
  'Authorization': `Bear<PERSON> ${userToken}`,
  'Content-Type': 'application/json'
};
```

## 📊 **STRUCTURE DES DONNÉES**

### **Réponse API Complète**
```javascript
{
  timestamp: "2024-01-15T10:30:00Z",
  is_realtime: true,
  user_info: {
    user_id: "employee_id",
    user_name: "Nom Employé", 
    user_role: "employee", // ou "client"
    teams_count: 2
  },
  metric_cards: [
    {
      title: "Tâches personnelles",
      value: 15,
      completion_rate: 66.67,
      icon: "clipboard-list",
      color: "#3B82F6",
      subtitle: "10 terminées sur 15"
    }
    // ... autres cartes
  ],
  charts: {
    personal_tasks: {
      type: "pie",
      title: "Distribution des tâches personnelles par statut",
      data: [
        {name: "À faire", value: 3, color: "#3B82F6"},
        {name: "En cours", value: 2, color: "#F59E0B"},
        {name: "Terminées", value: 10, color: "#10B981"}
      ],
      legend: [
        {label: "À faire", color: "#3B82F6"},
        {label: "En cours", color: "#F59E0B"},
        {label: "Terminées", color: "#10B981"}
      ],
      total: 15,
      completion_rate: 66.67
    }
    // ... autres graphiques
  },
  detailed_stats: {
    // Données détaillées pour analyses
  },
  metadata: {
    last_updated: "2024-01-15T10:30:00Z",
    refresh_mode: "manual",
    dashboard_title: "Tableau de Bord - Nom Employé",
    period: "today"
  }
}
```

## 🎨 **COMPOSANTS REACT SUGGÉRÉS**

### **1. Composant Principal**
```jsx
import React, { useState, useEffect } from 'react';
import { PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

const EmployeeDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('today');
  const [manualRefresh, setManualRefresh] = useState(true);

  // Récupération des données
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const response = await fetch(
        `http://localhost:8000/api/bi/employee/dashboard/?period=${period}&manual_refresh=${manualRefresh}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
      } else {
        console.error('Erreur lors de la récupération des données');
      }
    } catch (error) {
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Mise à jour automatique si manual_refresh = false
    let interval;
    if (!manualRefresh) {
      interval = setInterval(fetchDashboardData, 30000); // 30 secondes
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [period, manualRefresh]);

  if (loading) return <div>Chargement...</div>;
  if (!dashboardData) return <div>Erreur de chargement</div>;

  return (
    <div className="employee-dashboard">
      <DashboardHeader 
        title={dashboardData.metadata.dashboard_title}
        subtitle={dashboardData.metadata.dashboard_subtitle}
        lastUpdated={dashboardData.metadata.last_updated}
      />
      
      <PeriodFilter 
        period={period} 
        setPeriod={setPeriod}
        manualRefresh={manualRefresh}
        setManualRefresh={setManualRefresh}
        onRefresh={fetchDashboardData}
      />
      
      <MetricCardsRow cards={dashboardData.metric_cards} />
      
      <ChartsGrid 
        charts={dashboardData.charts}
        userRole={dashboardData.user_info.user_role}
      />
    </div>
  );
};
```

### **2. Composant Cartes de Métriques**
```jsx
const MetricCardsRow = ({ cards }) => {
  return (
    <div className="metric-cards-row">
      {cards.map((card, index) => (
        <MetricCard key={index} {...card} />
      ))}
    </div>
  );
};

const MetricCard = ({ title, value, completion_rate, icon, color, subtitle }) => {
  return (
    <div className="metric-card" style={{ borderLeftColor: color }}>
      <div className="metric-card-header">
        <h3>{title}</h3>
        <i className={`icon ${icon}`} style={{ color }}></i>
      </div>
      <div className="metric-card-body">
        <div className="metric-value">{value}</div>
        <div className="metric-subtitle">{subtitle}</div>
        <div className="completion-rate">
          Taux de completion: {completion_rate.toFixed(1)}%
        </div>
      </div>
    </div>
  );
};
```

### **3. Composant Graphiques en Secteurs**
```jsx
const ChartsGrid = ({ charts, userRole }) => {
  return (
    <div className="charts-grid">
      <div className="chart-container">
        <PieChartComponent 
          data={charts.personal_tasks}
          title="Tâches Personnelles"
        />
      </div>
      
      <div className="chart-container">
        <PieChartComponent 
          data={charts.personal_events}
          title="Événements Personnels"
        />
      </div>
      
      {userRole === 'employee' && charts.team_tasks && (
        <div className="chart-container">
          <PieChartComponent 
            data={charts.team_tasks}
            title="Tâches d'Équipe"
          />
        </div>
      )}
      
      {userRole === 'employee' && charts.team_events && (
        <div className="chart-container">
          <PieChartComponent 
            data={charts.team_events}
            title="Événements d'Équipe"
          />
        </div>
      )}
    </div>
  );
};

const PieChartComponent = ({ data, title }) => {
  // Filtrer les données avec valeur > 0 pour un affichage propre
  const chartData = data.data.filter(item => item.value > 0);
  
  return (
    <div className="pie-chart-container">
      <h4 className="chart-title">{title}</h4>
      <div className="chart-stats">
        <span>Total: {data.total}</span>
        <span>Completion: {data.completion_rate.toFixed(1)}%</span>
      </div>
      
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            outerRadius={80}
            dataKey="value"
            label={({ name, value, percent }) => 
              `${name}: ${value} (${(percent * 100).toFixed(0)}%)`
            }
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value, name) => [value, name]}
            labelFormatter={() => ''}
          />
          <Legend 
            verticalAlign="bottom" 
            height={36}
            formatter={(value, entry) => (
              <span style={{ color: entry.color }}>{value}</span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};
```

### **4. Composant Filtres de Période**
```jsx
const PeriodFilter = ({ period, setPeriod, manualRefresh, setManualRefresh, onRefresh }) => {
  const periods = [
    { value: 'today', label: 'Aujourd\'hui' },
    { value: '1h', label: 'Dernière heure' },
    { value: '24h', label: 'Dernières 24h' },
    { value: '7d', label: 'Derniers 7 jours' },
    { value: '30d', label: 'Derniers 30 jours' }
  ];

  return (
    <div className="period-filter">
      <div className="period-buttons">
        {periods.map(p => (
          <button
            key={p.value}
            className={`period-btn ${period === p.value ? 'active' : ''}`}
            onClick={() => setPeriod(p.value)}
          >
            {p.label}
          </button>
        ))}
      </div>
      
      <div className="refresh-controls">
        <label>
          <input
            type="checkbox"
            checked={manualRefresh}
            onChange={(e) => setManualRefresh(e.target.checked)}
          />
          Mise à jour manuelle
        </label>
        
        {manualRefresh && (
          <button className="refresh-btn" onClick={onRefresh}>
            🔄 Actualiser
          </button>
        )}
      </div>
    </div>
  );
};
```

## 🎨 **STYLES CSS SUGGÉRÉS**

```css
.employee-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.metric-cards-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-left: 4px solid;
}

.metric-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

.metric-subtitle {
  color: #666;
  font-size: 0.9rem;
  margin: 5px 0;
}

.completion-rate {
  color: #10B981;
  font-weight: 500;
  font-size: 0.9rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-title {
  text-align: center;
  margin-bottom: 10px;
  color: #333;
}

.chart-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #666;
}

.period-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.period-buttons {
  display: flex;
  gap: 10px;
}

.period-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.period-btn.active {
  background: #3B82F6;
  color: white;
  border-color: #3B82F6;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.refresh-btn {
  padding: 8px 16px;
  background: #10B981;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
```

## 🔄 **GESTION DES MISES À JOUR**

### **Mise à Jour Automatique**
```javascript
// Mise à jour toutes les 30 secondes si manual_refresh = false
useEffect(() => {
  let interval;
  if (!manualRefresh) {
    interval = setInterval(() => {
      fetchDashboardData();
    }, 30000);
  }
  
  return () => {
    if (interval) clearInterval(interval);
  };
}, [manualRefresh]);
```

### **Gestion des Erreurs**
```javascript
const fetchDashboardData = async () => {
  try {
    const response = await fetch(API_URL, { headers });
    
    if (!response.ok) {
      if (response.status === 403) {
        // Rediriger vers la page de connexion
        window.location.href = '/login';
        return;
      }
      throw new Error(`HTTP ${response.status}`);
    }
    
    const data = await response.json();
    setDashboardData(data);
    setError(null);
  } catch (error) {
    setError('Erreur lors du chargement des données');
    console.error('Erreur:', error);
  }
};
```

## 📱 **RESPONSIVE DESIGN**

```css
@media (max-width: 768px) {
  .metric-cards-row {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .period-filter {
    flex-direction: column;
    gap: 15px;
  }
  
  .period-buttons {
    flex-wrap: wrap;
    justify-content: center;
  }
}
```

## ✅ **POINTS CLÉS À RETENIR**

1. **Données en temps réel** : Importées directement depuis la base MongoDB
2. **Différenciation par rôle** : Employés voient équipes, clients non
3. **Graphiques interactifs** : Pie charts avec tooltips et légendes
4. **Filtrage par période** : Support des mêmes périodes que les autres dashboards
5. **Mise à jour automatique** : Configurable via manual_refresh
6. **Gestion d'erreurs** : Authentification et erreurs réseau
7. **Design responsive** : Adaptation mobile et desktop
8. **Couleurs cohérentes** : Palette standardisée selon les statuts
