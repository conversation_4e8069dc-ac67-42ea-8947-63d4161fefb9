/**
 * Composant de calendrier pour afficher et gérer les événements
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/components/calendar/
 */

import React, { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'moment/locale/fr';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { useAuth } from '../../contexts/AuthContext';
import { getEvents, updateEventStatus, createEvent, updateEvent, deleteEvent, archiveEvent } from '../../services/eventService';

// Configuration du localizer pour le calendrier
moment.locale('fr');
const localizer = momentLocalizer(moment);

// Composant modal pour afficher/éditer les détails d'un événement
const EventModal = ({ event, isOpen, onClose, onSave, onDelete, onArchive, onUpdateStatus, isAdmin }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_date: '',
    end_date: '',
    start_time: '',
    end_time: '',
    note: '',
    status: '',
    team_id: '',
    member_id: ''
  });

  useEffect(() => {
    if (event) {
      // Formater les dates pour l'affichage dans le formulaire
      const startDate = moment(event.start_date).format('YYYY-MM-DD');
      const endDate = moment(event.end_date).format('YYYY-MM-DD');
      
      setFormData({
        title: event.title || '',
        description: event.description || '',
        start_date: startDate,
        end_date: endDate,
        start_time: event.start_time || '',
        end_time: event.end_time || '',
        note: event.note || '',
        status: event.status || 'pending',
        team_id: event.team_id || '',
        member_id: event.member_id || ''
      });
    }
  }, [event]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  const handleStatusUpdate = (newStatus) => {
    onUpdateStatus(event.id, newStatus);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <h2>{isAdmin && !event.id ? 'Créer un événement' : 'Détails de l\'événement'}</h2>
        
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label>Titre</label>
            <input 
              type="text" 
              name="title" 
              value={formData.title} 
              onChange={handleChange} 
              readOnly={!isAdmin}
              required 
            />
          </div>
          
          <div className="form-group">
            <label>Description</label>
            <textarea 
              name="description" 
              value={formData.description} 
              onChange={handleChange} 
              readOnly={!isAdmin}
            />
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>Date de début</label>
              <input 
                type="date" 
                name="start_date" 
                value={formData.start_date} 
                onChange={handleChange} 
                readOnly={!isAdmin}
                required 
              />
            </div>
            
            <div className="form-group">
              <label>Heure de début</label>
              <input 
                type="time" 
                name="start_time" 
                value={formData.start_time} 
                onChange={handleChange} 
                readOnly={!isAdmin}
                required 
              />
            </div>
          </div>
          
          <div className="form-row">
            <div className="form-group">
              <label>Date de fin</label>
              <input 
                type="date" 
                name="end_date" 
                value={formData.end_date} 
                onChange={handleChange} 
                readOnly={!isAdmin}
                required 
              />
            </div>
            
            <div className="form-group">
              <label>Heure de fin</label>
              <input 
                type="time" 
                name="end_time" 
                value={formData.end_time} 
                onChange={handleChange} 
                readOnly={!isAdmin}
                required 
              />
            </div>
          </div>
          
          <div className="form-group">
            <label>Note</label>
            <textarea 
              name="note" 
              value={formData.note} 
              onChange={handleChange} 
              readOnly={!isAdmin}
            />
          </div>
          
          {isAdmin && (
            <>
              <div className="form-group">
                <label>Équipe</label>
                <select 
                  name="team_id" 
                  value={formData.team_id} 
                  onChange={handleChange}
                >
                  <option value="">Sélectionner une équipe</option>
                  {/* Ici, vous devriez mapper sur la liste des équipes disponibles */}
                </select>
              </div>
              
              <div className="form-group">
                <label>Membre</label>
                <select 
                  name="member_id" 
                  value={formData.member_id} 
                  onChange={handleChange}
                >
                  <option value="">Sélectionner un membre</option>
                  {/* Ici, vous devriez mapper sur la liste des membres disponibles */}
                </select>
              </div>
            </>
          )}
          
          <div className="form-group">
            <label>Statut</label>
            <div className="status-display">{formData.status}</div>
          </div>
          
          <div className="modal-actions">
            {isAdmin ? (
              <>
                {event.id ? (
                  <>
                    <button type="submit" className="btn-primary">Enregistrer</button>
                    <button type="button" className="btn-danger" onClick={() => onDelete(event.id)}>Supprimer</button>
                    <button type="button" className="btn-warning" onClick={() => onArchive(event.id)}>Archiver</button>
                  </>
                ) : (
                  <button type="submit" className="btn-primary">Créer</button>
                )}
              </>
            ) : (
              event.can_update_status && (
                <div className="status-actions">
                  <button 
                    type="button" 
                    className={`btn-status ${formData.status === 'completed' ? 'active' : ''}`}
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    Marquer comme terminé
                  </button>
                  <button 
                    type="button" 
                    className={`btn-status ${formData.status === 'pending' ? 'active' : ''}`}
                    onClick={() => handleStatusUpdate('pending')}
                  >
                    Marquer comme en attente
                  </button>
                </div>
              )
            )}
            <button type="button" className="btn-secondary" onClick={onClose}>Fermer</button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Composant principal du calendrier
const CalendarView = () => {
  const [events, setEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const { user } = useAuth();
  
  const isAdmin = user && user.role === 'admin';

  // Charger les événements au chargement du composant
  useEffect(() => {
    loadEvents();
  }, []);

  // Fonction pour charger les événements
  const loadEvents = async () => {
    try {
      const eventsData = await getEvents();
      
      // Formater les événements pour le calendrier
      const formattedEvents = eventsData.map(event => ({
        ...event,
        start: new Date(`${event.start_date.split('T')[0]}T${event.start_time}:00`),
        end: new Date(`${event.end_date.split('T')[0]}T${event.end_time}:00`)
      }));
      
      setEvents(formattedEvents);
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error);
    }
  };

  // Gérer la sélection d'un événement
  const handleSelectEvent = (event) => {
    setSelectedEvent(event);
    setIsCreating(false);
    setIsModalOpen(true);
  };

  // Gérer la création d'un nouvel événement
  const handleSelectSlot = ({ start }) => {
    if (isAdmin) {
      const startDate = moment(start).format('YYYY-MM-DD');
      const startTime = moment(start).format('HH:mm');
      const endTime = moment(start).add(1, 'hour').format('HH:mm');
      
      setSelectedEvent({
        title: '',
        description: '',
        start_date: startDate,
        end_date: startDate,
        start_time: startTime,
        end_time: endTime,
        note: '',
        status: 'pending',
        team_id: '',
        member_id: ''
      });
      setIsCreating(true);
      setIsModalOpen(true);
    }
  };

  // Fermer le modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedEvent(null);
  };

  // Sauvegarder un événement (création ou mise à jour)
  const handleSaveEvent = async (formData) => {
    try {
      if (isCreating) {
        // Créer un nouvel événement
        await createEvent(formData);
      } else {
        // Mettre à jour un événement existant
        await updateEvent(selectedEvent.id, formData);
      }
      
      // Recharger les événements
      await loadEvents();
      handleCloseModal();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'événement:', error);
    }
  };

  // Supprimer un événement
  const handleDeleteEvent = async (eventId) => {
    try {
      await deleteEvent(eventId);
      await loadEvents();
      handleCloseModal();
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'événement:', error);
    }
  };

  // Archiver un événement
  const handleArchiveEvent = async (eventId) => {
    try {
      await archiveEvent(eventId);
      await loadEvents();
      handleCloseModal();
    } catch (error) {
      console.error('Erreur lors de l\'archivage de l\'événement:', error);
    }
  };

  // Mettre à jour le statut d'un événement
  const handleUpdateEventStatus = async (eventId, status) => {
    try {
      await updateEventStatus(eventId, status);
      await loadEvents();
      handleCloseModal();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut de l\'événement:', error);
    }
  };

  // Personnaliser l'affichage des événements dans le calendrier
  const eventStyleGetter = (event) => {
    let backgroundColor = '#3174ad'; // Couleur par défaut
    
    // Changer la couleur selon le statut
    if (event.status === 'completed') {
      backgroundColor = '#5cb85c'; // Vert pour les événements terminés
    } else if (event.status === 'archived') {
      backgroundColor = '#777777'; // Gris pour les événements archivés
    }
    
    return {
      style: {
        backgroundColor,
        borderRadius: '5px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  return (
    <div className="calendar-container">
      <h1>Calendrier des événements</h1>
      
      {isAdmin && (
        <div className="calendar-actions">
          <button 
            className="btn-primary" 
            onClick={() => {
              setSelectedEvent({
                title: '',
                description: '',
                start_date: moment().format('YYYY-MM-DD'),
                end_date: moment().format('YYYY-MM-DD'),
                start_time: '09:00',
                end_time: '10:00',
                note: '',
                status: 'pending',
                team_id: '',
                member_id: ''
              });
              setIsCreating(true);
              setIsModalOpen(true);
            }}
          >
            Créer un événement
          </button>
        </div>
      )}
      
      <div className="calendar-wrapper">
        <Calendar
          localizer={localizer}
          events={events}
          startAccessor="start"
          endAccessor="end"
          style={{ height: 600 }}
          onSelectEvent={handleSelectEvent}
          onSelectSlot={handleSelectSlot}
          selectable={isAdmin}
          eventPropGetter={eventStyleGetter}
          views={['month', 'week', 'day', 'agenda']}
          messages={{
            next: "Suivant",
            previous: "Précédent",
            today: "Aujourd'hui",
            month: "Mois",
            week: "Semaine",
            day: "Jour",
            agenda: "Agenda"
          }}
        />
      </div>
      
      <EventModal 
        event={selectedEvent} 
        isOpen={isModalOpen} 
        onClose={handleCloseModal} 
        onSave={handleSaveEvent}
        onDelete={handleDeleteEvent}
        onArchive={handleArchiveEvent}
        onUpdateStatus={handleUpdateEventStatus}
        isAdmin={isAdmin}
      />
      
      {/* Styles CSS pour le modal et les boutons */}
      <style jsx>{`
        .calendar-container {
          padding: 20px;
        }
        
        .calendar-actions {
          margin-bottom: 20px;
        }
        
        .calendar-wrapper {
          height: 600px;
          background-color: white;
          padding: 10px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }
        
        .modal-content {
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          width: 90%;
          max-width: 600px;
          max-height: 90vh;
          overflow-y: auto;
        }
        
        .form-group {
          margin-bottom: 15px;
        }
        
        .form-row {
          display: flex;
          gap: 15px;
        }
        
        .form-row .form-group {
          flex: 1;
        }
        
        label {
          display: block;
          margin-bottom: 5px;
          font-weight: 500;
        }
        
        input, textarea, select {
          width: 100%;
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        
        .status-display {
          padding: 8px;
          border-radius: 4px;
          background-color: #f5f5f5;
          text-transform: capitalize;
        }
        
        .modal-actions {
          display: flex;
          gap: 10px;
          margin-top: 20px;
          justify-content: flex-end;
        }
        
        .btn-primary {
          background-color: #3174ad;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-danger {
          background-color: #dc3545;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-warning {
          background-color: #ffc107;
          color: #212529;
          border: none;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .status-actions {
          display: flex;
          gap: 10px;
        }
        
        .btn-status {
          background-color: #f8f9fa;
          border: 1px solid #ddd;
          padding: 8px 16px;
          border-radius: 4px;
          cursor: pointer;
        }
        
        .btn-status.active {
          background-color: #3174ad;
          color: white;
          border-color: #3174ad;
        }
      `}</style>
    </div>
  );
};

export default CalendarView;