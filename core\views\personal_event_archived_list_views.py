from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..mongo_models import PersonalEvent
import logging

logger = logging.getLogger(__name__)

class PersonalEventArchivedListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Liste des événements personnels archivés de l'utilisateur connecté"""
        try:
            user = request.user

            # Récupérer uniquement les événements personnels ARCHIVÉS créés par l'utilisateur connecté
            archived_events = PersonalEvent.objects(
                created_by=str(user.id),
                status='archived'  # Seulement les événements archivés
            ).order_by('-updated_at')  # Trier par date de mise à jour décroissante

            # Formater les événements archivés
            events_data = []
            for event in archived_events:
                events_data.append({
                    'id': str(event.id),
                    'title': event.title,
                    'description': event.description,
                    'start_date': event.start_date,
                    'end_date': event.end_date,
                    'start_time': event.start_time,
                    'end_time': event.end_time,
                    'note': event.note,
                    'status': event.status,
                    'color': event.color,
                    'created_by': event.created_by,
                    'created_by_name': event.created_by_name,
                    'created_at': event.created_at,
                    'updated_at': event.updated_at,
                    'archived_at': event.updated_at,  # Date d'archivage
                    'can_manage': event.can_manage_event(user),
                    'can_update_status': event.can_update_status(user),
                    'can_unarchive': event.can_manage_event(user)  # Peut désarchiver
                })

            return Response({
                'archived_events': events_data,
                'total_archived': len(events_data),
                'message': f"{len(events_data)} événement(s) archivé(s) trouvé(s)"
            })

        except Exception as e:
            logger.error(f"Error in PersonalEventArchivedListView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la récupération des événements archivés"}, status=500)
