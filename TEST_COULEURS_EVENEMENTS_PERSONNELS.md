# 🧪 Tests Couleurs - Événements Personnels

## 🎯 Objectif
Tester la validation et la gestion des couleurs pour les événements personnels après les corrections backend.

## 📋 Prérequis
- Backend démarré sur `http://localhost:8000`
- Token d'authentification d'un employé ou client
- Postman ou outil similaire

## 🔐 Authentification
```
POST http://localhost:8000/api/auth/login/
Content-Type: application/json

{
    "email": "[EMAIL_EMPLOYE_OU_CLIENT]",
    "password": "[MOT_DE_PASSE]"
}
```

## ✅ Test 1 : Création avec Couleur Prédéfinie

```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Réunion personnelle",
    "description": "Test couleur prédéfinie",
    "start_date": "2025-01-25T10:00:00Z",
    "end_date": "2025-01-25T11:00:00Z",
    "start_time": "10:00",
    "end_time": "11:00",
    "color": "bleu_personnel",
    "note": "Test couleur"
}
```

**Résultat attendu :** ✅ Création réussie avec couleur `#3788d8`

## ✅ Test 2 : Création avec Code Hexadécimal

```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Événement coloré",
    "description": "Test couleur hex",
    "start_date": "2025-01-25T14:00:00Z",
    "end_date": "2025-01-25T15:00:00Z",
    "start_time": "14:00",
    "end_time": "15:00",
    "color": "#ff5733",
    "note": "Couleur personnalisée"
}
```

**Résultat attendu :** ✅ Création réussie avec couleur `#ff5733`

## ✅ Test 3 : Création sans Couleur (Suggestion Automatique)

```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Formation importante",
    "description": "Test suggestion couleur",
    "start_date": "2025-01-25T16:00:00Z",
    "end_date": "2025-01-25T17:00:00Z",
    "start_time": "16:00",
    "end_time": "17:00",
    "note": "Sans couleur spécifiée"
}
```

**Résultat attendu :** ✅ Création réussie avec couleur suggérée automatiquement

## ❌ Test 4 : Couleur Invalide

```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test couleur invalide",
    "description": "Test validation",
    "start_date": "2025-01-25T18:00:00Z",
    "end_date": "2025-01-25T19:00:00Z",
    "start_time": "18:00",
    "end_time": "19:00",
    "color": "couleur_inexistante",
    "note": "Couleur invalide"
}
```

**Résultat attendu :** ❌ Erreur 400 avec message de validation

## ✅ Test 5 : Modification de Couleur

```
# D'abord récupérer un événement existant
GET http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]

# Puis modifier sa couleur
PUT http://localhost:8000/api/personal-events/[EVENT_ID]/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "color": "vert_personnel"
}
```

**Résultat attendu :** ✅ Modification réussie avec nouvelle couleur

## ✅ Test 6 : Vérification de la Couleur dans la Liste

```
GET http://localhost:8000/api/personal-events/
Authorization: Bearer [TOKEN]
```

**Résultat attendu :** ✅ Tous les événements affichent leur couleur correctement

## 🎨 Couleurs Disponibles pour Tests

### Couleurs Personnelles :
- `bleu_personnel` → `#3788d8`
- `vert_personnel` → `#10b981`
- `rouge_personnel` → `#ef4444`
- `violet_personnel` → `#8b5cf6`
- `rose_personnel` → `#ec4899`
- `orange_personnel` → `#f97316`

### Codes Hexadécimaux Valides :
- `#ff5733` (Orange vif)
- `#33ff57` (Vert vif)
- `#5733ff` (Bleu violet)
- `#ff3357` (Rouge rose)

## 📊 Résultats Attendus

| Test | Statut | Couleur Finale | Notes |
|------|--------|----------------|-------|
| Test 1 | ✅ 201 | `#3788d8` | Couleur prédéfinie |
| Test 2 | ✅ 201 | `#ff5733` | Code hex direct |
| Test 3 | ✅ 201 | Suggérée | Basée sur le titre |
| Test 4 | ❌ 400 | - | Validation échoue |
| Test 5 | ✅ 200 | `#10b981` | Modification OK |
| Test 6 | ✅ 200 | Toutes | Liste complète |

## 🔍 Points de Vérification

1. **Validation Backend :** Les couleurs invalides sont rejetées
2. **Suggestion Automatique :** Une couleur est suggérée si non fournie
3. **Conversion Hex :** Les noms de couleurs sont convertis en hex
4. **Persistance :** Les couleurs sont sauvegardées correctement
5. **Récupération :** Les couleurs sont retournées dans les réponses

## 🚀 Après les Tests Backend

Une fois ces tests validés, le problème frontend peut être résolu en :

1. **Ajoutant les attributs `id` et `name`** aux champs de couleur
2. **Utilisant les couleurs prédéfinies** dans les sélecteurs
3. **Testant la création/modification** depuis l'interface

Le backend est maintenant prêt à gérer correctement les couleurs des événements personnels ! 🎨
