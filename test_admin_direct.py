#!/usr/bin/env python3
"""
Script de test direct pour les fonctions du tableau de bord admin
"""

import os
import sys
import django
from datetime import datetime, timezone

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from core.mongo_models import User, Team
from core.models.event_model import Event
from core.models.team_task_model import TeamTask
from core.models.bi_model import AdminActivityTracker
from core.views.bi_views import safe_percentage, safe_int, safe_float
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_all_admins_stats():
    """
    Teste les calculs des statistiques pour TOUS les admins
    """
    print("=== Test des calculs pour TOUS les admins ===\n")

    # 1. Récupérer tous les admins
    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé dans la base de données")
        return False

    print(f"✅ {len(admin_users)} admin(s) trouvé(s) dans la base de données\n")

    all_success = True
    admins_with_teams = 0
    admins_with_data = 0
    total_teams_all = 0
    total_events_all = 0
    total_tasks_all = 0

    # 2. Tester chaque admin
    for i, admin in enumerate(admin_users, 1):
        admin_id = str(admin.id)
        print(f"--- Admin {i}/{len(admin_users)}: {admin.name} ({admin.email}) ---")

        try:
            # Vérifier les équipes gérées directement
            admin_teams = Team.objects(responsable=admin_id)
            teams_count = len(admin_teams)

            if teams_count > 0:
                admins_with_teams += 1
                print(f"✅ Gère {teams_count} équipe(s)")

                # Analyser les données de chaque équipe
                team_events_total = 0
                team_tasks_total = 0

                for team in admin_teams:
                    team_id = str(team.id)
                    team_events = Event.objects(team_id=team_id).count()
                    team_tasks = TeamTask.objects(team_id=team_id).count()
                    team_events_total += team_events
                    team_tasks_total += team_tasks

                    print(f"  - Équipe '{team.name}': {team_events} événements, {team_tasks} tâches, {len(team.members)} membres")

                if team_events_total > 0 or team_tasks_total > 0:
                    admins_with_data += 1

                total_teams_all += teams_count
                total_events_all += team_events_total
                total_tasks_all += team_tasks_total

            else:
                print("⚠️  Ne gère aucune équipe")

            # Tester la mise à jour des statistiques
            admin_stats = AdminActivityTracker.update_admin_stats(admin_id, 'today')

            if admin_stats:
                print("✅ Mise à jour des statistiques réussie")

                # Vérifier les valeurs avec les fonctions sécurisées
                total_teams = safe_int(admin_stats.total_teams)
                total_members = safe_int(admin_stats.total_team_members)
                events_total = safe_int(admin_stats.team_events_total)
                events_pending = safe_int(admin_stats.team_events_pending)
                events_completed = safe_int(admin_stats.team_events_completed)
                tasks_total = safe_int(admin_stats.team_tasks_total)
                tasks_pending = safe_int(admin_stats.team_tasks_pending)
                tasks_completed = safe_int(admin_stats.team_tasks_completed)
                progress = safe_float(admin_stats.team_progress_average)

                # Tester les calculs de pourcentage
                events_completion_rate = safe_percentage(events_completed, events_total)
                tasks_completion_rate = safe_percentage(tasks_completed, tasks_total)

                print(f"  Statistiques: {total_teams} équipes, {total_members} membres")
                print(f"  Événements: {events_total} total, {events_pending} en attente, {events_completed} terminés ({events_completion_rate}%)")
                print(f"  Tâches: {tasks_total} total, {tasks_pending} en attente, {tasks_completed} terminées ({tasks_completion_rate}%)")
                print(f"  Progression moyenne: {progress}%")

                # Vérifier qu'aucune valeur n'est NaN
                values_to_check = [
                    total_teams, total_members, events_total, events_pending,
                    events_completed, tasks_total, tasks_pending, tasks_completed,
                    progress, events_completion_rate, tasks_completion_rate
                ]

                nan_found = False
                for j, value in enumerate(values_to_check):
                    if isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf'):
                        print(f"❌ Valeur NaN détectée pour {admin.name} à l'index {j}: {value}")
                        nan_found = True
                        all_success = False

                if not nan_found:
                    print("✅ Aucune valeur NaN détectée")

                # Vérifier la cohérence des nouveaux champs
                if hasattr(admin_stats, 'team_events_archived'):
                    events_archived = safe_int(admin_stats.team_events_archived)
                    print(f"  Événements archivés: {events_archived}")

                if hasattr(admin_stats, 'team_tasks_in_progress'):
                    tasks_in_progress = safe_int(admin_stats.team_tasks_in_progress)
                    tasks_in_revision = safe_int(admin_stats.team_tasks_in_revision) if hasattr(admin_stats, 'team_tasks_in_revision') else 0
                    tasks_archived = safe_int(admin_stats.team_tasks_archived) if hasattr(admin_stats, 'team_tasks_archived') else 0
                    print(f"  Tâches détaillées: {tasks_in_progress} en cours, {tasks_in_revision} en révision, {tasks_archived} archivées")

            else:
                print("❌ Échec de la mise à jour des statistiques")
                all_success = False

        except Exception as e:
            print(f"❌ Erreur pour {admin.name}: {str(e)}")
            all_success = False

        print()  # Ligne vide entre les admins

    # 3. Résumé global
    print("=== RÉSUMÉ GLOBAL ===")
    print(f"Total admins testés: {len(admin_users)}")
    print(f"Admins avec équipes: {admins_with_teams}")
    print(f"Admins avec données: {admins_with_data}")
    print(f"Total équipes gérées: {total_teams_all}")
    print(f"Total événements: {total_events_all}")
    print(f"Total tâches: {total_tasks_all}")

    if all_success:
        print("✅ Tous les tests sont réussis pour tous les admins!")
    else:
        print("❌ Des erreurs ont été détectées pour certains admins")

    return all_success

def test_chart_data_generation():
    """
    Teste la génération des données de graphiques
    """
    print("\n=== Test de génération des données de graphiques ===\n")

    # Simuler des données d'admin stats
    class MockAdminStats:
        def __init__(self):
            self.team_events_total = 10
            self.team_events_pending = 3
            self.team_events_completed = 5
            self.team_events_archived = 2
            self.team_tasks_total = 15
            self.team_tasks_pending = 4
            self.team_tasks_in_progress = 6
            self.team_tasks_in_revision = 2
            self.team_tasks_completed = 2
            self.team_tasks_archived = 1

    mock_stats = MockAdminStats()

    # Test du graphique des événements
    print("--- Test graphique événements ---")

    events_pending = safe_int(mock_stats.team_events_pending)
    events_completed = safe_int(mock_stats.team_events_completed)
    events_archived = safe_int(mock_stats.team_events_archived)
    events_total = safe_int(mock_stats.team_events_total)

    events_chart_data = [
        {
            'name': 'En attente',
            'value': events_pending,
            'percentage': safe_percentage(events_pending, events_total)
        },
        {
            'name': 'Terminés',
            'value': events_completed,
            'percentage': safe_percentage(events_completed, events_total)
        },
        {
            'name': 'Archivés',
            'value': events_archived,
            'percentage': safe_percentage(events_archived, events_total)
        }
    ]

    print(f"Total événements: {events_total}")
    for point in events_chart_data:
        name = point['name']
        value = point['value']
        percentage = point['percentage']
        print(f"  - {name}: {value} ({percentage}%)")

        # Vérifier qu'il n'y a pas de NaN
        if isinstance(percentage, float) and (str(percentage) == 'nan' or str(percentage) == 'inf'):
            print(f"❌ NaN détecté dans {name}")
            return False

    # Test du graphique des tâches
    print("\n--- Test graphique tâches ---")

    tasks_pending = safe_int(mock_stats.team_tasks_pending)
    tasks_in_progress = safe_int(mock_stats.team_tasks_in_progress)
    tasks_in_revision = safe_int(mock_stats.team_tasks_in_revision)
    tasks_completed = safe_int(mock_stats.team_tasks_completed)
    tasks_archived = safe_int(mock_stats.team_tasks_archived)
    tasks_total = safe_int(mock_stats.team_tasks_total)

    tasks_chart_data = [
        {
            'name': 'À faire',
            'value': tasks_pending,
            'percentage': safe_percentage(tasks_pending, tasks_total)
        },
        {
            'name': 'En cours',
            'value': tasks_in_progress,
            'percentage': safe_percentage(tasks_in_progress, tasks_total)
        },
        {
            'name': 'En révision',
            'value': tasks_in_revision,
            'percentage': safe_percentage(tasks_in_revision, tasks_total)
        },
        {
            'name': 'Terminées',
            'value': tasks_completed,
            'percentage': safe_percentage(tasks_completed, tasks_total)
        },
        {
            'name': 'Archivées',
            'value': tasks_archived,
            'percentage': safe_percentage(tasks_archived, tasks_total)
        }
    ]

    print(f"Total tâches: {tasks_total}")
    for point in tasks_chart_data:
        name = point['name']
        value = point['value']
        percentage = point['percentage']
        print(f"  - {name}: {value} ({percentage}%)")

        # Vérifier qu'il n'y a pas de NaN
        if isinstance(percentage, float) and (str(percentage) == 'nan' or str(percentage) == 'inf'):
            print(f"❌ NaN détecté dans {name}")
            return False

    # Vérifier que les totaux correspondent
    events_sum = events_pending + events_completed + events_archived
    tasks_sum = tasks_pending + tasks_in_progress + tasks_in_revision + tasks_completed + tasks_archived

    print(f"\n--- Vérification des totaux ---")
    print(f"Événements - Total: {events_total}, Somme: {events_sum}, Match: {events_total == events_sum}")
    print(f"Tâches - Total: {tasks_total}, Somme: {tasks_sum}, Match: {tasks_total == tasks_sum}")

    print("✅ Génération des données de graphiques réussie")
    return True

if __name__ == "__main__":
    success1 = test_all_admins_stats()
    success2 = test_chart_data_generation()

    if success1 and success2:
        print("\n🎉 Tous les tests directs sont réussis!")
        print("🎉 Les corrections du tableau de bord admin fonctionnent correctement!")
    else:
        print("\n❌ Des problèmes ont été détectés dans les calculs admin.")
        sys.exit(1)
