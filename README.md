# 📊 API Tableau de Bord Super Admin - Documentation Frontend

## 🎯 **Vue d'ensemble**

Cette API fournit des métriques en temps réel pour le tableau de bord super admin, avec des données calculées directement depuis la base MongoDB.

## 🔗 **Endpoint Principal**

### **URL**
```
GET http://localhost:8000/api/bi/super-admin/dashboard/
```

### **Headers Requis**
```javascript
{
  "Authorization": "Bearer {super_admin_token}",
  "Content-Type": "application/json"
}
```

### **Méthode**
```
GET (aucun corps de requête)
```

---

## 🔐 **Authentification**

### **1. Connexion Super Admin**
```javascript
// Endpoint de connexion
POST http://localhost:8000/api/login/

// Corps de la requête
{
  "email": "<EMAIL>",
  "password": "votre_mot_de_passe"
}

// Réponse
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "...",
    "email": "<EMAIL>",
    "role": "super_admin"
  }
}
```

### **2. Utilisation du Token**
```javascript
// Headers pour toutes les requêtes suivantes
const headers = {
  'Authorization': `Bearer ${accessToken}`,
  'Content-Type': 'application/json'
};
```

---

## 📊 **Structure de Réponse**

### **Format JSON Complet**
```javascript
{
  "timestamp": "2025-01-27T16:20:00.000Z",
  "is_realtime": true,
  
  // 🎯 CARTES DE MÉTRIQUES (pour l'affichage en haut)
  "metric_cards": [
    {
      "title": "Nombre total d'utilisateurs",
      "value": 37,                    // Nombre réel depuis la base
      "trend": "+15.2%",              // Tendance calculée
      "trend_period": "ce mois",
      "icon": "users",                // Icône suggérée
      "color": "#3B82F6"             // Couleur bleue
    },
    {
      "title": "Utilisateurs actifs",
      "value": 25,
      "trend": "+8.7%",
      "trend_period": "cette semaine",
      "icon": "user-check",
      "color": "#10B981"             // Couleur verte
    },
    {
      "title": "Utilisateurs inactifs",
      "value": 12,
      "trend": "-5.1%",
      "trend_period": "ce mois",
      "icon": "user-x",
      "color": "#EF4444"             // Couleur rouge
    }
  ],
  
  // 📈 GRAPHIQUES (pour l'affichage en bas)
  "charts": {
    // Graphique de gauche - Anneau
    "active_vs_inactive": {
      "type": "doughnut",
      "title": "Utilisateurs Actifs vs Inactifs",
      "data": [
        {"name": "Actifs", "value": 25, "color": "#10B981"},
        {"name": "Inactifs", "value": 12, "color": "#EF4444"}
      ],
      "legend": [
        {"label": "Actifs", "color": "#10B981"},
        {"label": "Inactifs", "color": "#EF4444"}
      ]
    },
    
    // Graphique de droite - Barres
    "role_distribution": {
      "type": "bar",
      "title": "Distribution des Utilisateurs par Rôle",
      "data": [
        {"name": "Super Admin", "value": 2, "color": "#8B5CF6"},
        {"name": "Admin", "value": 9, "color": "#3B82F6"},
        {"name": "Employés", "value": 14, "color": "#10B981"},
        {"name": "Clients", "value": 12, "color": "#F59E0B"}
      ],
      "max_value": 14                // Pour l'échelle du graphique
    }
  },
  
  // 📋 DONNÉES DÉTAILLÉES (pour référence)
  "detailed_stats": {
    "users_by_role": {
      "super_admin": 2,
      "admin": 9,
      "employee": 14,
      "client": 12
    },
    "activity_stats": {
      "total_users": 37,
      "active_users": {
        "last_24h": 8,
        "last_7_days": 18,
        "last_30_days": 25
      },
      "inactive_users": 12,
      "never_logged_in": 5,
      "activity_rate": {
        "last_24h": 21.62,
        "last_7_days": 48.65,
        "last_30_days": 67.57
      }
    },
    "engagement_metrics": {
      "new_users_7d": 3,
      "new_users_30d": 8,
      "users_logged_today": 8,
      "users_never_logged": 5,
      "retention_rate": 67.57
    },
    "trends": {
      "total_users_trend": "+15.2%",
      "active_users_trend": "+8.7%",
      "inactive_users_trend": "-5.1%"
    }
  },
  
  // ⚙️ MÉTADONNÉES
  "metadata": {
    "last_updated": "2025-01-27T16:20:00.000Z",
    "data_source": "real_time",
    "refresh_interval": 30,
    "dashboard_title": "Tableau de Bord Super Admin",
    "dashboard_subtitle": "Vue d'ensemble des utilisateurs et analyses"
  }
}
```

---

## 💻 **Exemples d'Intégration Frontend**

### **1. Récupération des Données (JavaScript/React)**
```javascript
const fetchDashboardData = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/bi/super-admin/dashboard/', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erreur lors de la récupération des données:', error);
    throw error;
  }
};
```

### **2. Utilisation avec Axios**
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Intercepteur pour ajouter le token automatiquement
api.interceptors.request.use(config => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Récupération des données
const getDashboardData = async () => {
  try {
    const response = await api.get('/bi/super-admin/dashboard/');
    return response.data;
  } catch (error) {
    console.error('Erreur API:', error);
    throw error;
  }
};
```

### **3. Composant React Exemple**
```jsx
import React, { useState, useEffect } from 'react';

const SuperAdminDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadDashboard = async () => {
      try {
        setLoading(true);
        const data = await fetchDashboardData();
        setDashboardData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadDashboard();
    
    // Rafraîchissement automatique toutes les 30 secondes
    const interval = setInterval(loadDashboard, 30000);
    return () => clearInterval(interval);
  }, []);

  if (loading) return <div>Chargement...</div>;
  if (error) return <div>Erreur: {error}</div>;

  return (
    <div className="dashboard">
      <h1>{dashboardData.metadata.dashboard_title}</h1>
      
      {/* Cartes de métriques */}
      <div className="metric-cards">
        {dashboardData.metric_cards.map((card, index) => (
          <div key={index} className="metric-card" style={{borderColor: card.color}}>
            <h3>{card.title}</h3>
            <div className="value">{card.value}</div>
            <div className="trend" style={{color: card.trend.startsWith('+') ? 'green' : 'red'}}>
              {card.trend} {card.trend_period}
            </div>
          </div>
        ))}
      </div>
      
      {/* Graphiques */}
      <div className="charts">
        <div className="chart-left">
          {/* Graphique en anneau pour active_vs_inactive */}
        </div>
        <div className="chart-right">
          {/* Graphique en barres pour role_distribution */}
        </div>
      </div>
    </div>
  );
};
```

---

## 🎨 **Intégration avec Chart.js**

### **1. Installation**
```bash
npm install chart.js react-chartjs-2
```

### **2. Graphique en Anneau (Actifs vs Inactifs)**
```jsx
import { Doughnut } from 'react-chartjs-2';

const ActiveInactiveChart = ({ data }) => {
  const chartData = {
    labels: data.data.map(item => item.name),
    datasets: [{
      data: data.data.map(item => item.value),
      backgroundColor: data.data.map(item => item.color),
      borderWidth: 2,
      borderColor: '#ffffff'
    }]
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: data.title
      },
      legend: {
        position: 'bottom'
      }
    }
  };

  return <Doughnut data={chartData} options={options} />;
};
```

### **3. Graphique en Barres (Distribution par Rôle)**
```jsx
import { Bar } from 'react-chartjs-2';

const RoleDistributionChart = ({ data }) => {
  const chartData = {
    labels: data.data.map(item => item.name),
    datasets: [{
      label: 'Nombre d\'utilisateurs',
      data: data.data.map(item => item.value),
      backgroundColor: data.data.map(item => item.color),
      borderWidth: 1
    }]
  };

  const options = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: data.title
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: data.max_value
      }
    }
  };

  return <Bar data={chartData} options={options} />;
};
```

---

## ⚠️ **Gestion des Erreurs**

### **1. Codes de Statut HTTP**

| Code | Signification | Action Recommandée |
|------|---------------|-------------------|
| 200 | Succès | Traiter les données |
| 401 | Non authentifié | Rediriger vers login |
| 403 | Accès refusé | Vérifier le rôle utilisateur |
| 500 | Erreur serveur | Afficher message d'erreur |

### **2. Réponses d'Erreur**

#### **Erreur 401 - Non Authentifié**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

#### **Erreur 403 - Accès Refusé**
```json
{
  "error": "Unauthorized",
  "message": "Only super admin can access this resource",
  "current_role": "admin",
  "required_role": "super_admin"
}
```

### **3. Gestion Frontend**
```javascript
const handleApiError = (error) => {
  if (error.response) {
    switch (error.response.status) {
      case 401:
        // Token expiré ou invalide
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        break;
      case 403:
        // Accès refusé
        alert('Accès refusé. Vous devez être super admin.');
        break;
      case 500:
        // Erreur serveur
        alert('Erreur serveur. Veuillez réessayer plus tard.');
        break;
      default:
        alert('Une erreur est survenue.');
    }
  } else {
    // Erreur réseau
    alert('Erreur de connexion. Vérifiez votre connexion internet.');
  }
};
```

---

## 🔧 **Tips et Résolution de Problèmes**

### **1. Problèmes CORS**
Si vous rencontrez des erreurs CORS, ajoutez ces headers côté backend ou utilisez un proxy :

```javascript
// Solution proxy (package.json)
"proxy": "http://localhost:8000"

// Ou configuration manuelle
const API_BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8000/api' 
  : '/api';
```

### **2. Gestion du Token**
```javascript
// Vérifier la validité du token
const isTokenValid = (token) => {
  if (!token) return false;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 > Date.now();
  } catch {
    return false;
  }
};

// Rafraîchir le token automatiquement
const refreshToken = async () => {
  const refresh = localStorage.getItem('refresh_token');
  if (!refresh) throw new Error('No refresh token');
  
  const response = await fetch('/api/refresh-token/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ refresh })
  });
  
  const data = await response.json();
  localStorage.setItem('access_token', data.access);
  return data.access;
};
```

### **3. Optimisation des Performances**
```javascript
// Cache des données avec expiration
const CACHE_DURATION = 30000; // 30 secondes
let cachedData = null;
let cacheTimestamp = 0;

const getCachedDashboardData = async () => {
  const now = Date.now();
  
  if (cachedData && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedData;
  }
  
  cachedData = await fetchDashboardData();
  cacheTimestamp = now;
  return cachedData;
};
```

### **4. Rafraîchissement en Temps Réel**
```javascript
// WebSocket ou polling intelligent
const useRealTimeData = () => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        const newData = await fetchDashboardData();
        setData(newData);
      } catch (error) {
        console.error('Erreur de rafraîchissement:', error);
      }
    };
    
    // Chargement initial
    fetchData();
    
    // Rafraîchissement périodique
    const interval = setInterval(fetchData, 30000);
    
    // Rafraîchissement lors du focus
    const handleFocus = () => fetchData();
    window.addEventListener('focus', handleFocus);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);
  
  return data;
};
```

### **5. Validation des Données**
```javascript
// Validation de la structure de réponse
const validateDashboardData = (data) => {
  const required = ['metric_cards', 'charts', 'detailed_stats', 'metadata'];
  
  for (const field of required) {
    if (!data[field]) {
      throw new Error(`Champ manquant: ${field}`);
    }
  }
  
  if (!Array.isArray(data.metric_cards) || data.metric_cards.length !== 3) {
    throw new Error('Format metric_cards invalide');
  }
  
  return true;
};
```

---

## 📱 **Responsive Design**

### **CSS Suggestions**
```css
.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.metric-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

@media (max-width: 768px) {
  .charts {
    grid-template-columns: 1fr;
  }
  
  .metric-cards {
    grid-template-columns: 1fr;
  }
}
```

---

## 🚀 **Déploiement**

### **Variables d'Environnement**
```javascript
// .env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_REFRESH_INTERVAL=30000
REACT_APP_CACHE_DURATION=30000
```

### **Configuration de Production**
```javascript
const config = {
  apiUrl: process.env.REACT_APP_API_URL || '/api',
  refreshInterval: parseInt(process.env.REACT_APP_REFRESH_INTERVAL) || 30000,
  cacheDuration: parseInt(process.env.REACT_APP_CACHE_DURATION) || 30000
};
```

---

## 📞 **Support**

- **Backend** : Toutes les données sont calculées en temps réel depuis MongoDB
- **Sécurité** : Authentification JWT + vérification rôle super admin
- **Performance** : Recommandé de rafraîchir toutes les 30 secondes
- **Compatibilité** : Fonctionne avec React, Vue, Angular, Vanilla JS

**L'API est entièrement fonctionnelle et prête pour l'intégration frontend !**
