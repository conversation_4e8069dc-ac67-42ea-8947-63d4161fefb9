from mongoengine import Document, <PERSON><PERSON><PERSON>, DateT<PERSON><PERSON>ield, <PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timezone
from bson import ObjectId

class PersonalTask(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    description = StringField(required=False)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    status = StringField(default='a_faire', choices=['a_faire', 'en_cours', 'en_revision', 'achevee', 'archived'])
    priority = StringField(default='moyenne', choices=['faible', 'moyenne', 'haute'])  # Priorité de la tâche
    created_by = StringField(required=True)  # ID de l'utilisateur qui a créé la tâche (employé ou client)
    created_by_name = StringField(required=False)  # Nom de l'utilisateur qui a créé la tâche
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))
    display_mode = StringField(default='list', choices=['list', 'card', 'kanban'])  # Mode d'affichage choisi par l'utilisateur

    meta = {
        'collection': 'personal_tasks',
        'indexes': [
            {'fields': ['created_by']},
            {'fields': ['start_date']},
            {'fields': ['status']}
        ]
    }

    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(PersonalTask, self).save(*args, **kwargs)

    def can_manage_task(self, user):
        """Vérifie si un utilisateur peut gérer cette tâche personnelle"""
        # Un utilisateur ne peut gérer que ses propres tâches personnelles
        return str(user.id) == self.created_by

    def can_update_status(self, user):
        """Vérifie si un utilisateur peut mettre à jour le statut de la tâche personnelle"""
        # Un utilisateur ne peut mettre à jour que ses propres tâches personnelles
        return str(user.id) == self.created_by