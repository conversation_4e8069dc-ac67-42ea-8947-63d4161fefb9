// Service pour gérer les événements du calendrier
import axios from 'axios';
import { getAuthToken } from './authService';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Configuration des en-têtes avec le token d'authentification
const getConfig = () => {
  const token = getAuthToken();
  return {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  };
};

/**
 * Récupère tous les événements accessibles par l'utilisateur connecté
 * @returns {Promise<Array>} Liste des événements
 */
export const getEvents = async () => {
  try {
    const response = await axios.get(`${API_URL}/events/`, getConfig());
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des événements:', error);
    throw error;
  }
};

/**
 * Récupère un événement spécifique par son ID
 * @param {string} eventId - ID de l'événement à récupérer
 * @returns {Promise<Object>} Détails de l'événement
 */
export const getEventById = async (eventId) => {
  try {
    const response = await axios.get(`${API_URL}/events/${eventId}/`, getConfig());
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'événement ${eventId}:`, error);
    throw error;
  }
};

/**
 * Crée un nouvel événement (admin uniquement)
 * @param {Object} eventData - Données de l'événement à créer
 * @returns {Promise<Object>} Événement créé
 */
export const createEvent = async (eventData) => {
  try {
    const response = await axios.post(`${API_URL}/events/`, eventData, getConfig());
    return response.data.event;
  } catch (error) {
    console.error('Erreur lors de la création de l'événement:', error);
    throw error;
  }
};

/**
 * Met à jour un événement existant (admin uniquement)
 * @param {string} eventId - ID de l'événement à mettre à jour
 * @param {Object} eventData - Nouvelles données de l'événement
 * @returns {Promise<Object>} Événement mis à jour
 */
export const updateEvent = async (eventId, eventData) => {
  try {
    const response = await axios.put(`${API_URL}/events/${eventId}/`, eventData, getConfig());
    return response.data.event;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour de l'événement ${eventId}:`, error);
    throw error;
  }
};

/**
 * Met à jour le statut d'un événement
 * @param {string} eventId - ID de l'événement
 * @param {string} status - Nouveau statut ('pending', 'completed', 'archived')
 * @returns {Promise<Object>} Résultat de l'opération
 */
export const updateEventStatus = async (eventId, status) => {
  try {
    const response = await axios.put(
      `${API_URL}/events/${eventId}/status/`, 
      { status }, 
      getConfig()
    );
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du statut de l'événement ${eventId}:`, error);
    throw error;
  }
};

/**
 * Archive un événement (admin uniquement)
 * @param {string} eventId - ID de l'événement à archiver
 * @returns {Promise<Object>} Résultat de l'opération
 */
export const archiveEvent = async (eventId) => {
  try {
    const response = await axios.put(
      `${API_URL}/events/${eventId}/archive/`, 
      {}, 
      getConfig()
    );
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de l'archivage de l'événement ${eventId}:`, error);
    throw error;
  }
};

/**
 * Supprime un événement (admin uniquement)
 * @param {string} eventId - ID de l'événement à supprimer
 * @returns {Promise<Object>} Résultat de l'opération
 */
export const deleteEvent = async (eventId) => {
  try {
    const response = await axios.delete(`${API_URL}/events/${eventId}/`, getConfig());
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la suppression de l'événement ${eventId}:`, error);
    throw error;
  }
};

/**
 * Récupère les événements filtrés par équipe
 * @param {string} teamId - ID de l'équipe
 * @returns {Promise<Array>} Liste des événements de l'équipe
 */
export const getEventsByTeam = async (teamId) => {
  try {
    const allEvents = await getEvents();
    return allEvents.filter(event => event.team_id === teamId);
  } catch (error) {
    console.error(`Erreur lors de la récupération des événements de l'équipe ${teamId}:`, error);
    throw error;
  }
};

/**
 * Récupère les événements filtrés par responsable
 * @param {string} adminId - ID de l'administrateur responsable
 * @returns {Promise<Array>} Liste des événements créés par cet admin
 */
export const getEventsByAdmin = async (adminId) => {
  try {
    const allEvents = await getEvents();
    return allEvents.filter(event => event.created_by === adminId);
  } catch (error) {
    console.error(`Erreur lors de la récupération des événements de l'admin ${adminId}:`, error);
    throw error;
  }
};

/**
 * Récupère les événements filtrés par nom d'équipe
 * @param {string} teamName - Nom de l'équipe à rechercher
 * @returns {Promise<Array>} Liste des événements de l'équipe
 */
export const getEventsByTeamName = async (teamName) => {
  try {
    const allEvents = await getEvents();
    return allEvents.filter(event => 
      event.team_name && event.team_name.toLowerCase().includes(teamName.toLowerCase())
    );
  } catch (error) {
    console.error(`Erreur lors de la récupération des événements de l'équipe ${teamName}:`, error);
    throw error;
  }
};

/**
 * Récupère les événements filtrés par nom de responsable
 * @param {string} adminName - Nom de l'administrateur responsable
 * @returns {Promise<Array>} Liste des événements créés par cet admin
 */
export const getEventsByAdminName = async (adminName) => {
  try {
    const allEvents = await getEvents();
    return allEvents.filter(event => 
      event.created_by_name && event.created_by_name.toLowerCase().includes(adminName.toLowerCase())
    );
  } catch (error) {
    console.error(`Erreur lors de la récupération des événements de l'admin ${adminName}:`, error);
    throw error;
  }
};