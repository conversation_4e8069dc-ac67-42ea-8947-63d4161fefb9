from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse

# Vue d'accueil
def home(request):
    return JsonResponse({"message": "Bienvenue sur mon API Django !"})

# Configuration des URLs
urlpatterns = [
    path('admin/', admin.site.urls),  # Interface d'administration Django
    path('api/', include('core.urls')),  # Routes de l'API
    path("", home, name="home"),  # Route d'accueil (placée en dernier)
]