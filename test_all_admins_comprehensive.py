#!/usr/bin/env python3
"""
Test complet de tous les admins avec analyse des statuts
"""

import os
import sys
import django
from datetime import datetime, timezone
from collections import Counter

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from core.mongo_models import User, Team
from core.models.event_model import Event
from core.models.team_task_model import TeamTask
from core.models.bi_model import AdminActivityTracker
from core.views.bi_views import safe_percentage, safe_int, safe_float
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_database_status():
    """
    Analyse les statuts dans la base de données pour vérifier la cohérence
    """
    print("=== ANALYSE DES STATUTS DANS LA BASE DE DONNÉES ===\n")
    
    # 1. Analyser les statuts des événements
    print("--- Statuts des Événements d'Équipe ---")
    all_events = Event.objects()
    event_statuses = Counter([event.status for event in all_events])
    
    print(f"Total événements d'équipe: {len(all_events)}")
    for status, count in event_statuses.items():
        percentage = (count / len(all_events)) * 100 if len(all_events) > 0 else 0
        print(f"  - {status}: {count} ({percentage:.1f}%)")
    
    # Vérifier les statuts valides pour les événements
    valid_event_statuses = ['pending', 'completed', 'archived']
    invalid_event_statuses = [status for status in event_statuses.keys() if status not in valid_event_statuses]
    
    if invalid_event_statuses:
        print(f"⚠️  Statuts invalides détectés pour les événements: {invalid_event_statuses}")
    else:
        print("✅ Tous les statuts d'événements sont valides")
    
    # 2. Analyser les statuts des tâches d'équipe
    print("\n--- Statuts des Tâches d'Équipe ---")
    all_tasks = TeamTask.objects()
    task_statuses = Counter([task.status for task in all_tasks])
    
    print(f"Total tâches d'équipe: {len(all_tasks)}")
    for status, count in task_statuses.items():
        percentage = (count / len(all_tasks)) * 100 if len(all_tasks) > 0 else 0
        print(f"  - {status}: {count} ({percentage:.1f}%)")
    
    # Vérifier les statuts valides pour les tâches
    valid_task_statuses = ['a_faire', 'en_cours', 'en_revision', 'achevee', 'archived']
    invalid_task_statuses = [status for status in task_statuses.keys() if status not in valid_task_statuses]
    
    if invalid_task_statuses:
        print(f"⚠️  Statuts invalides détectés pour les tâches: {invalid_task_statuses}")
    else:
        print("✅ Tous les statuts de tâches sont valides")
    
    # 3. Analyser la répartition par équipe
    print("\n--- Répartition par Équipe ---")
    all_teams = Team.objects()
    print(f"Total équipes: {len(all_teams)}")
    
    teams_with_events = 0
    teams_with_tasks = 0
    
    for team in all_teams:
        team_id = str(team.id)
        team_events = Event.objects(team_id=team_id).count()
        team_tasks = TeamTask.objects(team_id=team_id).count()
        
        if team_events > 0:
            teams_with_events += 1
        if team_tasks > 0:
            teams_with_tasks += 1
        
        if team_events > 0 or team_tasks > 0:
            print(f"  - {team.name}: {team_events} événements, {team_tasks} tâches")
    
    print(f"\nÉquipes avec événements: {teams_with_events}/{len(all_teams)}")
    print(f"Équipes avec tâches: {teams_with_tasks}/{len(all_teams)}")
    
    return {
        'total_events': len(all_events),
        'total_tasks': len(all_tasks),
        'total_teams': len(all_teams),
        'event_statuses': dict(event_statuses),
        'task_statuses': dict(task_statuses),
        'invalid_event_statuses': invalid_event_statuses,
        'invalid_task_statuses': invalid_task_statuses
    }

def test_all_admins_comprehensive():
    """
    Test complet de tous les admins avec analyse détaillée
    """
    print("\n=== TEST COMPLET DE TOUS LES ADMINS ===\n")
    
    # Récupérer tous les admins
    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé dans la base de données")
        return False
    
    print(f"✅ {len(admin_users)} admin(s) trouvé(s)\n")
    
    all_success = True
    detailed_results = []
    
    for i, admin in enumerate(admin_users, 1):
        admin_id = str(admin.id)
        print(f"{'='*60}")
        print(f"ADMIN {i}/{len(admin_users)}: {admin.name}")
        print(f"Email: {admin.email}")
        print(f"ID: {admin_id}")
        print(f"{'='*60}")
        
        admin_result = {
            'name': admin.name,
            'email': admin.email,
            'id': admin_id,
            'teams': [],
            'total_events': 0,
            'total_tasks': 0,
            'success': True,
            'errors': []
        }
        
        try:
            # 1. Analyser les équipes gérées
            admin_teams = Team.objects(responsable=admin_id)
            print(f"\n🏢 ÉQUIPES GÉRÉES: {len(admin_teams)}")
            
            if len(admin_teams) == 0:
                print("⚠️  Cet admin ne gère aucune équipe")
            else:
                for team in admin_teams:
                    team_id = str(team.id)
                    team_events = Event.objects(team_id=team_id)
                    team_tasks = TeamTask.objects(team_id=team_id)
                    
                    # Analyser les statuts des événements de cette équipe
                    events_by_status = {
                        'pending': team_events.filter(status='pending').count(),
                        'completed': team_events.filter(status='completed').count(),
                        'archived': team_events.filter(status='archived').count()
                    }
                    
                    # Analyser les statuts des tâches de cette équipe
                    tasks_by_status = {
                        'a_faire': team_tasks.filter(status='a_faire').count(),
                        'en_cours': team_tasks.filter(status='en_cours').count(),
                        'en_revision': team_tasks.filter(status='en_revision').count(),
                        'achevee': team_tasks.filter(status='achevee').count(),
                        'archived': team_tasks.filter(status='archived').count()
                    }
                    
                    events_total = team_events.count()
                    tasks_total = team_tasks.count()
                    
                    admin_result['total_events'] += events_total
                    admin_result['total_tasks'] += tasks_total
                    
                    team_info = {
                        'name': team.name,
                        'id': team_id,
                        'members': len(team.members),
                        'events_total': events_total,
                        'events_by_status': events_by_status,
                        'tasks_total': tasks_total,
                        'tasks_by_status': tasks_by_status
                    }
                    admin_result['teams'].append(team_info)
                    
                    print(f"\n  📋 Équipe: {team.name}")
                    print(f"     Membres: {len(team.members)}")
                    print(f"     Événements: {events_total} total")
                    for status, count in events_by_status.items():
                        if count > 0:
                            print(f"       - {status}: {count}")
                    print(f"     Tâches: {tasks_total} total")
                    for status, count in tasks_by_status.items():
                        if count > 0:
                            print(f"       - {status}: {count}")
            
            # 2. Tester les statistiques AdminActivityTracker
            print(f"\n📊 TEST DES STATISTIQUES")
            admin_stats = AdminActivityTracker.update_admin_stats(admin_id, 'today')
            
            if admin_stats:
                print("✅ Mise à jour des statistiques réussie")
                
                # Vérifier toutes les valeurs avec les fonctions sécurisées
                stats_values = {
                    'total_teams': safe_int(admin_stats.total_teams),
                    'total_members': safe_int(admin_stats.total_team_members),
                    'events_total': safe_int(admin_stats.team_events_total),
                    'events_pending': safe_int(admin_stats.team_events_pending),
                    'events_completed': safe_int(admin_stats.team_events_completed),
                    'tasks_total': safe_int(admin_stats.team_tasks_total),
                    'tasks_pending': safe_int(admin_stats.team_tasks_pending),
                    'tasks_completed': safe_int(admin_stats.team_tasks_completed),
                    'progress': safe_float(admin_stats.team_progress_average)
                }
                
                # Vérifier les nouveaux champs
                if hasattr(admin_stats, 'team_events_archived'):
                    stats_values['events_archived'] = safe_int(admin_stats.team_events_archived)
                if hasattr(admin_stats, 'team_tasks_in_progress'):
                    stats_values['tasks_in_progress'] = safe_int(admin_stats.team_tasks_in_progress)
                if hasattr(admin_stats, 'team_tasks_in_revision'):
                    stats_values['tasks_in_revision'] = safe_int(admin_stats.team_tasks_in_revision)
                if hasattr(admin_stats, 'team_tasks_archived'):
                    stats_values['tasks_archived'] = safe_int(admin_stats.team_tasks_archived)
                
                # Afficher les statistiques
                for key, value in stats_values.items():
                    print(f"     {key}: {value}")
                
                # Calculer et vérifier les pourcentages
                events_completion_rate = safe_percentage(stats_values['events_completed'], stats_values['events_total'])
                tasks_completion_rate = safe_percentage(stats_values['tasks_completed'], stats_values['tasks_total'])
                
                print(f"     events_completion_rate: {events_completion_rate}%")
                print(f"     tasks_completion_rate: {tasks_completion_rate}%")
                
                # Vérifier qu'aucune valeur n'est NaN
                all_values = list(stats_values.values()) + [events_completion_rate, tasks_completion_rate]
                nan_found = False
                
                for value in all_values:
                    if isinstance(value, float) and (str(value) == 'nan' or str(value) == 'inf'):
                        print(f"❌ Valeur NaN détectée: {value}")
                        admin_result['errors'].append(f"NaN détecté: {value}")
                        nan_found = True
                        all_success = False
                        admin_result['success'] = False
                
                if not nan_found:
                    print("✅ Aucune valeur NaN détectée")
                
            else:
                print("❌ Échec de la mise à jour des statistiques")
                admin_result['errors'].append("Échec mise à jour statistiques")
                admin_result['success'] = False
                all_success = False
        
        except Exception as e:
            error_msg = f"Erreur: {str(e)}"
            print(f"❌ {error_msg}")
            admin_result['errors'].append(error_msg)
            admin_result['success'] = False
            all_success = False
        
        detailed_results.append(admin_result)
        print()  # Ligne vide
    
    # Résumé final
    print("="*80)
    print("RÉSUMÉ FINAL")
    print("="*80)
    
    successful_admins = [r for r in detailed_results if r['success']]
    failed_admins = [r for r in detailed_results if not r['success']]
    admins_with_teams = [r for r in detailed_results if len(r['teams']) > 0]
    admins_with_data = [r for r in detailed_results if r['total_events'] > 0 or r['total_tasks'] > 0]
    
    total_events_all = sum(r['total_events'] for r in detailed_results)
    total_tasks_all = sum(r['total_tasks'] for r in detailed_results)
    total_teams_all = sum(len(r['teams']) for r in detailed_results)
    
    print(f"📊 Statistiques globales:")
    print(f"   - Total admins: {len(admin_users)}")
    print(f"   - Admins avec succès: {len(successful_admins)}")
    print(f"   - Admins avec erreurs: {len(failed_admins)}")
    print(f"   - Admins avec équipes: {len(admins_with_teams)}")
    print(f"   - Admins avec données: {len(admins_with_data)}")
    print(f"   - Total équipes: {total_teams_all}")
    print(f"   - Total événements: {total_events_all}")
    print(f"   - Total tâches: {total_tasks_all}")
    
    if failed_admins:
        print(f"\n❌ Admins avec erreurs:")
        for admin in failed_admins:
            print(f"   - {admin['name']}: {', '.join(admin['errors'])}")
    
    return all_success

if __name__ == "__main__":
    # 1. Analyser la base de données
    db_analysis = analyze_database_status()
    
    # 2. Tester tous les admins
    success = test_all_admins_comprehensive()
    
    if success:
        print("\n🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("🎉 Les corrections du tableau de bord admin fonctionnent parfaitement!")
    else:
        print("\n❌ DES PROBLÈMES ONT ÉTÉ DÉTECTÉS.")
        sys.exit(1)
