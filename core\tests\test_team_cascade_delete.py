from django.test import TestCase
from rest_framework.test import APIClient
from core.mongo_models import User, Team
from core.models.event_model import Event
from core.models.team_task_model import TeamTask
from datetime import datetime, timedelta, timezone
import json

class TeamCascadeDeleteTest(TestCase):
    def setUp(self):
        # Créer un admin avec un email unique
        import uuid
        unique_id = str(uuid.uuid4())[:8]
        self.admin = User(
            name="Admin Test",
            email=f"admin.test.{unique_id}@example.com",
            role="admin"
        )
        self.admin.set_password("password123")
        self.admin.save()

        # Créer une équipe
        self.team = Team(
            name="Équipe Test",
            description="Description de l'équipe test",
            responsable=str(self.admin.id),
            responsable_name=self.admin.name
        )
        self.team.save()

        # Créer un événement associé à l'équipe
        self.event = Event(
            title="Événement Test",
            description="Description de l'événement test",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(hours=2),
            start_time="10:00",
            end_time="12:00",
            status="pending",
            team_id=str(self.team.id),
            team_name=self.team.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.event.save()

        # Créer une tâche associée à l'équipe
        self.task = TeamTask(
            title="Tâche Test",
            description="Description de la tâche test",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=1),
            priority="moyenne",
            status="a_faire",
            team_id=str(self.team.id),
            team_name=self.team.name,
            responsable=str(self.admin.id),
            responsable_name=self.admin.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.task.save()

        # Configurer le client API
        self.client = APIClient()

    def test_team_cascade_delete(self):
        """Tester que la suppression d'une équipe supprime également ses événements et tâches"""
        # Authentifier l'admin
        response = self.client.post('/api/auth/login/', {
            'email': self.admin.email,
            'password': 'password123'
        })
        token = json.loads(response.content)['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Vérifier que l'équipe, l'événement et la tâche existent
        self.assertEqual(Team.objects.count(), 1)
        self.assertEqual(Event.objects.count(), 1)
        self.assertEqual(TeamTask.objects.count(), 1)

        # Supprimer l'équipe
        response = self.client.delete(f'/api/teams/{self.team.id}/')

        # Vérifier que la suppression a réussi
        self.assertEqual(response.status_code, 200)

        # Vérifier que l'équipe, l'événement et la tâche ont été supprimés
        self.assertEqual(Team.objects.count(), 0)
        self.assertEqual(Event.objects.count(), 0)
        self.assertEqual(TeamTask.objects.count(), 0)
