from mongoengine import Document, StringField, DateTimeField, ReferenceField, BooleanField, ObjectIdField
from datetime import datetime, timezone
from bson import ObjectId

class TeamTask(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    description = StringField(required=False)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    status = StringField(default='a_faire', choices=['a_faire', 'en_cours', 'en_revision', 'achevee', 'archived'])
    priority = StringField(default='moyenne', choices=['faible', 'moyenne', 'haute'])  # Priorité de la tâche
    team_id = StringField(required=True)  # ID de l'équipe assignée à la tâche
    team_name = StringField(required=False)  # Nom de l'équipe assignée à la tâche
    member_id = StringField(required=False, default="")  # ID du membre assigné à la tâche (optionnel)
    member_name = StringField(required=False, default="")  # Nom du membre assigné à la tâche (optionnel)
    responsable = StringField(required=True, default="")  # ID de l'admin responsable de la tâche
    responsable_name = StringField(required=False, default="Responsable")  # Nom de l'admin responsable de la tâche
    created_by = StringField(required=True, default="")  # ID de l'admin qui a créé la tâche
    created_by_name = StringField(required=False, default="")  # Nom de l'admin qui a créé la tâche
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))
    display_mode = StringField(default='list', choices=['list', 'card', 'kanban'])  # Mode d'affichage choisi par l'utilisateur

    meta = {
        'collection': 'team_tasks',
        'indexes': [
            {'fields': ['team_id']},
            {'fields': ['member_id']},
            {'fields': ['responsable']},
            {'fields': ['start_date']},
            {'fields': ['status']}
        ]
    }

    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(TeamTask, self).save(*args, **kwargs)

    def can_manage_task(self, user):
        """Vérifie si un utilisateur peut gérer cette tâche"""
        # Seuls les admins responsables de l'équipe associée à la tâche peuvent la gérer
        if user.role == 'admin':
            # Vérifier si l'admin est le responsable de l'équipe
            if self.team_id:
                from ..mongo_models import Team
                try:
                    team = Team.objects.get(id=self.team_id)
                    # Vérifier si l'admin est le responsable de l'équipe
                    return str(user.id) == team.responsable
                except Team.DoesNotExist:
                    # L'équipe n'existe pas
                    return False
                except Exception:
                    # Autre erreur lors de la vérification de l'équipe
                    return False
            return False
        return False

    def can_update_status(self, user):
        """Vérifie si un utilisateur peut mettre à jour le statut de la tâche"""
        # Les admins ne peuvent pas mettre à jour le statut, ils peuvent seulement consulter
        if user.role == 'admin':
            return False
        # Les employés peuvent mettre à jour le statut s'ils sont assignés à la tâche
        if user.role == 'employee':
            # Si la tâche est assignée à un membre spécifique
            if self.member_id and self.member_id == str(user.id):
                return True
            # Si la tâche est assignée à une équipe dont l'utilisateur est membre
            if self.team_id:
                from ..mongo_models import Team
                try:
                    team = Team.objects.get(id=self.team_id)
                    return str(user.id) in team.members
                except Team.DoesNotExist:
                    # L'équipe n'existe pas
                    return False
                except Exception:
                    # Autre erreur lors de la vérification de l'équipe
                    return False
        return False