# 📊 Guide Dashboard Employés - Tests Postman

## 🎯 Vue d'Ensemble

Ce guide présente les nouveaux dashboards en temps réel pour les employés et clients avec des graphiques en secteurs (pie charts) qui importent correctement les données de la base et se mettent à jour automatiquement.

## ✅ **NOUVELLES FONCTIONNALITÉS IMPLÉMENTÉES**

### **1. Dashboard Employés/Clients**
- ✅ **Graphiques en temps réel** : Pie charts avec données dynamiques
- ✅ **Métriques personnelles** : Tâches et événements personnels
- ✅ **Métriques d'équipe** : Pour les employés uniquement
- ✅ **Mise à jour automatique** : Données importées depuis la base
- ✅ **Filtrage par période** : 1h, 24h, 7d, 30d, today

### **2. Graphiques Disponibles**
1. **Distribution des tâches personnelles par statut** (pie chart)
2. **Distribution des événements personnels par statut** (pie chart)
3. **Distribution des tâches d'équipe par statut** (pie chart - employés uniquement)
4. **Distribution des événements d'équipe par statut** (pie chart - employés uniquement)

## 🔗 **ENDPOINT PRINCIPAL**

### **URL**
```
GET http://localhost:8000/api/bi/employee/dashboard/
```

### **Headers Requis**
```javascript
{
  "Authorization": "Bearer {employee_or_client_token}",
  "Content-Type": "application/json"
}
```

### **Paramètres de Requête (Optionnels)**
```
?period=today&manual_refresh=true
```

**Périodes disponibles :**
- `today` (défaut) - Aujourd'hui
- `1h` - Dernière heure
- `24h` - Dernières 24h
- `7d` - Derniers 7 jours
- `30d` - Derniers 30 jours

## 🧪 **TESTS POSTMAN À EFFECTUER**

### **Test 1: Dashboard Employé - Données Complètes**

**Méthode :** `GET`
**URL :** `http://localhost:8000/api/bi/employee/dashboard/`
**Headers :**
```json
{
  "Authorization": "Bearer {employee_token}",
  "Content-Type": "application/json"
}
```

**Réponse Attendue :**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "is_realtime": true,
  "user_info": {
    "user_id": "employee_id",
    "user_name": "Nom Employé",
    "user_role": "employee",
    "teams_count": 2
  },
  "metric_cards": [
    {
      "title": "Tâches personnelles",
      "value": 15,
      "completion_rate": 66.67,
      "icon": "clipboard-list",
      "color": "#3B82F6",
      "subtitle": "10 terminées sur 15"
    },
    {
      "title": "Événements personnels",
      "value": 8,
      "completion_rate": 75.0,
      "icon": "calendar",
      "color": "#10B981",
      "subtitle": "6 terminés sur 8"
    },
    {
      "title": "Tâches d'équipe",
      "value": 12,
      "completion_rate": 58.33,
      "icon": "users",
      "color": "#8B5CF6",
      "subtitle": "7 terminées sur 12"
    },
    {
      "title": "Événements d'équipe",
      "value": 5,
      "completion_rate": 80.0,
      "icon": "calendar-days",
      "color": "#F59E0B",
      "subtitle": "4 terminés sur 5"
    }
  ],
  "charts": {
    "personal_tasks": {
      "type": "pie",
      "title": "Distribution des tâches personnelles par statut",
      "data": [
        {"name": "À faire", "value": 3, "color": "#3B82F6"},
        {"name": "En cours", "value": 2, "color": "#F59E0B"},
        {"name": "Terminées", "value": 10, "color": "#10B981"}
      ],
      "legend": [
        {"label": "À faire", "color": "#3B82F6"},
        {"label": "En cours", "color": "#F59E0B"},
        {"label": "Terminées", "color": "#10B981"}
      ],
      "total": 15,
      "completion_rate": 66.67
    },
    "personal_events": {
      "type": "pie",
      "title": "Distribution des événements personnels par statut",
      "data": [
        {"name": "À faire", "value": 2, "color": "#3B82F6"},
        {"name": "En cours", "value": 0, "color": "#F59E0B"},
        {"name": "Terminés", "value": 6, "color": "#10B981"}
      ],
      "total": 8,
      "completion_rate": 75.0
    },
    "team_tasks": {
      "type": "pie",
      "title": "Distribution des tâches d'équipe par statut",
      "data": [
        {"name": "À faire", "value": 2, "color": "#3B82F6"},
        {"name": "En cours", "value": 3, "color": "#F59E0B"},
        {"name": "Terminées", "value": 7, "color": "#10B981"}
      ],
      "total": 12,
      "completion_rate": 58.33
    },
    "team_events": {
      "type": "pie",
      "title": "Distribution des événements d'équipe par statut",
      "data": [
        {"name": "À faire", "value": 1, "color": "#3B82F6"},
        {"name": "En cours", "value": 0, "color": "#F59E0B"},
        {"name": "Terminés", "value": 4, "color": "#10B981"}
      ],
      "total": 5,
      "completion_rate": 80.0
    }
  },
  "detailed_stats": {
    "personal_tasks": {
      "total": 15,
      "by_status": {
        "a_faire": 3,
        "en_cours": 2,
        "en_revision": 0,
        "achevee": 10,
        "archived": 0
      },
      "completion_rate": 66.67
    },
    "personal_events": {
      "total": 8,
      "by_status": {
        "pending": 2,
        "completed": 6,
        "archived": 0
      },
      "completion_rate": 75.0
    },
    "team_tasks": {
      "total": 12,
      "by_status": {
        "a_faire": 2,
        "en_cours": 3,
        "en_revision": 0,
        "achevee": 7,
        "archived": 0
      },
      "completion_rate": 58.33
    },
    "team_events": {
      "total": 5,
      "by_status": {
        "pending": 1,
        "completed": 4,
        "archived": 0
      },
      "completion_rate": 80.0
    }
  },
  "metadata": {
    "last_updated": "2024-01-15T10:30:00Z",
    "refresh_mode": "manual",
    "refresh_interval": null,
    "dashboard_title": "Tableau de Bord - Nom Employé",
    "dashboard_subtitle": "Suivi personnel (employee)",
    "period": "today",
    "manual_refresh": true
  }
}
```

### **Test 2: Dashboard Client - Données Personnelles Uniquement**

**Méthode :** `GET`
**URL :** `http://localhost:8000/api/bi/employee/dashboard/`
**Headers :**
```json
{
  "Authorization": "Bearer {client_token}",
  "Content-Type": "application/json"
}
```

**Réponse Attendue :**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "is_realtime": true,
  "user_info": {
    "user_id": "client_id",
    "user_name": "Nom Client",
    "user_role": "client",
    "teams_count": 0
  },
  "metric_cards": [
    {
      "title": "Tâches personnelles",
      "value": 8,
      "completion_rate": 62.5,
      "icon": "clipboard-list",
      "color": "#3B82F6",
      "subtitle": "5 terminées sur 8"
    },
    {
      "title": "Événements personnels",
      "value": 4,
      "completion_rate": 50.0,
      "icon": "calendar",
      "color": "#10B981",
      "subtitle": "2 terminés sur 4"
    }
  ],
  "charts": {
    "personal_tasks": {
      "type": "pie",
      "title": "Distribution des tâches personnelles par statut",
      "data": [
        {"name": "À faire", "value": 2, "color": "#3B82F6"},
        {"name": "En cours", "value": 1, "color": "#F59E0B"},
        {"name": "Terminées", "value": 5, "color": "#10B981"}
      ],
      "total": 8,
      "completion_rate": 62.5
    },
    "personal_events": {
      "type": "pie",
      "title": "Distribution des événements personnels par statut",
      "data": [
        {"name": "À faire", "value": 2, "color": "#3B82F6"},
        {"name": "En cours", "value": 0, "color": "#F59E0B"},
        {"name": "Terminés", "value": 2, "color": "#10B981"}
      ],
      "total": 4,
      "completion_rate": 50.0
    }
  }
}
```

### **Test 3: Dashboard avec Filtrage par Période**

**Méthode :** `GET`
**URL :** `http://localhost:8000/api/bi/employee/dashboard/?period=7d&manual_refresh=false`
**Headers :**
```json
{
  "Authorization": "Bearer {employee_token}",
  "Content-Type": "application/json"
}
```

**Vérifications :**
- ✅ `metadata.period` = "7d"
- ✅ `metadata.manual_refresh` = false
- ✅ `metadata.refresh_interval` = 30
- ✅ Données filtrées selon la période

### **Test 4: Accès Non Autorisé (Admin/Super Admin)**

**Méthode :** `GET`
**URL :** `http://localhost:8000/api/bi/employee/dashboard/`
**Headers :**
```json
{
  "Authorization": "Bearer {admin_token}",
  "Content-Type": "application/json"
}
```

**Réponse Attendue :**
```json
{
  "error": "Accès non autorisé"
}
```
**Status Code :** `403 Forbidden`

## 🎨 **COULEURS DES GRAPHIQUES**

### **Palette de Couleurs Standardisée :**
- **Bleu clair** (`#3B82F6`) : À faire / Pending
- **Jaune** (`#F59E0B`) : En cours / In Progress
- **Vert foncé** (`#10B981`) : Terminées / Completed
- **Violet** (`#8B5CF6`) : Tâches d'équipe
- **Orange** (`#F59E0B`) : Événements d'équipe

## 🔄 **MISE À JOUR EN TEMPS RÉEL**

### **Fonctionnalités :**
- ✅ **Données dynamiques** : Importées directement depuis MongoDB
- ✅ **Calculs en temps réel** : Pourcentages et totaux recalculés à chaque requête
- ✅ **Mise à jour automatique** : Toutes les 30 secondes (si manual_refresh=false)
- ✅ **Horodatage** : Timestamp de dernière mise à jour inclus

### **Vérifications à Effectuer :**
1. **Cohérence des données** : Totaux = somme des statuts
2. **Calculs corrects** : Pourcentages de completion
3. **Filtrage par rôle** : Employés voient équipes, clients non
4. **Permissions** : Seuls employés/clients peuvent accéder
5. **Temps réel** : Données mises à jour après modifications

## 🧪 **TESTS SUPPLÉMENTAIRES**

### **Test 5: Vérification des Calculs en Temps Réel**

**Objectif :** Vérifier que les données sont bien importées depuis la base et que les calculs sont corrects.

**Étapes :**
1. Créer quelques tâches personnelles avec différents statuts
2. Appeler l'API dashboard
3. Vérifier que les totaux correspondent
4. Modifier le statut d'une tâche
5. Rappeler l'API et vérifier la mise à jour

**Méthode :** `POST` (créer une tâche d'abord)
**URL :** `http://localhost:8000/api/personal-tasks/`
**Body :**
```json
{
  "title": "Test Task Dashboard",
  "description": "Tâche de test pour dashboard",
  "status": "a_faire",
  "priority": "medium"
}
```

Puis tester le dashboard et vérifier que la nouvelle tâche apparaît dans les métriques.

### **Test 6: Performance et Temps de Réponse**

**Objectif :** Vérifier que l'API répond rapidement même avec beaucoup de données.

**Méthode :** `GET`
**URL :** `http://localhost:8000/api/bi/employee/dashboard/`
**Vérifications :**
- ✅ Temps de réponse < 2 secondes
- ✅ Données cohérentes
- ✅ Pas d'erreurs de calcul

### **Test 7: Test de Charge (Optionnel)**

**Objectif :** Tester avec plusieurs requêtes simultanées.

Utiliser l'outil "Runner" de Postman pour exécuter le test plusieurs fois en parallèle.

## 🔧 **DÉPANNAGE**

### **Erreurs Communes**

**1. Erreur 403 - Accès non autorisé**
```json
{
  "error": "Accès non autorisé"
}
```
**Solution :** Vérifier que le token appartient à un employé ou client.

**2. Erreur 401 - Token invalide**
```json
{
  "detail": "Given token not valid for any token type"
}
```
**Solution :** Renouveler le token d'authentification.

**3. Données vides**
```json
{
  "charts": {
    "personal_tasks": {
      "data": [],
      "total": 0
    }
  }
}
```
**Solution :** Normal si l'utilisateur n'a pas encore de tâches/événements.

### **Vérifications de Cohérence**

**1. Totaux des graphiques :**
```javascript
// Vérifier que la somme des statuts = total
const personalTasksData = response.charts.personal_tasks.data;
const sum = personalTasksData.reduce((acc, item) => acc + item.value, 0);
console.assert(sum === response.charts.personal_tasks.total, "Incohérence dans les totaux");
```

**2. Pourcentages de completion :**
```javascript
// Vérifier le calcul du pourcentage
const completed = personalTasksData.find(item => item.name === "Terminées")?.value || 0;
const total = response.charts.personal_tasks.total;
const expectedRate = total > 0 ? (completed / total) * 100 : 0;
console.assert(
  Math.abs(response.charts.personal_tasks.completion_rate - expectedRate) < 0.01,
  "Erreur de calcul du pourcentage"
);
```

## 📊 **COLLECTION POSTMAN COMPLÈTE**

### **Variables d'Environnement**
```json
{
  "base_url": "http://localhost:8000/api",
  "employee_token": "{{employee_auth_token}}",
  "client_token": "{{client_auth_token}}",
  "admin_token": "{{admin_auth_token}}"
}
```

### **Tests Automatisés dans Postman**
```javascript
// Test automatique à ajouter dans l'onglet "Tests" de Postman

pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has required fields", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('timestamp');
    pm.expect(jsonData).to.have.property('is_realtime');
    pm.expect(jsonData).to.have.property('user_info');
    pm.expect(jsonData).to.have.property('metric_cards');
    pm.expect(jsonData).to.have.property('charts');
});

pm.test("Charts have correct structure", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData.charts).to.have.property('personal_tasks');
    pm.expect(jsonData.charts).to.have.property('personal_events');

    // Vérifier la structure des graphiques
    const personalTasks = jsonData.charts.personal_tasks;
    pm.expect(personalTasks).to.have.property('type', 'pie');
    pm.expect(personalTasks).to.have.property('data');
    pm.expect(personalTasks).to.have.property('total');
    pm.expect(personalTasks).to.have.property('completion_rate');
});

pm.test("Completion rates are valid percentages", function () {
    const jsonData = pm.response.json();

    jsonData.metric_cards.forEach(card => {
        if (card.completion_rate !== undefined) {
            pm.expect(card.completion_rate).to.be.at.least(0);
            pm.expect(card.completion_rate).to.be.at.most(100);
        }
    });
});

pm.test("Colors are valid hex codes", function () {
    const jsonData = pm.response.json();
    const hexColorRegex = /^#[0-9A-F]{6}$/i;

    Object.values(jsonData.charts).forEach(chart => {
        if (chart.data) {
            chart.data.forEach(item => {
                pm.expect(item.color).to.match(hexColorRegex);
            });
        }
    });
});
```

## 📝 **NOTES IMPORTANTES**

- Les **clients** ne voient que leurs données personnelles (pas d'équipes)
- Les **employés** voient leurs données personnelles + équipes
- Les graphiques utilisent des **couleurs cohérentes** selon le statut
- Les **pourcentages** sont calculés en temps réel avec protection anti-NaN
- L'endpoint supporte le **filtrage par période** comme les autres dashboards
- Les **données sont importées en temps réel** depuis MongoDB à chaque requête
- Les **calculs sont sécurisés** contre les divisions par zéro et valeurs NaN
- L'API respecte les **permissions par rôle** (employés/clients uniquement)
