/**
 * Contexte pour la gestion des événements du calendrier
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/contexts/
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext'; // Ajustez le chemin selon votre structure
// Utiliser la syntaxe import ES6 au lieu de require
import { getEvents, updateEventStatus, createEvent, updateEvent, deleteEvent, archiveEvent } from '../services/eventService';

// Création du contexte
const EventContext = createContext();

// Hook personnalisé pour utiliser le contexte
export const useEvent = () => useContext(EventContext);

// Fournisseur du contexte
export const EventProvider = ({ children }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Charger les événements
  const loadEvents = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Utiliser la fonction importée avec ES6
      const eventsData = await getEvents();
      
      // Formater les événements pour le calendrier
      const formattedEvents = eventsData.map(event => ({
        ...event,
        start: new Date(`${event.start_date.split('T')[0]}T${event.start_time}:00`),
        end: new Date(`${event.end_date.split('T')[0]}T${event.end_time}:00`)
      }));
      
      setEvents(formattedEvents);
    } catch (err) {
      setError("Erreur lors du chargement des événements");
      console.error("Erreur lors du chargement des événements:", err);
    } finally {
      setLoading(false);
    }
  };

  // Créer un événement
  const addEvent = async (eventData) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await createEvent(eventData);
      await loadEvents(); // Recharger la liste après création
      return result;
    } catch (err) {
      setError("Erreur lors de la création de l'événement");
      console.error("Erreur lors de la création de l'événement:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour un événement
  const editEvent = async (id, eventData) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await updateEvent(id, eventData);
      await loadEvents(); // Recharger la liste après mise à jour
      return result;
    } catch (err) {
      setError("Erreur lors de la mise à jour de l'événement");
      console.error("Erreur lors de la mise à jour de l'événement:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Supprimer un événement
  const removeEvent = async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await deleteEvent(id);
      await loadEvents(); // Recharger la liste après suppression
      return result;
    } catch (err) {
      setError("Erreur lors de la suppression de l'événement");
      console.error("Erreur lors de la suppression de l'événement:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Archiver un événement
  const archiveEventById = async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await archiveEvent(id);
      await loadEvents(); // Recharger la liste après archivage
      return result;
    } catch (err) {
      setError("Erreur lors de l'archivage de l'événement");
      console.error("Erreur lors de l'archivage de l'événement:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Mettre à jour le statut d'un événement
  const updateStatus = async (id, status) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await updateEventStatus(id, status);
      await loadEvents(); // Recharger la liste après mise à jour du statut
      return result;
    } catch (err) {
      setError("Erreur lors de la mise à jour du statut de l'événement");
      console.error("Erreur lors de la mise à jour du statut de l'événement:", err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Charger les événements au montage du composant
  useEffect(() => {
    loadEvents();
  }, []);

  // Valeur du contexte
  const value = {
    events,
    loading,
    error,
    loadEvents,
    addEvent,
    editEvent,
    removeEvent,
    archiveEvent: archiveEventById,
    updateStatus,
    isAdmin: user?.role === 'admin'
  };

  return (
    <EventContext.Provider value={value}>
      {children}
    </EventContext.Provider>
  );
};