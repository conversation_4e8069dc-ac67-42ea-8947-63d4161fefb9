from django.core.management.base import BaseCommand
from core.tasks.bi_tasks import update_bi_metrics
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Met à jour les métriques BI'
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Mise à jour des métriques BI...'))
        update_bi_metrics()
        self.stdout.write(self.style.SUCCESS('Mise à jour des métriques BI terminée'))
