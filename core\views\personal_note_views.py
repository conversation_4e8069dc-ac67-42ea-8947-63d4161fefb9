from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.personal_note_model import PersonalNote
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PersonalNoteListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une nouvelle note personnelle (employé ou client)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de la note est requis"}, status=400)
            if not data.get('content'):
                return Response({"error": "Le contenu de la note est requis"}, status=400)

            # Vérifier si une note personnelle avec le même titre existe déjà pour cet utilisateur
            if PersonalNote.objects(title=data['title'], created_by=str(user.id)).first():
                return Response({"error": "Vous avez déjà une note personnelle avec ce titre"}, status=400)

            # Créer la note personnelle
            note = PersonalNote(
                title=data['title'],
                content=data['content'],
                created_by=str(user.id),
                created_by_name=user.name
            )
            note.save()

            return Response({
                "message": "Note personnelle créée avec succès",
                "note": {
                    "id": str(note.id),
                    "title": note.title,
                    "content": note.content,
                    "created_by": note.created_by,
                    "created_by_name": note.created_by_name,
                    "created_at": note.created_at,
                    "updated_at": note.updated_at,
                    "is_archived": note.is_archived
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in PersonalNoteListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de la note personnelle"}, status=500)

    def get(self, request):
        """Liste des notes personnelles de l'utilisateur connecté"""
        try:
            user = request.user
            archived = request.query_params.get('archived', 'false').lower() == 'true'

            # Récupérer uniquement les notes personnelles créées par l'utilisateur connecté
            notes = PersonalNote.objects(created_by=str(user.id), is_archived=archived)

            # Formater les notes
            notes_data = []
            for note in notes:
                notes_data.append({
                    'id': str(note.id),
                    'title': note.title,
                    'content': note.content,
                    'created_by': note.created_by,
                    'created_by_name': note.created_by_name,
                    'created_at': note.created_at,
                    'updated_at': note.updated_at,
                    'is_archived': note.is_archived
                })

            return Response(notes_data)

        except Exception as e:
            logger.error(f"Error in PersonalNoteListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalNoteDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, note_id):
        """Récupérer les détails d'une note personnelle"""
        try:
            user = request.user

            try:
                note = PersonalNote.objects.get(id=note_id)
            except PersonalNote.DoesNotExist:
                return Response({"error": "Note personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à voir cette note
            if note.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à voir cette note personnelle"}, status=403)

            # Retourner les détails de la note
            return Response({
                'id': str(note.id),
                'title': note.title,
                'content': note.content,
                'created_by': note.created_by,
                'created_by_name': note.created_by_name,
                'created_at': note.created_at,
                'updated_at': note.updated_at,
                'is_archived': note.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalNoteDetailView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def put(self, request, note_id):
        """Mettre à jour une note personnelle"""
        try:
            user = request.user
            data = request.data

            try:
                note = PersonalNote.objects.get(id=note_id)
            except PersonalNote.DoesNotExist:
                return Response({"error": "Note personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à modifier cette note
            if not note.can_manage_note(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cette note personnelle"}, status=403)

            # Mettre à jour les champs de la note
            if 'title' in data:
                note.title = data['title']
            if 'content' in data:
                note.content = data['content']

            # Sauvegarder les modifications
            note.save()

            return Response({
                "message": "Note personnelle mise à jour avec succès",
                "note": {
                    'id': str(note.id),
                    'title': note.title,
                    'content': note.content,
                    'created_by': note.created_by,
                    'created_by_name': note.created_by_name,
                    'created_at': note.created_at,
                    'updated_at': note.updated_at,
                    'is_archived': note.is_archived
                }
            })

        except Exception as e:
            logger.error(f"Error in PersonalNoteDetailView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def delete(self, request, note_id):
        """Supprimer une note personnelle"""
        try:
            user = request.user

            try:
                note = PersonalNote.objects.get(id=note_id)
            except PersonalNote.DoesNotExist:
                return Response({"error": "Note personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à supprimer cette note
            if not note.can_manage_note(user):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cette note personnelle"}, status=403)

            # Supprimer la note
            note.delete()

            return Response({"message": "Note personnelle supprimée avec succès"}, status=200)

        except Exception as e:
            logger.error(f"Error in PersonalNoteDetailView.delete: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalNoteArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, note_id):
        """Archiver une note personnelle"""
        try:
            user = request.user

            try:
                note = PersonalNote.objects.get(id=note_id)
            except PersonalNote.DoesNotExist:
                return Response({"error": "Note personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à archiver cette note
            if not note.can_manage_note(user):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cette note personnelle"}, status=403)

            # Archiver la note
            note.is_archived = True
            note.save()

            return Response({
                "message": "Note personnelle archivée avec succès",
                "is_archived": note.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalNoteArchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalNoteUnarchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, note_id):
        """Désarchiver une note personnelle"""
        try:
            user = request.user

            try:
                note = PersonalNote.objects.get(id=note_id)
            except PersonalNote.DoesNotExist:
                return Response({"error": "Note personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à désarchiver cette note
            if not note.can_manage_note(user):
                return Response({"error": "Vous n'êtes pas autorisé à désarchiver cette note personnelle"}, status=403)

            # Vérifier que la note est bien archivée
            if not note.is_archived:
                return Response({"error": "Cette note n'est pas archivée et ne peut donc pas être désarchivée"}, status=400)

            # Désarchiver la note
            note.is_archived = False
            note.save()

            return Response({
                "message": "Note personnelle désarchivée avec succès",
                "is_archived": note.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalNoteUnarchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)
