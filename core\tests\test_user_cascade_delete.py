from django.test import TestCase
from rest_framework.test import APIClient
from ..mongo_models import User, Team
from ..models.event_model import Event
from ..models.personal_event_model import PersonalEvent
from ..models.team_task_model import TeamTask
from ..models.personal_task_model import PersonalTask
from ..models.personal_note_model import PersonalNote
from ..models.personal_journal_model import PersonalJournal
from ..models.pomodoro_model import PomodoroSettings
from ..models.bi_model import BiMetric, BiDashboard
from datetime import datetime, timezone, timedelta
import json

class UserCascadeDeleteTest(TestCase):
    def setUp(self):
        # Créer un super admin pour effectuer les suppressions
        self.super_admin = User(
            email="<EMAIL>",
            name="Super Admin",
            role="super_admin"
        )
        self.super_admin.set_password("password123")
        self.super_admin.save()

        # Créer un admin qui sera supprimé
        self.admin = User(
            email="<EMAIL>",
            name="Admin Test",
            role="admin"
        )
        self.admin.set_password("password123")
        self.admin.save()

        # Créer un employé qui sera supprimé
        self.employee = User(
            email="<EMAIL>",
            name="Employee Test",
            role="employee"
        )
        self.employee.set_password("password123")
        self.employee.save()

        # Créer un client qui sera supprimé
        self.client_user = User(
            email="<EMAIL>",
            name="Client Test",
            role="client"
        )
        self.client_user.set_password("password123")
        self.client_user.save()

        # Créer une équipe dont l'admin est responsable
        self.team = Team(
            name="Test Team",
            description="Test Team Description",
            responsable=str(self.admin.id),
            responsable_name=self.admin.name
        )
        self.team.save()

        # Ajouter l'employé à l'équipe
        self.team.add_member(self.employee)

        # Créer un événement d'équipe
        tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
        self.event = Event(
            title="Test Event",
            description="Test Event Description",
            start_date=tomorrow,
            end_date=tomorrow,
            start_time="09:00",
            end_time="10:00",
            team_id=str(self.team.id),
            team_name=self.team.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.event.save()

        # Créer un événement personnel pour l'employé
        self.personal_event = PersonalEvent(
            title="Test Personal Event",
            description="Test Personal Event Description",
            start_date=tomorrow,
            end_date=tomorrow,
            start_time="14:00",
            end_time="15:00",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_event.save()

        # Créer une tâche d'équipe
        self.team_task = TeamTask(
            title="Test Team Task",
            description="Test Team Task Description",
            start_date=tomorrow,
            end_date=tomorrow,
            team_id=str(self.team.id),
            team_name=self.team.name,
            responsable=str(self.admin.id),
            responsable_name=self.admin.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_task.save()

        # Créer une tâche personnelle pour l'employé
        self.personal_task = PersonalTask(
            title="Test Personal Task",
            description="Test Personal Task Description",
            start_date=tomorrow,
            end_date=tomorrow,
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_task.save()

        # Créer une tâche personnelle pour le client
        self.client_task = PersonalTask(
            title="Test Client Task",
            description="Test Client Task Description",
            start_date=tomorrow,
            end_date=tomorrow,
            created_by=str(self.client_user.id),
            created_by_name=self.client_user.name
        )
        self.client_task.save()

        # Créer une note personnelle pour le client
        self.client_note = PersonalNote(
            title="Test Client Note",
            content="Contenu de la note de test du client",
            created_by=str(self.client_user.id),
            created_by_name=self.client_user.name
        )
        self.client_note.save()

        # Créer une entrée de journal pour le client
        self.client_journal = PersonalJournal(
            title="Test Client Journal Entry",
            content="Contenu de l'entrée de journal de test du client",
            entry_date=tomorrow,
            created_by=str(self.client_user.id),
            created_by_name=self.client_user.name
        )
        self.client_journal.save()

        # Créer des paramètres Pomodoro pour l'employé
        self.pomodoro_settings = PomodoroSettings(
            user_id=str(self.employee.id),
            focus_duration=25,
            short_break_duration=5,
            long_break_duration=15,
            sessions_before_long_break=4
        )
        self.pomodoro_settings.save()

        # Créer des métriques BI pour l'employé
        self.bi_metric = BiMetric(
            metric_type='personal_task_status',
            user_id=str(self.employee.id),
            data={
                'total': 1,
                'by_status': {
                    'a_faire': 1,
                    'en_cours': 0,
                    'en_revision': 0,
                    'achevee': 0,
                    'archived': 0
                },
                'completion_rate': 0
            }
        )
        self.bi_metric.save()

        # Créer un tableau de bord BI pour l'employé
        self.bi_dashboard = BiDashboard(
            user_id=str(self.employee.id),
            dashboard_type='employee',
            title="Tableau de bord de test",
            layout={
                'columns': 2,
                'rows': 2
            },
            metrics={
                'personal_tasks': {
                    'position': {'x': 0, 'y': 0, 'w': 1, 'h': 1},
                    'title': 'Tâches personnelles',
                    'type': 'pie_chart'
                }
            }
        )
        self.bi_dashboard.save()

        # Initialiser le client API
        self.api_client = APIClient()

    def get_auth_token(self, email, password):
        """Obtenir un token d'authentification"""
        response = self.api_client.post('/login/', {
            'email': email,
            'password': password
        })
        return response.data.get('access_token')

    def test_delete_admin_cascade(self):
        """Test de suppression en cascade d'un admin"""
        # Authentifier en tant que super admin
        token = self.get_auth_token("<EMAIL>", "password123")
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Vérifier que les données existent avant la suppression
        self.assertEqual(Team.objects.count(), 1)
        self.assertEqual(Event.objects.count(), 1)
        self.assertEqual(TeamTask.objects.count(), 1)

        # Supprimer l'admin
        response = self.api_client.delete(f'/users/{self.admin.id}/')
        self.assertEqual(response.status_code, 200)

        # Vérifier que l'admin a été supprimé
        self.assertEqual(User.objects(id=self.admin.id).count(), 0)

        # Vérifier que les équipes, événements et tâches associés ont été supprimés
        self.assertEqual(Team.objects.count(), 0)
        self.assertEqual(Event.objects.count(), 0)
        self.assertEqual(TeamTask.objects.count(), 0)

    def test_delete_employee_cascade(self):
        """Test de suppression en cascade d'un employé"""
        # Authentifier en tant que super admin
        token = self.get_auth_token("<EMAIL>", "password123")
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Vérifier que les données existent avant la suppression
        self.assertEqual(PersonalEvent.objects(created_by=str(self.employee.id)).count(), 1)
        self.assertEqual(PersonalTask.objects(created_by=str(self.employee.id)).count(), 1)
        self.assertTrue(str(self.employee.id) in self.team.members)
        self.assertEqual(PomodoroSettings.objects(user_id=str(self.employee.id)).count(), 1)
        self.assertEqual(BiMetric.objects(user_id=str(self.employee.id)).count(), 1)
        self.assertEqual(BiDashboard.objects(user_id=str(self.employee.id)).count(), 1)

        # Supprimer l'employé
        response = self.api_client.delete(f'/users/{self.employee.id}/')
        self.assertEqual(response.status_code, 200)

        # Vérifier que l'employé a été supprimé
        self.assertEqual(User.objects(id=self.employee.id).count(), 0)

        # Vérifier que les événements et tâches personnels ont été supprimés
        self.assertEqual(PersonalEvent.objects(created_by=str(self.employee.id)).count(), 0)
        self.assertEqual(PersonalTask.objects(created_by=str(self.employee.id)).count(), 0)

        # Vérifier que les paramètres Pomodoro ont été supprimés
        self.assertEqual(PomodoroSettings.objects(user_id=str(self.employee.id)).count(), 0)

        # Vérifier que les métriques BI ont été supprimées
        self.assertEqual(BiMetric.objects(user_id=str(self.employee.id)).count(), 0)
        self.assertEqual(BiDashboard.objects(user_id=str(self.employee.id)).count(), 0)

        # Vérifier que l'employé a été retiré de l'équipe
        self.team.reload()  # Recharger l'équipe depuis la base de données
        self.assertFalse(str(self.employee.id) in self.team.members)

    def test_delete_client_cascade(self):
        """Test de suppression en cascade d'un client"""
        # Authentifier en tant que super admin
        token = self.get_auth_token("<EMAIL>", "password123")
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Vérifier que les données existent avant la suppression
        self.assertEqual(PersonalTask.objects(created_by=str(self.client_user.id)).count(), 1)
        self.assertEqual(PersonalNote.objects(created_by=str(self.client_user.id)).count(), 1)
        self.assertEqual(PersonalJournal.objects(created_by=str(self.client_user.id)).count(), 1)

        # Supprimer le client
        response = self.api_client.delete(f'/users/{self.client_user.id}/')
        self.assertEqual(response.status_code, 200)

        # Vérifier que le client a été supprimé
        self.assertEqual(User.objects(id=self.client_user.id).count(), 0)

        # Vérifier que toutes les données personnelles ont été supprimées
        self.assertEqual(PersonalTask.objects(created_by=str(self.client_user.id)).count(), 0)
        self.assertEqual(PersonalNote.objects(created_by=str(self.client_user.id)).count(), 0)
        self.assertEqual(PersonalJournal.objects(created_by=str(self.client_user.id)).count(), 0)

    def test_cannot_delete_super_admin(self):
        """Test qu'un super_admin ne peut pas être supprimé"""
        # Créer un autre super_admin pour effectuer le test
        another_super_admin = User(
            email="<EMAIL>",
            name="Another Super Admin",
            role="super_admin"
        )
        another_super_admin.set_password("password123")
        another_super_admin.save()

        # Authentifier en tant que super admin
        token = self.get_auth_token("<EMAIL>", "password123")
        self.api_client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Tenter de supprimer l'autre super_admin
        response = self.api_client.delete(f'/users/{another_super_admin.id}/')

        # Vérifier que la suppression a été refusée
        self.assertEqual(response.status_code, 403)
        self.assertIn('Il est interdit de supprimer un super_admin', response.data.get('error', ''))

        # Vérifier que le super_admin n'a pas été supprimé
        self.assertEqual(User.objects(id=another_super_admin.id).count(), 1)
