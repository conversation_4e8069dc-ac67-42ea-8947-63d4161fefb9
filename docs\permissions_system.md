# Système de Permissions et Implémentation Frontend

## 1. Logique des Permissions dans le Backend

### Structure des Permissions

Le système de permissions est basé sur un dictionnaire stocké dans le modèle `User` avec les clés suivantes :

```python
permissions = DictField(default={
    # Gestion utilisateurs
    'manage_users': False,
    'activate_user_permissions': False,
    
    # Gestion équipes
    'manage_teams': False,
    'manage_team_tasks': False,
    'manage_team_calendars': False,
    
    # Gestion personnelle
    'manage_personal_tasks': True,
    'activate_focus_mode': True,
    'manage_journal_notes': True,
    
    # Dashboards
    'view_main_dashboard': True,
    'view_team_dashboards': False,
    'view_personal_dashboard': True
})
```

### Permissions par Rôle

- **Super Admin** : Accès complet au système, peut gérer tous les utilisateurs et leurs permissions
- **Admin** : Peut gérer les équipes dont il est responsable
- **Employé** : Peut voir les équipes dont il est membre et gérer ses tâches personnelles
- **Client** : Accès limité aux fonctionnalités personnelles

### Vérification des Permissions

Le backend utilise plusieurs mécanismes pour vérifier les permissions :

1. **Décorateurs de rôle** : `@admin_required` et `@super_admin_required`
2. **Décorateur de permission** : `@check_permission('permission_name')`
3. **Méthodes de vérification dans les modèles** : `can_manage_team(user)` et `can_view_team(user)`

#### Gestion des Équipes

La méthode `can_manage_team(user)` dans le modèle `Team` vérifie :
- Si l'utilisateur est un admin
- Si l'admin est le responsable de l'équipe

```python
def can_manage_team(self, user):
    """Vérifie si un utilisateur peut gérer cette équipe"""
    # Vérifier si l'utilisateur est un admin et s'il est le responsable de l'équipe
    if user.role == 'admin':
        # Si l'admin est le responsable de l'équipe, il peut la gérer
        return str(user.id) == self.responsable
    return False
```

La méthode `can_view_team(user)` vérifie :
- Si l'utilisateur est un admin (peut voir toutes les équipes)
- Si l'utilisateur est un employé membre de l'équipe

```python
def can_view_team(self, user):
    """Vérifie si un utilisateur peut voir cette équipe"""
    # Les admins peuvent voir toutes les équipes
    if user.role == 'admin':
        return True
    # Les employés peuvent voir les équipes dont ils sont membres
    if user.role == 'employee':
        return str(user.id) in self.members
    # Les super_admin n'ont pas accès aux équipes
    return False
```

## 2. Implémentation Frontend

### Structure Proposée

Pour implémenter correctement ce système de permissions dans le frontend, nous proposons :

1. **Service de Permissions** : Un service centralisé pour vérifier les permissions
2. **Composants Conditionnels** : Des composants qui s'affichent en fonction des permissions
3. **Gardes de Routes** : Pour protéger les routes en fonction des permissions

### Service de Permissions

Créez un service `permissionService.js` :

```javascript
// src/services/permissionService.js

/**
 * Vérifie si l'utilisateur a une permission spécifique
 * @param {Object} user - L'utilisateur courant
 * @param {String} permission - La permission à vérifier
 * @returns {Boolean} - True si l'utilisateur a la permission
 */
export const hasPermission = (user, permission) => {
  if (!user || !user.permissions) return false;
  return user.permissions[permission] === true;
};

/**
 * Vérifie si l'utilisateur peut gérer une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier
 * @returns {Boolean} - True si l'utilisateur peut gérer l'équipe
 */
export const canManageTeam = (user, team) => {
  if (!user || !team) return false;
  
  // Vérifier si l'utilisateur est un admin et le responsable de l'équipe
  return user.role === 'admin' && user.id === team.responsable?.id;
};

/**
 * Vérifie si l'utilisateur peut voir une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier
 * @returns {Boolean} - True si l'utilisateur peut voir l'équipe
 */
export const canViewTeam = (user, team) => {
  if (!user || !team) return false;
  
  // Les admins peuvent voir toutes les équipes
  if (user.role === 'admin') return true;
  
  // Les employés peuvent voir les équipes dont ils sont membres
  if (user.role === 'employee') {
    return team.members?.some(member => member.id === user.id);
  }
  
  return false;
};

/**
 * Vérifie les permissions complètes pour une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier
 * @returns {Object} - Objet contenant toutes les permissions
 */
export const checkTeamPermissions = (user, team) => {
  const isAdmin = user?.role === 'admin';
  const isResponsable = team && user ? user.id === team.responsable?.id : false;
  const isMember = team && user ? team.members?.some(member => member.id === user.id) : false;
  const hasTeamManagementPermission = hasPermission(user, 'manage_teams');
  
  return {
    canView: isAdmin || isMember,
    canManage: isAdmin && isResponsable,
    canAddMembers: isAdmin && isResponsable && hasTeamManagementPermission,
    canRemoveMembers: isAdmin && isResponsable && hasTeamManagementPermission,
    canCreateTeam: isAdmin && hasTeamManagementPermission,
    isResponsable,
    isMember
  };
};
```

### Composant de Contrôle d'Accès

Créez un composant `PermissionGate` pour contrôler l'affichage en fonction des permissions :

```jsx
// src/components/common/PermissionGate.jsx

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { hasPermission } from '@/services/permissionService';

/**
 * Composant qui affiche son contenu uniquement si l'utilisateur a la permission requise
 * @param {String} permission - La permission requise
 * @param {ReactNode} children - Le contenu à afficher
 * @param {ReactNode} fallback - Contenu alternatif si la permission est refusée
 */
const PermissionGate = ({ permission, children, fallback = null }) => {
  const { user } = useAuth();
  
  if (hasPermission(user, permission)) {
    return <>{children}</>;
  }
  
  return fallback;
};

export default PermissionGate;
```

### Composant de Contrôle d'Accès pour les Équipes

```jsx
// src/components/teams/TeamPermissionGate.jsx

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { checkTeamPermissions } from '@/services/permissionService';

/**
 * Composant qui affiche son contenu uniquement si l'utilisateur a la permission requise pour l'équipe
 * @param {Object} team - L'équipe concernée
 * @param {String} permissionType - Type de permission (canView, canManage, canAddMembers, etc.)
 * @param {ReactNode} children - Le contenu à afficher
 * @param {ReactNode} fallback - Contenu alternatif si la permission est refusée
 */
const TeamPermissionGate = ({ team, permissionType, children, fallback = null }) => {
  const { user } = useAuth();
  const permissions = checkTeamPermissions(user, team);
  
  if (permissions[permissionType]) {
    return <>{children}</>;
  }
  
  return fallback;
};

export default TeamPermissionGate;
```

### Utilisation dans les Composants

Exemple d'utilisation dans un composant d'équipe :

```jsx
// src/components/teams/TeamCard.jsx

import React from 'react';
import { TeamPermissionGate } from '@/components/teams/TeamPermissionGate';

const TeamCard = ({ team }) => {
  return (
    <div className="team-card">
      <h3>{team.name}</h3>
      <p>{team.description}</p>
      
      <TeamPermissionGate team={team} permissionType="canManage">
        <div className="team-actions">
          <button className="edit-btn">Modifier</button>
          <button className="delete-btn">Supprimer</button>
        </div>
      </TeamPermissionGate>
      
      <TeamPermissionGate team={team} permissionType="canAddMembers">
        <button className="add-member-btn">Ajouter un membre</button>
      </TeamPermissionGate>
    </div>
  );
};

export default TeamCard;
```

### Garde de Routes

Créez un composant pour protéger les routes en fonction des permissions :

```jsx
// src/components/routing/ProtectedRoute.jsx

import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { hasPermission } from '@/services/permissionService';

const ProtectedRoute = ({ permission, children }) => {
  const { user, isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (permission && !hasPermission(user, permission)) {
    return <Navigate to="/unauthorized" replace />;
  }
  
  return children;
};

export default ProtectedRoute;
```

## 3. Recommandations pour l'Implémentation

1. **Centraliser la Logique** : Toute la logique de vérification des permissions doit être centralisée dans le service de permissions.

2. **Cohérence Backend/Frontend** : Assurez-vous que les vérifications de permissions dans le frontend correspondent exactement à celles du backend.

3. **Messages d'Erreur Clairs** : Fournissez des messages d'erreur clairs lorsqu'une action est refusée en raison de permissions insuffisantes.

4. **Mise en Cache des Permissions** : Envisagez de mettre en cache les permissions de l'utilisateur pour éviter des appels API répétés.

5. **Tests Unitaires** : Écrivez des tests unitaires pour vérifier que les permissions fonctionnent correctement.

## 4. Exemple d'Implémentation dans une Page d'Équipe

```jsx
// src/pages/TeamDetail.jsx

import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { checkTeamPermissions } from '@/services/permissionService';
import { getTeamById, updateTeam, deleteTeam } from '@/services/teamService';
import TeamPermissionGate from '@/components/teams/TeamPermissionGate';
import MemberList from '@/components/teams/MemberList';
import AddMemberForm from '@/components/teams/AddMemberForm';
import ErrorMessage from '@/components/common/ErrorMessage';

const TeamDetail = () => {
  const { teamId } = useParams();
  const { user } = useAuth();
  const [team, setTeam] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({ name: '', description: '' });
  
  useEffect(() => {
    const fetchTeam = async () => {
      try {
        setLoading(true);
        const data = await getTeamById(teamId);
        setTeam(data);
        setFormData({ name: data.name, description: data.description });
      } catch (err) {
        setError(err.message || 'Erreur lors du chargement de l\'équipe');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeam();
  }, [teamId]);
  
  const handleUpdate = async () => {
    try {
      await updateTeam(teamId, formData);
      setTeam({ ...team, ...formData });
      setEditMode(false);
    } catch (err) {
      setError(err.message || 'Erreur lors de la mise à jour de l\'équipe');
    }
  };
  
  const handleDelete = async () => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette équipe ?')) {
      try {
        await deleteTeam(teamId);
        // Rediriger vers la liste des équipes
        window.location.href = '/teams';
      } catch (err) {
        setError(err.message || 'Erreur lors de la suppression de l\'équipe');
      }
    }
  };
  
  if (loading) return <div>Chargement...</div>;
  if (error) return <ErrorMessage message={error} />;
  if (!team) return <div>Équipe non trouvée</div>;
  
  const permissions = checkTeamPermissions(user, team);
  
  return (
    <div className="team-detail-page">
      {!permissions.canView ? (
        <ErrorMessage message="Vous n'avez pas les permissions nécessaires pour voir cette équipe" />
      ) : (
        <>
          <div className="team-header">
            {editMode ? (
              <div className="edit-form">
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                />
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
                <div className="form-actions">
                  <button onClick={handleUpdate}>Enregistrer</button>
                  <button onClick={() => setEditMode(false)}>Annuler</button>
                </div>
              </div>
            ) : (
              <>
                <h1>{team.name}</h1>
                <p>{team.description}</p>
                <p>Responsable: {team.responsable.name}</p>
                
                <TeamPermissionGate team={team} permissionType="canManage">
                  <div className="team-actions">
                    <button onClick={() => setEditMode(true)}>Modifier</button>
                    <button onClick={handleDelete}>Supprimer</button>
                  </div>
                </TeamPermissionGate>
              </>
            )}
          </div>
          
          <div className="team-members">
            <h2>Membres de l'équipe</h2>
            <MemberList team={team} />
            
            <TeamPermissionGate team={team} permissionType="canAddMembers">
              <AddMemberForm teamId={teamId} onMemberAdded={(newMember) => {
                setTeam({
                  ...team,
                  members: [...team.members, newMember]
                });
              }} />
            </TeamPermissionGate>
          </div>
        </>
      )}
    </div>
  );
};

export default TeamDetail;
```

## Conclusion

Le système de permissions est un élément crucial de l'application qui permet de contrôler l'accès aux fonctionnalités en fonction du rôle de l'utilisateur et de ses permissions spécifiques. L'implémentation frontend proposée permet de refléter fidèlement la logique de permissions du backend tout en offrant une expérience utilisateur fluide et sécurisée.

En suivant ces recommandations, vous pourrez mettre en place un système de permissions robuste qui protège les ressources sensibles tout en permettant aux utilisateurs d'accéder aux fonctionnalités auxquelles ils ont droit.