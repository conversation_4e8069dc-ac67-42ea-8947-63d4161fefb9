#!/usr/bin/env python
"""
Script de test pour vérifier que les corrections du mode Pomodoro fonctionnent
"""

import requests
import time

def test_pomodoro_functionality():
    """Test complet de la fonctionnalité Pomodoro"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion (utilisez un utilisateur existant)
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test de la fonctionnalité Pomodoro ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    print("✅ Connexion réussie")
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n" + "-"*50 + "\n")
    
    # 2. Récupération des paramètres Pomodoro
    print("2. Récupération des paramètres Pomodoro...")
    settings_response = requests.get(
        f"{base_url}/pomodoro/settings/",
        headers=headers
    )
    
    print(f"Statut: {settings_response.status_code}")
    if settings_response.status_code == 200:
        settings_data = settings_response.json()
        print("✅ Paramètres récupérés avec succès")
        print(f"Durée de focus: {settings_data['focus_duration']} minutes")
        print(f"Session active: {settings_data['is_active']}")
        print(f"Statut session: {settings_data['current_session']['status']}")
    else:
        print(f"❌ Échec de récupération des paramètres: {settings_response.json()}")
        return
    
    print("\n" + "-"*50 + "\n")
    
    # 3. Test de démarrage de session
    print("3. Test de démarrage de session...")
    start_response = requests.post(
        f"{base_url}/pomodoro/control/start/",
        headers=headers
    )
    
    print(f"Statut: {start_response.status_code}")
    if start_response.status_code == 200:
        start_data = start_response.json()
        print("✅ Session démarrée avec succès")
        print(f"Message: {start_data['message']}")
        print(f"Session active: {start_data['is_active']}")
        print(f"Temps restant: {start_data['current_session']['remaining_time']} secondes")
    else:
        print(f"❌ Échec du démarrage: {start_response.json()}")
        return
    
    print("\n" + "-"*50 + "\n")
    
    # 4. Test de pause
    print("4. Test de mise en pause...")
    time.sleep(2)  # Attendre 2 secondes
    
    pause_response = requests.post(
        f"{base_url}/pomodoro/control/pause/",
        headers=headers
    )
    
    print(f"Statut: {pause_response.status_code}")
    if pause_response.status_code == 200:
        pause_data = pause_response.json()
        print("✅ Session mise en pause avec succès")
        print(f"Message: {pause_data['message']}")
        print(f"Statut: {pause_data['current_session']['status']}")
    else:
        print(f"❌ Échec de la pause: {pause_response.json()}")
    
    print("\n" + "-"*50 + "\n")
    
    # 5. Test de reprise
    print("5. Test de reprise...")
    resume_response = requests.post(
        f"{base_url}/pomodoro/control/resume/",
        headers=headers
    )
    
    print(f"Statut: {resume_response.status_code}")
    if resume_response.status_code == 200:
        resume_data = resume_response.json()
        print("✅ Session reprise avec succès")
        print(f"Message: {resume_data['message']}")
        print(f"Statut: {resume_data['current_session']['status']}")
    else:
        print(f"❌ Échec de la reprise: {resume_response.json()}")
    
    print("\n" + "-"*50 + "\n")
    
    # 6. Test de réinitialisation
    print("6. Test de réinitialisation...")
    reset_response = requests.post(
        f"{base_url}/pomodoro/control/reset/",
        headers=headers
    )
    
    print(f"Statut: {reset_response.status_code}")
    if reset_response.status_code == 200:
        reset_data = reset_response.json()
        print("✅ Session réinitialisée avec succès")
        print(f"Message: {reset_data['message']}")
        print(f"Session active: {reset_data['is_active']}")
        print(f"Statut: {reset_data['current_session']['status']}")
    else:
        print(f"❌ Échec de la réinitialisation: {reset_response.json()}")
    
    print("\n" + "-"*50 + "\n")
    
    # 7. Test d'erreur - essayer de terminer une session inexistante
    print("7. Test d'erreur - terminer une session inexistante...")
    complete_response = requests.post(
        f"{base_url}/pomodoro/control/complete/",
        headers=headers
    )
    
    print(f"Statut: {complete_response.status_code}")
    if complete_response.status_code == 400:
        complete_data = complete_response.json()
        print("✅ Gestion d'erreur correcte")
        print(f"Message d'erreur: {complete_data['error']}")
    else:
        print(f"❌ Gestion d'erreur incorrecte: {complete_response.json()}")
    
    print("\n" + "-"*50 + "\n")
    
    # 8. Test de mise à jour des paramètres
    print("8. Test de mise à jour des paramètres...")
    new_settings = {
        "focus_duration": 30,
        "short_break_duration": 10,
        "long_break_duration": 20,
        "sessions_before_long_break": 3
    }
    
    update_response = requests.put(
        f"{base_url}/pomodoro/settings/",
        json=new_settings,
        headers=headers
    )
    
    print(f"Statut: {update_response.status_code}")
    if update_response.status_code == 200:
        update_data = update_response.json()
        print("✅ Paramètres mis à jour avec succès")
        print(f"Message: {update_data['message']}")
        print(f"Nouvelle durée de focus: {update_data['settings']['focus_duration']} minutes")
    else:
        print(f"❌ Échec de la mise à jour: {update_response.json()}")
    
    print("\n" + "="*50 + "\n")
    print("Tests terminés !")

if __name__ == "__main__":
    test_pomodoro_functionality()
