# 🧪 Tests de Création et Modification d'Événements

## 🎯 Objectif
Tester la création et modification d'événements (personnels et d'équipe) pour vérifier si le champ couleur fonctionne correctement côté backend.

## 🔐 Prérequis
1. Backend démarré sur `http://localhost:8000`
2. Tokens d'authentification pour différents rôles
3. <PERSON><PERSON> ou outil similaire

---

## 📋 **PARTIE 1 : AUTHENTIFICATION**

### 1.1 Connexion Super Admin
```
POST http://localhost:8000/api/auth/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "[MOT_DE_PASSE_SUPER_ADMIN]"
}
```
**Stocker le token dans :** `super_admin_token`

### 1.2 Connexion Admin
```
POST http://localhost:8000/api/auth/login/
Content-Type: application/json

{
    "email": "[EMAIL_ADMIN]",
    "password": "[MOT_DE_PASSE_ADMIN]"
}
```
**Stocker le token dans :** `admin_token`

### 1.3 Connexion Employé
```
POST http://localhost:8000/api/auth/login/
Content-Type: application/json

{
    "email": "[EMAIL_EMPLOYE]",
    "password": "[MOT_DE_PASSE_EMPLOYE]"
}
```
**Stocker le token dans :** `employee_token`

### 1.4 Connexion Client
```
POST http://localhost:8000/api/auth/login/
Content-Type: application/json

{
    "email": "[EMAIL_CLIENT]",
    "password": "[MOT_DE_PASSE_CLIENT]"
}
```
**Stocker le token dans :** `client_token`

---

## 📋 **PARTIE 2 : ÉVÉNEMENTS D'ÉQUIPE (ADMIN)**

### 2.1 Création d'Événement d'Équipe AVEC Couleur
```
POST http://localhost:8000/api/events/
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
    "title": "Réunion Sprint Planning",
    "description": "Planification du prochain sprint",
    "start_date": "2025-01-25T09:00:00Z",
    "end_date": "2025-01-25T10:30:00Z",
    "start_time": "09:00",
    "end_time": "10:30",
    "note": "Préparer les user stories",
    "color": "bleu_equipe",
    "team_id": "[TEAM_ID]"
}
```

**Vérifications :**
- ✅ Statut 201 (Created)
- ✅ Réponse contient `"color": "#1e40af"`
- ✅ Événement créé avec la couleur correcte

### 2.2 Création d'Événement d'Équipe SANS Couleur
```
POST http://localhost:8000/api/events/
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
    "title": "Formation DevOps",
    "description": "Session de formation",
    "start_date": "2025-01-26T14:00:00Z",
    "end_date": "2025-01-26T17:00:00Z",
    "start_time": "14:00",
    "end_time": "17:00",
    "team_id": "[TEAM_ID]"
}
```

**Vérifications :**
- ✅ Statut 201 (Created)
- ✅ Couleur suggérée automatiquement
- ✅ Réponse contient un champ `color` avec une valeur hex

### 2.3 Modification de Couleur d'Événement d'Équipe
```
PUT http://localhost:8000/api/events/[EVENT_ID]/
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
    "color": "vert_equipe"
}
```

**Vérifications :**
- ✅ Statut 200 (OK)
- ✅ Couleur mise à jour vers `#059669`

---

## 📋 **PARTIE 3 : ÉVÉNEMENTS PERSONNELS (EMPLOYÉ)**

### 3.1 Création d'Événement Personnel AVEC Couleur
```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer {{employee_token}}
Content-Type: application/json

{
    "title": "Rendez-vous médical",
    "description": "Consultation chez le médecin",
    "start_date": "2025-01-27T10:00:00Z",
    "end_date": "2025-01-27T11:00:00Z",
    "start_time": "10:00",
    "end_time": "11:00",
    "note": "Apporter la carte vitale",
    "color": "vert_personnel"
}
```

**Vérifications :**
- ✅ Statut 201 (Created)
- ✅ Réponse contient `"color": "#10b981"`
- ✅ Événement personnel créé avec la couleur correcte

### 3.2 Création d'Événement Personnel SANS Couleur
```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer {{employee_token}}
Content-Type: application/json

{
    "title": "Pause déjeuner importante",
    "description": "Déjeuner d'affaires",
    "start_date": "2025-01-28T12:00:00Z",
    "end_date": "2025-01-28T13:30:00Z",
    "start_time": "12:00",
    "end_time": "13:30"
}
```

**Vérifications :**
- ✅ Statut 201 (Created)
- ✅ Couleur suggérée automatiquement
- ✅ Réponse contient un champ `color` avec une valeur hex

### 3.3 Modification de Couleur d'Événement Personnel
```
PUT http://localhost:8000/api/personal-events/[EVENT_ID]/
Authorization: Bearer {{employee_token}}
Content-Type: application/json

{
    "color": "rose_personnel"
}
```

**Vérifications :**
- ✅ Statut 200 (OK)
- ✅ Couleur mise à jour vers `#ec4899`

---

## 📋 **PARTIE 4 : ÉVÉNEMENTS PERSONNELS (CLIENT)**

### 4.1 Création d'Événement Personnel Client AVEC Couleur
```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer {{client_token}}
Content-Type: application/json

{
    "title": "Séance de sport",
    "description": "Cours de fitness",
    "start_date": "2025-01-29T18:00:00Z",
    "end_date": "2025-01-29T19:30:00Z",
    "start_time": "18:00",
    "end_time": "19:30",
    "color": "orange_personnel"
}
```

**Vérifications :**
- ✅ Statut 201 (Created)
- ✅ Réponse contient `"color": "#f97316"`

### 4.2 Modification d'Événement Personnel Client
```
PUT http://localhost:8000/api/personal-events/[EVENT_ID]/
Authorization: Bearer {{client_token}}
Content-Type: application/json

{
    "title": "Séance de sport (modifié)",
    "color": "violet_personnel"
}
```

**Vérifications :**
- ✅ Statut 200 (OK)
- ✅ Couleur mise à jour vers `#8b5cf6`

---

## 📋 **PARTIE 5 : TESTS DE VALIDATION**

### 5.1 Test Couleur Invalide (Événement d'Équipe)
```
POST http://localhost:8000/api/events/
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
    "title": "Test couleur invalide",
    "start_date": "2025-01-30T10:00:00Z",
    "end_date": "2025-01-30T11:00:00Z",
    "start_time": "10:00",
    "end_time": "11:00",
    "color": "couleur_inexistante",
    "team_id": "[TEAM_ID]"
}
```

**Vérifications :**
- ❌ Statut 400 (Bad Request)
- ❌ Message d'erreur de validation

### 5.2 Test Couleur Invalide (Événement Personnel)
```
POST http://localhost:8000/api/personal-events/
Authorization: Bearer {{employee_token}}
Content-Type: application/json

{
    "title": "Test couleur invalide",
    "start_date": "2025-01-30T14:00:00Z",
    "end_date": "2025-01-30T15:00:00Z",
    "start_time": "14:00",
    "end_time": "15:00",
    "color": "mauvaise_couleur"
}
```

**Vérifications :**
- ❌ Statut 400 (Bad Request)
- ❌ Message d'erreur de validation

---

## 📋 **PARTIE 6 : VÉRIFICATION DES LISTES**

### 6.1 Liste des Événements d'Équipe
```
GET http://localhost:8000/api/events/
Authorization: Bearer {{admin_token}}
```

**Vérifications :**
- ✅ Tous les événements affichent leur couleur
- ✅ Les couleurs sont au format hexadécimal

### 6.2 Liste des Événements Personnels
```
GET http://localhost:8000/api/personal-events/
Authorization: Bearer {{employee_token}}
```

**Vérifications :**
- ✅ Tous les événements personnels affichent leur couleur
- ✅ Les couleurs sont au format hexadécimal

---

## 🎯 **RÉSULTATS ATTENDUS**

| Test | Type | Statut | Couleur Finale | Notes |
|------|------|--------|----------------|-------|
| 2.1 | Équipe avec couleur | ✅ 201 | `#1e40af` | Couleur prédéfinie |
| 2.2 | Équipe sans couleur | ✅ 201 | Suggérée | Auto-suggestion |
| 2.3 | Modification équipe | ✅ 200 | `#059669` | Mise à jour OK |
| 3.1 | Personnel avec couleur | ✅ 201 | `#10b981` | Couleur prédéfinie |
| 3.2 | Personnel sans couleur | ✅ 201 | Suggérée | Auto-suggestion |
| 3.3 | Modification personnel | ✅ 200 | `#ec4899` | Mise à jour OK |
| 4.1 | Client avec couleur | ✅ 201 | `#f97316` | Couleur prédéfinie |
| 4.2 | Modification client | ✅ 200 | `#8b5cf6` | Mise à jour OK |
| 5.1 | Couleur invalide équipe | ❌ 400 | - | Validation échoue |
| 5.2 | Couleur invalide personnel | ❌ 400 | - | Validation échoue |

## 🔍 **DIAGNOSTIC**

Si tous ces tests passent ✅, alors le problème est **uniquement côté frontend**.
Si certains tests échouent ❌, alors il y a un problème côté backend à corriger.

**Prochaine étape :** Exécuter ces tests pour identifier précisément où se situe le problème !
