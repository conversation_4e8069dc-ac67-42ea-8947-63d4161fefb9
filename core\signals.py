"""
Signaux pour mettre à jour automatiquement les métriques BI
"""
import logging
from mongoengine.signals import post_save
from .mongo_models import User
from .tasks.bi_tasks import update_user_activity_metrics

logger = logging.getLogger(__name__)

# Connecter le signal post_save pour User
def update_bi_metrics_on_user_change(sender, document, **kwargs):
    """
    Met à jour les métriques BI lorsqu'un utilisateur est créé ou modifié
    """
    logger.info(f"Signal post_save déclenché pour l'utilisateur: {document.email}")
    try:
        # Mettre à jour uniquement les métriques d'activité des utilisateurs
        update_user_activity_metrics()
        logger.info(f"Métriques BI mises à jour après modification de l'utilisateur: {document.email}")
    except Exception as e:
        logger.error(f"Erreur lors de la mise à jour des métriques BI: {str(e)}")

# Connecter le signal
post_save.connect(update_bi_metrics_on_user_change, sender=User)
