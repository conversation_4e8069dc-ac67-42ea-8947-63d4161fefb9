#!/usr/bin/env python3
"""
Script de test pour vérifier les corrections du tableau de bord admin
"""

import os
import sys
import django
from datetime import datetime, timezone

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from core.mongo_models import User, Team
from core.models.event_model import Event
from core.models.team_task_model import TeamTask
from core.models.bi_model import AdminActivityTracker
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_admin_dashboard_data():
    """
    Teste les données du tableau de bord admin pour vérifier les corrections
    """
    print("=== Test des corrections du tableau de bord admin ===\n")
    
    # 1. Trouver un admin avec des équipes
    admin_users = User.objects(role='admin')
    if not admin_users:
        print("❌ Aucun admin trouvé dans la base de données")
        return False
    
    admin = admin_users.first()
    admin_id = str(admin.id)
    print(f"✅ Admin trouvé: {admin.name} ({admin.email})")
    
    # 2. Vérifier les équipes gérées par cet admin
    admin_teams = Team.objects(responsable=admin_id)
    print(f"✅ Équipes gérées: {len(admin_teams)}")
    
    if not admin_teams:
        print("⚠️  Cet admin ne gère aucune équipe")
        return True
    
    # 3. Analyser les données pour chaque équipe
    total_events = 0
    total_tasks = 0
    
    for team in admin_teams:
        team_id = str(team.id)
        print(f"\n--- Équipe: {team.name} ---")
        
        # Événements d'équipe avec statuts corrects
        events = Event.objects(team_id=team_id)
        events_pending = events.filter(status='pending').count()
        events_completed = events.filter(status='completed').count()
        events_archived = events.filter(status='archived').count()
        events_total = events.count()
        
        print(f"Événements - Total: {events_total}")
        print(f"  - En attente (pending): {events_pending}")
        print(f"  - Terminés (completed): {events_completed}")
        print(f"  - Archivés (archived): {events_archived}")
        
        # Tâches d'équipe avec statuts corrects
        tasks = TeamTask.objects(team_id=team_id)
        tasks_a_faire = tasks.filter(status='a_faire').count()
        tasks_en_cours = tasks.filter(status='en_cours').count()
        tasks_en_revision = tasks.filter(status='en_revision').count()
        tasks_achevee = tasks.filter(status='achevee').count()
        tasks_archived = tasks.filter(status='archived').count()
        tasks_total = tasks.count()
        
        print(f"Tâches - Total: {tasks_total}")
        print(f"  - À faire (a_faire): {tasks_a_faire}")
        print(f"  - En cours (en_cours): {tasks_en_cours}")
        print(f"  - En révision (en_revision): {tasks_en_revision}")
        print(f"  - Terminées (achevee): {tasks_achevee}")
        print(f"  - Archivées (archived): {tasks_archived}")
        
        total_events += events_total
        total_tasks += tasks_total
    
    print(f"\n=== Résumé global ===")
    print(f"Total événements: {total_events}")
    print(f"Total tâches: {total_tasks}")
    
    # 4. Tester la mise à jour des statistiques admin
    print(f"\n=== Test de mise à jour des statistiques ===")
    
    try:
        # Mettre à jour les statistiques pour aujourd'hui
        admin_stats = AdminActivityTracker.update_admin_stats(admin_id, 'today')
        
        if admin_stats:
            print("✅ Mise à jour des statistiques réussie")
            print(f"Équipes gérées: {admin_stats.total_teams}")
            print(f"Membres total: {admin_stats.total_team_members}")
            print(f"Événements total: {admin_stats.team_events_total}")
            print(f"Événements en attente: {admin_stats.team_events_pending}")
            print(f"Événements terminés: {admin_stats.team_events_completed}")
            print(f"Tâches total: {admin_stats.team_tasks_total}")
            print(f"Tâches en attente: {admin_stats.team_tasks_pending}")
            print(f"Tâches terminées: {admin_stats.team_tasks_completed}")
            print(f"Progression moyenne: {admin_stats.team_progress_average}%")
            
            # Vérifier les nouveaux champs
            if hasattr(admin_stats, 'team_events_archived'):
                print(f"Événements archivés: {admin_stats.team_events_archived}")
            if hasattr(admin_stats, 'team_tasks_in_progress'):
                print(f"Tâches en cours: {admin_stats.team_tasks_in_progress}")
            if hasattr(admin_stats, 'team_tasks_in_revision'):
                print(f"Tâches en révision: {admin_stats.team_tasks_in_revision}")
            if hasattr(admin_stats, 'team_tasks_archived'):
                print(f"Tâches archivées: {admin_stats.team_tasks_archived}")
                
        else:
            print("❌ Échec de la mise à jour des statistiques")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la mise à jour des statistiques: {str(e)}")
        return False
    
    # 5. Tester les calculs de pourcentage sécurisés
    print(f"\n=== Test des calculs sécurisés ===")
    
    from core.views.bi_views import safe_percentage, safe_int, safe_float
    
    # Test avec des valeurs normales
    result1 = safe_percentage(10, 100)
    print(f"safe_percentage(10, 100) = {result1}% (attendu: 10.0%)")
    
    # Test avec division par zéro
    result2 = safe_percentage(10, 0)
    print(f"safe_percentage(10, 0) = {result2}% (attendu: 0.0%)")
    
    # Test avec valeurs None
    result3 = safe_percentage(None, 100)
    print(f"safe_percentage(None, 100) = {result3}% (attendu: 0.0%)")
    
    # Test safe_int
    result4 = safe_int("123")
    print(f"safe_int('123') = {result4} (attendu: 123)")
    
    result5 = safe_int(None)
    print(f"safe_int(None) = {result5} (attendu: 0)")
    
    print("\n✅ Tous les tests sont terminés!")
    return True

if __name__ == "__main__":
    success = test_admin_dashboard_data()
    if success:
        print("\n🎉 Tests réussis! Les corrections semblent fonctionner.")
    else:
        print("\n❌ Des problèmes ont été détectés.")
        sys.exit(1)
