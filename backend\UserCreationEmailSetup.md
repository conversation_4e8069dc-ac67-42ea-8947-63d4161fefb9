# Configuration de la création d'utilisateurs avec mots de passe temporaires

## Problèmes résolus

Cette mise à jour résout les problèmes suivants :

1. Le formulaire d'ajout d'utilisateur exigeait un mot de passe obligatoire
2. Aucun mot de passe temporaire n'était généré automatiquement
3. Aucun email n'était envoyé à l'utilisateur nouvellement créé

## Modifications apportées

### Frontend (React)

1. Modification du formulaire d'ajout d'utilisateur pour rendre le champ mot de passe optionnel
2. Création d'un service de génération de mots de passe temporaires
3. Création d'un service d'envoi d'emails
4. Mise à jour du processus d'ajout d'utilisateur pour générer un mot de passe temporaire et envoyer un email

## Configuration requise pour le backend (Django)

Pour que cette fonctionnalité fonctionne correctement, vous devez configurer le backend Django pour prendre en charge l'envoi d'emails et la génération de mots de passe temporaires.

### 1. Configuration de l'envoi d'emails dans Django

Ajoutez les paramètres suivants dans votre fichier `settings.py` :

```python
# Configuration de l'email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # Ou votre serveur SMTP
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # Remplacez par votre email
EMAIL_HOST_PASSWORD = 'votre-mot-de-passe-d-application'  # Mot de passe d'application pour Gmail
```

### 2. Création d'un endpoint pour l'envoi d'emails avec mot de passe temporaire

Créez un nouvel endpoint dans votre API Django pour gérer l'envoi d'emails :

```python
# views.py
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_temp_password_email(request):
    data = request.data
    email = data.get('email')
    name = data.get('name')
    temp_password = data.get('temp_password')
    subject = data.get('subject', 'Votre mot de passe temporaire')
    from_email = data.get('from_email', '<EMAIL>')
    app_url = data.get('app_url', 'http://localhost:3000')
    
    # Créer le contenu HTML de l'email
    html_message = render_to_string('emails/temp_password_email.html', {
        'name': name,
        'temp_password': temp_password,
        'app_url': app_url
    })
    
    # Version texte brut de l'email
    plain_message = strip_tags(html_message)
    
    try:
        # Envoyer l'email
        send_mail(
            subject,
            plain_message,
            from_email,
            [email],
            html_message=html_message,
            fail_silently=False,
        )
        return Response({'success': True, 'message': 'Email envoyé avec succès'})
    except Exception as e:
        return Response({'success': False, 'error': str(e)}, status=500)
```

### 3. Création du template d'email

Créez un template HTML pour l'email dans `templates/emails/temp_password_email.html` :

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Votre mot de passe temporaire</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
    <div style="text-align: center; margin-bottom: 20px;">
        <h1 style="color: #6b46c1; margin: 0;">Bienvenue dans notre application</h1>
    </div>
    
    <div style="margin-bottom: 20px;">
        <p>Bonjour <strong>{{ name }}</strong>,</p>
        <p>Votre compte a été créé avec succès. Voici vos informations de connexion :</p>
    </div>
    
    <div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <p><strong>Mot de passe temporaire :</strong> {{ temp_password }}</p>
    </div>
    
    <div style="margin-bottom: 20px;">
        <p>Pour des raisons de sécurité, vous devrez changer ce mot de passe temporaire lors de votre première connexion.</p>
        <p>Veuillez vous connecter en utilisant le lien ci-dessous :</p>
        <p style="text-align: center;">
            <a 
                href="{{ app_url }}/login" 
                style="display: inline-block; padding: 10px 20px; background: #6b46c1; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;"
            >
                Se connecter
            </a>
        </p>
    </div>
    
    <div style="border-top: 1px solid #e0e0e0; padding-top: 20px; font-size: 12px; color: #666;">
        <p>Si vous n'avez pas demandé la création de ce compte, veuillez ignorer cet email ou contacter l'administrateur.</p>
        <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
    </div>
</body>
</html>
```

### 4. Mise à jour de l'endpoint de création d'utilisateur

Modifiez l'endpoint de création d'utilisateur pour prendre en charge la génération de mots de passe temporaires :

```python
# views.py
import random
import string
from django.contrib.auth.hashers import make_password

def generate_temp_password(length=10):
    # Générer un mot de passe aléatoire avec au moins une majuscule, une minuscule, un chiffre et un caractère spécial
    uppercase_chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'  # Sans I et O qui peuvent être confondus
    lowercase_chars = 'abcdefghijkmnpqrstuvwxyz'  # Sans l et o qui peuvent être confondus
    number_chars = '23456789'  # Sans 0 et 1 qui peuvent être confondus
    special_chars = '@#$%&*'
    
    # Assurer au moins un caractère de chaque type
    password = [
        random.choice(uppercase_chars),
        random.choice(lowercase_chars),
        random.choice(number_chars),
        random.choice(special_chars)
    ]
    
    # Compléter le reste du mot de passe
    all_chars = uppercase_chars + lowercase_chars + number_chars + special_chars
    password.extend(random.choice(all_chars) for _ in range(length - 4))
    
    # Mélanger le mot de passe
    random.shuffle(password)
    
    return ''.join(password)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_user(request):
    data = request.data
    name = data.get('name')
    email = data.get('email')
    role = data.get('role', 'client')
    password = data.get('password')
    generate_temp = data.get('generate_temp_password', False)
    
    # Vérifier si l'email existe déjà
    if User.objects.filter(email=email).exists():
        return Response({'error': 'Cet email est déjà utilisé'}, status=400)
    
    # Générer un mot de passe temporaire si nécessaire
    temp_password_required = False
    if not password or generate_temp:
        password = generate_temp_password()
        temp_password_required = True
    
    # Créer l'utilisateur
    try:
        user = User.objects.create(
            name=name,
            email=email,
            role=role,
            password=make_password(password),
            temp_password_required=temp_password_required
        )
        
        # Si un mot de passe temporaire a été généré, envoyer un email
        if temp_password_required:
            # Appeler la fonction d'envoi d'email ou utiliser une tâche asynchrone
            # Cette partie peut être implémentée selon votre architecture
            # Par exemple, vous pouvez utiliser Celery pour les tâches asynchrones
            
            # Exemple simple d'envoi d'email
            send_temp_password_email_to_user(user, password)
        
        return Response({
            'success': True,
            'message': 'Utilisateur créé avec succès',
            'user': {
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'temp_password_required': user.temp_password_required
            }
        })
    except Exception as e:
        return Response({'error': str(e)}, status=500)

# Fonction auxiliaire pour envoyer l'email
def send_temp_password_email_to_user(user, temp_password):
    subject = 'Votre mot de passe temporaire'
    from_email = '<EMAIL>'
    app_url = 'http://localhost:3000'
    
    # Créer le contenu HTML de l'email
    html_message = render_to_string('emails/temp_password_email.html', {
        'name': user.name,
        'temp_password': temp_password,
        'app_url': app_url
    })
    
    # Version texte brut de l'email
    plain_message = strip_tags(html_message)
    
    # Envoyer l'email
    send_mail(
        subject,
        plain_message,
        from_email,
        [user.email],
        html_message=html_message,
        fail_silently=False,
    )
```

### 5. Mise à jour des URLs

Ajoutez les nouveaux endpoints à votre fichier `urls.py` :

```python
from django.urls import path
from . import views

urlpatterns = [
    # Autres URLs existantes
    path('users/create/', views.create_user, name='create_user'),
    path('email/send-temp-password/', views.send_temp_password_email, name='send_temp_password_email'),
]
```

## Test de la fonctionnalité

1. Assurez-vous que le backend Django est correctement configuré pour l'envoi d'emails
2. Créez un nouvel utilisateur sans spécifier de mot de passe
3. Vérifiez que l'email est bien envoyé à l'adresse spécifiée
4. Testez la connexion avec le mot de passe temporaire
5. Vérifiez que l'utilisateur est redirigé vers la page de changement de mot de passe

## Dépannage

- Si les emails ne sont pas envoyés, vérifiez les paramètres SMTP dans `settings.py`
- Pour Gmail, assurez-vous d'utiliser un mot de passe d'application et non votre mot de passe principal
- Vérifiez les logs du serveur pour les erreurs d'envoi d'email
- Assurez-vous que le template d'email est correctement placé dans le dossier `templates/emails/`