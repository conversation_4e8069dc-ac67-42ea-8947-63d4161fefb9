import os
import sys
import django

# Configurer l'environnement Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

# Importer les modules nécessaires
from django.urls import get_resolver
from django.urls.resolvers import URLPattern, URLResolver

def collect_urls(urls=None, namespace=None, prefix=None):
    if urls is None:
        urls = get_resolver().url_patterns
    
    all_urls = []
    prefix = prefix or ''
    
    for pattern in urls:
        if isinstance(pattern, URLResolver):
            # C'est un groupe d'URLs
            sub_prefix = prefix + pattern.pattern.regex.pattern
            sub_namespace = pattern.namespace or namespace
            all_urls.extend(collect_urls(pattern.url_patterns, sub_namespace, sub_prefix))
        elif isinstance(pattern, URLPattern):
            # C'est une URL individuelle
            url_pattern = prefix + pattern.pattern.regex.pattern
            url_name = f"{namespace}:{pattern.name}" if namespace and pattern.name else pattern.name
            callback = pattern.callback
            
            # Obtenir les méthodes HTTP autorisées
            http_methods = []
            if hasattr(callback, 'view_class'):
                view_class = callback.view_class
                for method in ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace']:
                    if hasattr(view_class, method):
                        http_methods.append(method.upper())
            
            all_urls.append({
                'pattern': url_pattern,
                'name': url_name,
                'callback': callback.__name__ if hasattr(callback, '__name__') else str(callback),
                'http_methods': http_methods
            })
    
    return all_urls

if __name__ == '__main__':
    urls = collect_urls()
    
    # Filtrer les URLs contenant "change-password"
    change_password_urls = [url for url in urls if 'change-password' in url['pattern']]
    
    # Afficher les résultats
    print("\n=== URLs contenant 'change-password' ===")
    for url in change_password_urls:
        print(f"Pattern: {url['pattern']}")
        print(f"Name: {url['name']}")
        print(f"Callback: {url['callback']}")
        print(f"HTTP Methods: {', '.join(url['http_methods']) if url['http_methods'] else 'Non spécifié'}")
        print()
    
    # Afficher toutes les URLs
    print("\n=== Toutes les URLs ===")
    for url in urls:
        print(f"Pattern: {url['pattern']}")
        print(f"Name: {url['name']}")
        print(f"Callback: {url['callback']}")
        print(f"HTTP Methods: {', '.join(url['http_methods']) if url['http_methods'] else 'Non spécifié'}")
        print()
