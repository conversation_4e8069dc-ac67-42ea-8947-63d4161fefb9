#!/usr/bin/env python3
"""
Script de test rapide pour vérifier la gestion des couleurs dans le backend
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000/api"

def test_couleurs_backend():
    """Test rapide de la gestion des couleurs"""
    
    print("🎨 Test de la gestion des couleurs - Backend")
    print("=" * 50)
    
    # 1. Connexion avec un employé ou client
    print("\n1. Connexion...")
    login_data = {
        "email": input("Email (employé ou client): "),
        "password": input("Mot de passe: ")
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code != 200:
            print(f"❌ Erreur de connexion: {response.status_code}")
            print(response.text)
            return
        
        token = response.json().get('access_token')
        headers = {'Authorization': f'Bearer {token}'}
        print("✅ Connexion réussie")
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return
    
    # 2. Test création avec couleur prédéfinie
    print("\n2. Test création avec couleur prédéfinie...")
    tomorrow = datetime.now() + timedelta(days=1)
    
    event_data = {
        "title": "Test couleur prédéfinie",
        "description": "Test de validation des couleurs",
        "start_date": tomorrow.strftime("%Y-%m-%dT10:00:00Z"),
        "end_date": tomorrow.strftime("%Y-%m-%dT11:00:00Z"),
        "start_time": "10:00",
        "end_time": "11:00",
        "color": "bleu_personnel",
        "note": "Test backend"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/personal-events/",
            json=event_data,
            headers=headers
        )
        
        if response.status_code == 201:
            event = response.json().get('event', {})
            print(f"✅ Création réussie")
            print(f"   Couleur reçue: {event.get('color')}")
            print(f"   ID événement: {event.get('id')}")
            event_id = event.get('id')
        else:
            print(f"❌ Erreur création: {response.status_code}")
            print(response.text)
            return
            
    except Exception as e:
        print(f"❌ Erreur création: {e}")
        return
    
    # 3. Test création sans couleur (suggestion automatique)
    print("\n3. Test création sans couleur (suggestion automatique)...")
    
    event_data_2 = {
        "title": "Formation importante",
        "description": "Test suggestion automatique",
        "start_date": tomorrow.strftime("%Y-%m-%dT14:00:00Z"),
        "end_date": tomorrow.strftime("%Y-%m-%dT15:00:00Z"),
        "start_time": "14:00",
        "end_time": "15:00",
        "note": "Sans couleur spécifiée"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/personal-events/",
            json=event_data_2,
            headers=headers
        )
        
        if response.status_code == 201:
            event = response.json().get('event', {})
            print(f"✅ Création réussie")
            print(f"   Couleur suggérée: {event.get('color')}")
        else:
            print(f"❌ Erreur création: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Erreur création: {e}")
    
    # 4. Test modification de couleur
    if event_id:
        print("\n4. Test modification de couleur...")
        
        update_data = {
            "color": "vert_personnel"
        }
        
        try:
            response = requests.put(
                f"{BASE_URL}/personal-events/{event_id}/",
                json=update_data,
                headers=headers
            )
            
            if response.status_code == 200:
                event = response.json().get('event', {})
                print(f"✅ Modification réussie")
                print(f"   Nouvelle couleur: {event.get('color')}")
            else:
                print(f"❌ Erreur modification: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"❌ Erreur modification: {e}")
    
    # 5. Test couleur invalide
    print("\n5. Test couleur invalide...")
    
    event_data_invalid = {
        "title": "Test couleur invalide",
        "start_date": tomorrow.strftime("%Y-%m-%dT16:00:00Z"),
        "end_date": tomorrow.strftime("%Y-%m-%dT17:00:00Z"),
        "start_time": "16:00",
        "end_time": "17:00",
        "color": "couleur_inexistante"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/personal-events/",
            json=event_data_invalid,
            headers=headers
        )
        
        if response.status_code == 400:
            print("✅ Validation fonctionne - couleur invalide rejetée")
            error_data = response.json()
            print(f"   Message d'erreur: {error_data.get('error')}")
        else:
            print(f"❌ Validation échoue: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Erreur test validation: {e}")
    
    # 6. Vérification de la liste
    print("\n6. Vérification de la liste des événements...")
    
    try:
        response = requests.get(f"{BASE_URL}/personal-events/", headers=headers)
        
        if response.status_code == 200:
            events = response.json()
            print(f"✅ Liste récupérée: {len(events)} événement(s)")
            
            for event in events:
                color = event.get('color', 'Non définie')
                print(f"   - {event.get('title')}: {color}")
        else:
            print(f"❌ Erreur récupération liste: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur récupération liste: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Test terminé!")
    print("\nSi tous les tests sont ✅, le backend fonctionne correctement.")
    print("Le problème vient alors du frontend (champs sans id/name).")

if __name__ == "__main__":
    test_couleurs_backend()
