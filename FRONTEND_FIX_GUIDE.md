# 🔧 Guide de Correction Frontend - Dashboard BI

## ❌ **PROBLÈME PRINCIPAL**

D'après la console, le frontend appelle le **mauvais port** :
- ❌ **Incorrect** : `http://localhost:3173/api/bi/super-admin/dashboard/`
- ✅ **Correct** : `http://localhost:8000/api/bi/super-admin/dashboard/`

## 🚀 **SOLUTION RAPIDE**

### **1. Corriger l'URL de Base**
```javascript
// ❌ MAUVAIS
const API_BASE_URL = 'http://localhost:3173/api';

// ✅ CORRECT
const API_BASE_URL = 'http://localhost:8000/api';
```

### **2. Appel API Correct**
```javascript
const response = await fetch('http://localhost:8000/api/bi/super-admin/dashboard/', {
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
});
```

## 🎯 **CORRECTION IMMÉDIATE**

### **Étape 1 : Trouver le fichier de configuration API**
Cherchez dans votre projet frontend un fichier contenant :
- `localhost:3173`
- `API_BASE_URL`
- `baseURL`
- Configuration d'axios ou fetch

### **Étape 2 : Remplacer l'URL**
```javascript
// ❌ REMPLACER CECI
const API_BASE_URL = 'http://localhost:3173/api';
// ou
baseURL: 'http://localhost:3173/api'

// ✅ PAR CECI
const API_BASE_URL = 'http://localhost:8000/api';
// ou
baseURL: 'http://localhost:8000/api'
```

### **Étape 3 : Vérifier l'endpoint**
L'endpoint correct est :
```
GET http://localhost:8000/api/bi/super-admin/dashboard/
```

## 🔧 **Code de Service Correct**
```javascript
// services/biService.js
const API_BASE_URL = 'http://localhost:8000/api';

export const getSuperAdminDashboard = async (token) => {
    const response = await fetch(`${API_BASE_URL}/bi/super-admin/dashboard/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });

    if (!response.ok) {
        throw new Error(`Erreur ${response.status}: ${response.statusText}`);
    }

    return response.json();
};
```

### **2. Composant Dashboard Corrigé**
```javascript
// components/SuperAdminDashboard.jsx
import { useState, useEffect } from 'react';
import BiService from '../services/biService';

const SuperAdminDashboard = () => {
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Récupérer le token depuis le localStorage ou votre store
            const token = localStorage.getItem('access_token');

            if (!token) {
                throw new Error('Token d\'authentification manquant');
            }

            const data = await BiService.getSuperAdminDashboard(token);
            setDashboardData(data);

        } catch (err) {
            console.error('Erreur lors du chargement du dashboard:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDashboardData();

        // Rafraîchissement automatique toutes les 30 secondes
        const interval = setInterval(fetchDashboardData, 30000);

        return () => clearInterval(interval);
    }, []);

    if (loading) return <div>Chargement...</div>;
    if (error) return <div>Erreur: {error}</div>;
    if (!dashboardData) return <div>Aucune donnée disponible</div>;

    return (
        <div className="dashboard">
            {/* Cartes de métriques */}
            <div className="metric-cards">
                {dashboardData.metric_cards?.map((card, index) => (
                    <div key={index} className="metric-card">
                        <h3>{card.title}</h3>
                        <div className="value">{card.value}</div>
                        <div className="trend">{card.trend} {card.trend_period}</div>
                    </div>
                ))}
            </div>

            {/* Graphiques */}
            <div className="charts">
                {/* Graphique Doughnut - Actifs vs Inactifs */}
                {dashboardData.charts?.active_vs_inactive && (
                    <div className="chart-container">
                        <h3>{dashboardData.charts.active_vs_inactive.title}</h3>
                        {/* Votre composant de graphique doughnut */}
                    </div>
                )}

                {/* Graphique Bar - Distribution par Rôle */}
                {dashboardData.charts?.role_distribution && (
                    <div className="chart-container">
                        <h3>{dashboardData.charts.role_distribution.title}</h3>
                        {/* Votre composant de graphique en barres */}
                    </div>
                )}
            </div>

            {/* Bouton de rafraîchissement manuel */}
            <button onClick={fetchDashboardData} className="refresh-btn">
                🔄 Actualiser
            </button>
        </div>
    );
};

export default SuperAdminDashboard;
```

### **3. Gestion des Erreurs**
```javascript
// utils/errorHandler.js
export const handleApiError = (error, response) => {
    if (response?.status === 403) {
        return 'Accès non autorisé. Vous devez être super admin.';
    }
    if (response?.status === 401) {
        return 'Session expirée. Veuillez vous reconnecter.';
    }
    if (response?.status === 404) {
        return 'Endpoint non trouvé. Vérifiez l\'URL de l\'API.';
    }
    if (response?.status === 500) {
        return 'Erreur serveur. Contactez l\'administrateur.';
    }
    return error.message || 'Erreur inconnue';
};
```

## 🔍 **Debugging**

### **1. Vérifier la Configuration**
```javascript
// Dans la console du navigateur
console.log('API Base URL:', 'http://localhost:8000/api');
console.log('Token:', localStorage.getItem('access_token'));
```

### **2. Tester l'Endpoint Manuellement**
```javascript
// Test direct dans la console
fetch('http://localhost:8000/api/bi/super-admin/dashboard/', {
    headers: {
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log('Données reçues:', data))
.catch(error => console.error('Erreur:', error));
```

## ✅ **Checklist de Vérification**

- [ ] URL correcte : `http://localhost:8000/api/bi/super-admin/dashboard/`
- [ ] Token d'authentification inclus
- [ ] Headers corrects
- [ ] Gestion des erreurs 403/401/404/500
- [ ] Rafraîchissement automatique (30s)
- [ ] Affichage des vraies données (29 utilisateurs)
- [ ] Suppression des données de démonstration

## 🎯 **Données Attendues**

Après correction, vous devriez recevoir :
```json
{
    "metric_cards": [
        {"title": "Nombre total d'utilisateurs", "value": 29},
        {"title": "Utilisateurs actifs", "value": 7},
        {"title": "Utilisateurs inactifs", "value": 22}
    ],
    "charts": {
        "active_vs_inactive": {...},
        "role_distribution": {...}
    }
}
```
