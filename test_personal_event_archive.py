#!/usr/bin/env python
"""
Script de test pour l'archivage d'événements personnels
"""

import requests
import json
from datetime import datetime, timedelta

def test_personal_event_archive():
    """Test complet de l'archivage d'événements personnels"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test d'archivage d'événements personnels ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Créer un événement à archiver
    print("\n2. Création d'un événement à archiver...")
    tomorrow = datetime.now() + timedelta(days=1)
    
    event_data = {
        "title": "Événement à archiver",
        "description": "Cet événement sera archivé pour test",
        "start_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
        "end_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
        "start_time": "10:00",
        "end_time": "11:00",
        "note": "Test d'archivage",
        "color": "#FF6B6B"
    }
    
    create_response = requests.post(
        f"{base_url}/personal-events/",
        json=event_data,
        headers=headers
    )
    
    print(f"📡 Statut création: {create_response.status_code}")
    if create_response.status_code != 201:
        print(f"❌ Échec de la création: {create_response.json()}")
        return
    
    event_id = create_response.json()['event']['id']
    print(f"✅ Événement créé avec succès")
    print(f"   ID: {event_id}")
    print(f"   Statut initial: {create_response.json()['event']['status']}")
    
    # 3. Vérifier l'état initial
    print("\n3. Vérification de l'état initial...")
    initial_check = requests.get(
        f"{base_url}/personal-events/{event_id}/",
        headers=headers
    )
    
    if initial_check.status_code == 200:
        initial_data = initial_check.json()
        print(f"✅ État initial vérifié:")
        print(f"   Titre: {initial_data['title']}")
        print(f"   Statut: {initial_data['status']}")
        print(f"   Peut gérer: {initial_data['can_manage']}")
    else:
        print(f"❌ Échec de vérification: {initial_check.json()}")
        return
    
    # 4. ARCHIVER L'ÉVÉNEMENT
    print("\n4. 🗂️ ARCHIVAGE DE L'ÉVÉNEMENT...")
    archive_response = requests.put(
        f"{base_url}/personal-events/{event_id}/archive/",
        json={},
        headers=headers
    )
    
    print(f"📡 Statut archivage: {archive_response.status_code}")
    
    if archive_response.status_code == 200:
        archive_data = archive_response.json()
        print("✅ Archivage réussi !")
        print(f"   Message: {archive_data['message']}")
        print(f"   Nouveau statut: {archive_data['event']['status']}")
        print(f"   Mis à jour le: {archive_data['event']['updated_at']}")
    else:
        print(f"❌ Échec de l'archivage: {archive_response.json()}")
        return
    
    # 5. Vérifier l'archivage
    print("\n5. Vérification de l'archivage...")
    verify_response = requests.get(
        f"{base_url}/personal-events/{event_id}/",
        headers=headers
    )
    
    if verify_response.status_code == 200:
        verify_data = verify_response.json()
        print(f"✅ Archivage vérifié:")
        print(f"   Statut: {verify_data['status']}")
        print(f"   Créé le: {verify_data['created_at']}")
        print(f"   Mis à jour le: {verify_data['updated_at']}")
        
        if verify_data['status'] == 'archived':
            print("🎯 L'événement est bien archivé !")
        else:
            print("❌ L'événement n'est pas archivé correctement")
    else:
        print(f"❌ Échec de vérification: {verify_response.json()}")
    
    # 6. Test d'archivage double (idempotence)
    print("\n6. Test d'archivage double...")
    double_archive = requests.put(
        f"{base_url}/personal-events/{event_id}/archive/",
        json={},
        headers=headers
    )
    
    print(f"📡 Statut double archivage: {double_archive.status_code}")
    if double_archive.status_code == 200:
        print("✅ Double archivage géré correctement (idempotent)")
    else:
        print(f"❌ Problème avec le double archivage: {double_archive.json()}")
    
    # 7. Vérifier dans la liste des événements
    print("\n7. Vérification dans la liste des événements...")
    list_response = requests.get(
        f"{base_url}/personal-events/",
        headers=headers
    )
    
    if list_response.status_code == 200:
        events_list = list_response.json()
        archived_events = [e for e in events_list if e['status'] == 'archived']
        print(f"✅ Liste récupérée: {len(events_list)} événement(s) total")
        print(f"   Événements archivés: {len(archived_events)}")
        
        # Trouver notre événement
        our_event = next((e for e in events_list if e['id'] == event_id), None)
        if our_event and our_event['status'] == 'archived':
            print("🎯 Notre événement apparaît bien comme archivé dans la liste !")
        else:
            print("❌ Notre événement n'apparaît pas correctement dans la liste")
    else:
        print(f"❌ Échec de récupération de la liste: {list_response.json()}")
    
    # 8. Test d'erreur - événement inexistant
    print("\n8. Test d'erreur - événement inexistant...")
    fake_id = "000000000000000000000000"
    error_response = requests.put(
        f"{base_url}/personal-events/{fake_id}/archive/",
        json={},
        headers=headers
    )
    
    print(f"📡 Statut erreur: {error_response.status_code}")
    if error_response.status_code == 404:
        error_data = error_response.json()
        print("✅ Gestion d'erreur correcte")
        print(f"   Message: {error_data['error']}")
    else:
        print(f"❌ Gestion d'erreur incorrecte: {error_response.json()}")
    
    print("\n" + "="*60)
    print("🎯 TESTS D'ARCHIVAGE TERMINÉS AVEC SUCCÈS !")
    print("="*60)
    print(f"📋 Résumé:")
    print(f"   - Événement créé: ✅")
    print(f"   - Archivage réussi: ✅")
    print(f"   - Vérification: ✅")
    print(f"   - Idempotence: ✅")
    print(f"   - Gestion d'erreurs: ✅")

if __name__ == "__main__":
    test_personal_event_archive()
