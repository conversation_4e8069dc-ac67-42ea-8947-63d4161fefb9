from django.core.management.base import BaseCommand
from core.models.bi_model import BiMetric, BiMetricSnapshot, BiDashboard
from core.tasks.bi_tasks import update_bi_metrics
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Nettoie et reconstruit toutes les métriques BI'
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Nettoyage et reconstruction des métriques BI...'))
        
        # Supprimer toutes les métriques existantes
        self.stdout.write('Suppression des métriques existantes...')
        bi_metrics_count = BiMetric.objects.count()
        BiMetric.objects.delete()
        self.stdout.write(self.style.SUCCESS(f'Supprimé {bi_metrics_count} métriques BiMetric'))
        
        # Supprimer tous les instantanés
        self.stdout.write('Suppression des instantanés de métriques...')
        snapshots_count = BiMetricSnapshot.objects.count()
        BiMetricSnapshot.objects.delete()
        self.stdout.write(self.style.SUCCESS(f'Supprimé {snapshots_count} instantanés BiMetricSnapshot'))
        
        # Reconstruire les métriques
        self.stdout.write('Reconstruction des métriques BI...')
        update_bi_metrics()
        
        # Vérifier les résultats
        new_metrics_count = BiMetric.objects.count()
        self.stdout.write(self.style.SUCCESS(f'Métriques reconstruites: {new_metrics_count} BiMetric'))
        
        self.stdout.write(self.style.SUCCESS('Nettoyage et reconstruction des métriques BI terminés'))
