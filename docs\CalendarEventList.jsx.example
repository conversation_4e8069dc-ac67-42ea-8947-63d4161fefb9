// Exemple de composant pour afficher la liste des événements du calendrier
import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, CircularProgress, Alert, TextField, InputAdornment } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import EventCard from './CalendarEventCard';
import { getEvents } from '../services/eventService';

const CalendarEventList = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const eventsData = await getEvents();
        setEvents(eventsData);
        setError(null);
      } catch (err) {
        setError('Erreur lors du chargement des événements. Veuillez réessayer.');
        console.error('Erreur lors du chargement des événements:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  // Filtrer les événements en fonction du terme de recherche
  const filteredEvents = events.filter(event => {
    const searchLower = searchTerm.toLowerCase();
    return (
      event.title.toLowerCase().includes(searchLower) ||
      (event.description && event.description.toLowerCase().includes(searchLower)) ||
      (event.team_name && event.team_name.toLowerCase().includes(searchLower)) ||
      (event.created_by_name && event.created_by_name.toLowerCase().includes(searchLower))
    );
  });

  return (
    <Container maxWidth="md">
      <Typography variant="h4" component="h1" gutterBottom>
        Événements du Calendrier
      </Typography>

      {/* Barre de recherche */}
      <TextField
        fullWidth
        variant="outlined"
        placeholder="Rechercher par titre, équipe ou responsable..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        margin="normal"
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
        }}
      />

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ my: 2 }}>
          {error}
        </Alert>
      ) : filteredEvents.length === 0 ? (
        <Alert severity="info" sx={{ my: 2 }}>
          {searchTerm ? 'Aucun événement ne correspond à votre recherche.' : 'Aucun événement à afficher.'}
        </Alert>
      ) : (
        <Box sx={{ mt: 3 }}>
          {/* Statistiques des événements */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Typography variant="body2">
              Total: <strong>{filteredEvents.length}</strong> événement(s)
            </Typography>
            <Typography variant="body2">
              Équipes: <strong>{new Set(filteredEvents.filter(e => e.team_name).map(e => e.team_name)).size}</strong> différente(s)
            </Typography>
            <Typography variant="body2">
              Responsables: <strong>{new Set(filteredEvents.filter(e => e.created_by_name).map(e => e.created_by_name)).size}</strong> différent(s)
            </Typography>
          </Box>

          {/* Liste des événements */}
          {filteredEvents.map((event) => (
            <EventCard key={event.id} event={event} />
          ))}
        </Box>
      )}
    </Container>
  );
};

export default CalendarEventList;