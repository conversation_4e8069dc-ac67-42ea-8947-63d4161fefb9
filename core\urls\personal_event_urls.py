from django.urls import path
from ..views.personal_event_views import PersonalEventListCreateView, PersonalEventDetailView
from ..views.personal_event_archived_list_views import PersonalEventArchivedListView

urlpatterns = [
    # Routes pour les événements personnels
    path('personal-events/', PersonalEventListCreateView.as_view(), name='personal-event-list-create'),
    path('personal-events/<str:event_id>/', PersonalEventDetailView.as_view(), name='personal-event-detail'),

    # Route pour les événements archivés
    path('personal-events/archived/list/', PersonalEventArchivedListView.as_view(), name='personal-event-archived-list'),
]