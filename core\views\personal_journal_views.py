from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.personal_journal_model import PersonalJournal
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class PersonalJournalListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une nouvelle entrée de journal personnel (employé ou client)"""
        try:
            data = request.data
            user = request.user

            # Log pour le débogage
            logger.info(f"Création de journal personnel - Données reçues: {data}")
            logger.info(f"Utilisateur: {user.id} ({user.name})")

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de l'entrée est requis"}, status=400)
            if not data.get('content'):
                return Response({"error": "Le contenu de l'entrée est requis"}, status=400)

            # Vérifier si une entrée de journal avec le même titre existe déjà pour cet utilisateur
            if PersonalJournal.objects(title=data['title'], created_by=str(user.id)).first():
                return Response({"error": "Vous avez déjà une entrée de journal avec ce titre"}, status=400)

            # Vérifier si la date est fournie, sinon utiliser la date actuelle
            entry_date = None
            if data.get('entry_date'):
                try:
                    # Nettoyage de la chaîne de date pour éviter les problèmes de format
                    entry_date_str = data['entry_date']
                    if isinstance(entry_date_str, str):
                        entry_date_str = entry_date_str.strip().replace('Z', '+00:00')
                        entry_date = datetime.fromisoformat(entry_date_str)
                    else:
                        return Response({"error": "Format de date invalide"}, status=400)
                except ValueError as ve:
                    logger.error(f"Erreur de format de date: {ve}")
                    return Response({"error": f"Format de date invalide: {str(ve)}"}, status=400)
            else:
                # Utiliser la date actuelle si aucune date n'est fournie
                entry_date = datetime.now(timezone.utc)
                logger.info(f"Aucune date fournie, utilisation de la date actuelle: {entry_date}")

            # Créer l'entrée de journal
            try:
                journal = PersonalJournal(
                    title=data['title'],
                    content=data['content'],
                    entry_date=entry_date,
                    created_by=str(user.id),
                    created_by_name=user.name
                )
                journal.save()
                logger.info(f"Journal personnel créé avec succès: {journal.id}")

                return Response({
                    "message": "Entrée de journal créée avec succès",
                    "journal": {
                        "id": str(journal.id),
                        "title": journal.title,
                        "content": journal.content,
                        "entry_date": journal.entry_date,
                        "created_by": journal.created_by,
                        "created_by_name": journal.created_by_name,
                        "created_at": journal.created_at,
                        "updated_at": journal.updated_at,
                        "is_archived": journal.is_archived
                    }
                }, status=201)
            except Exception as save_error:
                logger.error(f"Erreur lors de la sauvegarde du journal: {save_error}")
                return Response({"error": f"Erreur lors de la sauvegarde: {str(save_error)}"}, status=500)

        except Exception as e:
            logger.error(f"Error in PersonalJournalListCreateView.post: {str(e)}")
            return Response({"error": f"Une erreur est survenue lors de la création de l'entrée de journal: {str(e)}"}, status=500)

    def get(self, request):
        """Liste des entrées de journal de l'utilisateur connecté"""
        try:
            user = request.user
            archived = request.query_params.get('archived', 'false').lower() == 'true'
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')

            # Filtrer par utilisateur et statut d'archivage
            query = {
                'created_by': str(user.id),
                'is_archived': archived
            }

            # Ajouter des filtres de date si spécifiés
            if start_date and end_date:
                try:
                    start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                    end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                    query['entry_date__gte'] = start
                    query['entry_date__lte'] = end
                except ValueError:
                    return Response({"error": "Format de date invalide"}, status=400)

            # Récupérer les entrées de journal
            journals = PersonalJournal.objects(**query).order_by('-entry_date')

            # Formater les entrées
            journals_data = []
            for journal in journals:
                journals_data.append({
                    'id': str(journal.id),
                    'title': journal.title,
                    'content': journal.content,
                    'entry_date': journal.entry_date,
                    'created_by': journal.created_by,
                    'created_by_name': journal.created_by_name,
                    'created_at': journal.created_at,
                    'updated_at': journal.updated_at,
                    'is_archived': journal.is_archived
                })

            return Response(journals_data)

        except Exception as e:
            logger.error(f"Error in PersonalJournalListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalJournalDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, journal_id):
        """Récupérer les détails d'une entrée de journal"""
        try:
            user = request.user

            try:
                journal = PersonalJournal.objects.get(id=journal_id)
            except PersonalJournal.DoesNotExist:
                return Response({"error": "Entrée de journal non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à voir cette entrée
            if journal.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à voir cette entrée de journal"}, status=403)

            # Retourner les détails de l'entrée
            return Response({
                'id': str(journal.id),
                'title': journal.title,
                'content': journal.content,
                'entry_date': journal.entry_date,
                'created_by': journal.created_by,
                'created_by_name': journal.created_by_name,
                'created_at': journal.created_at,
                'updated_at': journal.updated_at,
                'is_archived': journal.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalJournalDetailView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def put(self, request, journal_id):
        """Mettre à jour une entrée de journal"""
        try:
            user = request.user
            data = request.data

            try:
                journal = PersonalJournal.objects.get(id=journal_id)
            except PersonalJournal.DoesNotExist:
                return Response({"error": "Entrée de journal non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à modifier cette entrée
            if not journal.can_manage_journal(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cette entrée de journal"}, status=403)

            # Mettre à jour les champs de l'entrée
            if 'title' in data:
                journal.title = data['title']
            if 'content' in data:
                journal.content = data['content']
            if 'entry_date' in data:
                try:
                    journal.entry_date = datetime.fromisoformat(data['entry_date'].replace('Z', '+00:00'))
                except ValueError:
                    return Response({"error": "Format de date invalide"}, status=400)

            # Sauvegarder les modifications
            journal.save()

            return Response({
                "message": "Entrée de journal mise à jour avec succès",
                "journal": {
                    'id': str(journal.id),
                    'title': journal.title,
                    'content': journal.content,
                    'entry_date': journal.entry_date,
                    'created_by': journal.created_by,
                    'created_by_name': journal.created_by_name,
                    'created_at': journal.created_at,
                    'updated_at': journal.updated_at,
                    'is_archived': journal.is_archived
                }
            })

        except Exception as e:
            logger.error(f"Error in PersonalJournalDetailView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def delete(self, request, journal_id):
        """Supprimer une entrée de journal"""
        try:
            user = request.user

            try:
                journal = PersonalJournal.objects.get(id=journal_id)
            except PersonalJournal.DoesNotExist:
                return Response({"error": "Entrée de journal non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à supprimer cette entrée
            if not journal.can_manage_journal(user):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cette entrée de journal"}, status=403)

            # Supprimer l'entrée
            journal.delete()

            return Response({"message": "Entrée de journal supprimée avec succès"}, status=200)

        except Exception as e:
            logger.error(f"Error in PersonalJournalDetailView.delete: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalJournalArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, journal_id):
        """Archiver une entrée de journal"""
        try:
            user = request.user

            try:
                journal = PersonalJournal.objects.get(id=journal_id)
            except PersonalJournal.DoesNotExist:
                return Response({"error": "Entrée de journal non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à archiver cette entrée
            if not journal.can_manage_journal(user):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cette entrée de journal"}, status=403)

            # Archiver l'entrée
            journal.is_archived = True
            journal.save()

            return Response({
                "message": "Entrée de journal archivée avec succès",
                "is_archived": journal.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalJournalArchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalJournalUnarchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, journal_id):
        """Désarchiver une entrée de journal"""
        try:
            user = request.user

            try:
                journal = PersonalJournal.objects.get(id=journal_id)
            except PersonalJournal.DoesNotExist:
                return Response({"error": "Entrée de journal non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à désarchiver cette entrée
            if not journal.can_manage_journal(user):
                return Response({"error": "Vous n'êtes pas autorisé à désarchiver cette entrée de journal"}, status=403)

            # Vérifier que l'entrée est bien archivée
            if not journal.is_archived:
                return Response({"error": "Cette entrée n'est pas archivée et ne peut donc pas être désarchivée"}, status=400)

            # Désarchiver l'entrée
            journal.is_archived = False
            journal.save()

            return Response({
                "message": "Entrée de journal désarchivée avec succès",
                "is_archived": journal.is_archived
            })

        except Exception as e:
            logger.error(f"Error in PersonalJournalUnarchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)
