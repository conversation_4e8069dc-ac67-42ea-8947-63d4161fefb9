from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.pomodoro_model import PomodoroSettings
import logging

logger = logging.getLogger(__name__)

class PomodoroSettingsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Récupérer les paramètres Pomodoro de l'utilisateur"""
        try:
            user = request.user

            # Récupérer ou créer les paramètres Pomodoro de l'utilisateur
            try:
                settings = PomodoroSettings.objects.get(user_id=str(user.id))
                created = False
            except PomodoroSettings.DoesNotExist:
                settings = PomodoroSettings(
                    user_id=str(user.id),
                    focus_duration=25,
                    short_break_duration=5,
                    long_break_duration=15,
                    sessions_before_long_break=4
                )
                settings.save()
                created = True

            # Récupérer le statut de la session en cours
            session_status = settings.get_session_status()

            return Response({
                'is_active': settings.is_active,
                'focus_duration': settings.focus_duration,
                'short_break_duration': settings.short_break_duration,
                'long_break_duration': settings.long_break_duration,
                'sessions_before_long_break': settings.sessions_before_long_break,
                'current_session': session_status,
                'session_history': [
                    {
                        'session_id': session.session_id,
                        'start_time': session.start_time,
                        'end_time': session.end_time,
                        'focus_duration': session.focus_duration,
                        'break_duration': session.break_duration,
                        'completed': session.completed
                    } for session in settings.session_history[-10:]  # Retourner les 10 dernières sessions
                ] if settings.session_history else []
            })

        except Exception as e:
            logger.error(f"Error in PomodoroSettingsView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def put(self, request):
        """Mettre à jour les paramètres Pomodoro de l'utilisateur"""
        try:
            user = request.user
            data = request.data

            # Récupérer ou créer les paramètres Pomodoro de l'utilisateur
            try:
                settings = PomodoroSettings.objects.get(user_id=str(user.id))
            except PomodoroSettings.DoesNotExist:
                settings = PomodoroSettings(
                    user_id=str(user.id),
                    focus_duration=25,
                    short_break_duration=5,
                    long_break_duration=15,
                    sessions_before_long_break=4
                )
                settings.save()

            # Mettre à jour les paramètres
            if 'focus_duration' in data:
                settings.focus_duration = int(data['focus_duration'])
            if 'short_break_duration' in data:
                settings.short_break_duration = int(data['short_break_duration'])
            if 'long_break_duration' in data:
                settings.long_break_duration = int(data['long_break_duration'])
            if 'sessions_before_long_break' in data:
                settings.sessions_before_long_break = int(data['sessions_before_long_break'])

            settings.save()

            return Response({
                'message': 'Paramètres Pomodoro mis à jour avec succès',
                'settings': {
                    'is_active': settings.is_active,
                    'focus_duration': settings.focus_duration,
                    'short_break_duration': settings.short_break_duration,
                    'long_break_duration': settings.long_break_duration,
                    'sessions_before_long_break': settings.sessions_before_long_break
                }
            })

        except Exception as e:
            logger.error(f"Error in PomodoroSettingsView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PomodoroControlView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, action):
        """Contrôler la session Pomodoro (start, pause, resume, complete, reset)"""
        try:
            user = request.user

            # Récupérer les paramètres Pomodoro de l'utilisateur
            try:
                settings = PomodoroSettings.objects.get(user_id=str(user.id))
            except PomodoroSettings.DoesNotExist:
                settings = PomodoroSettings(
                    user_id=str(user.id),
                    focus_duration=25,
                    short_break_duration=5,
                    long_break_duration=15,
                    sessions_before_long_break=4
                )
                settings.save()

            # Exécuter l'action demandée
            if action == 'start':
                session = settings.start_session()
                message = 'Session Pomodoro démarrée'
            elif action == 'pause':
                if not settings.current_session or settings.current_session.completed:
                    return Response({"error": "Aucune session active à mettre en pause"}, status=400)
                session = settings.pause_session()
                message = 'Session Pomodoro mise en pause'
            elif action == 'resume':
                if not settings.current_session or not settings.current_session.paused_at:
                    return Response({"error": "Aucune session en pause à reprendre"}, status=400)
                session = settings.resume_session()
                message = 'Session Pomodoro reprise'
            elif action == 'complete':
                if not settings.current_session or settings.current_session.completed:
                    return Response({"error": "Aucune session active à terminer"}, status=400)
                session = settings.complete_session()
                message = 'Session Pomodoro terminée'
            elif action == 'reset':
                settings.reset_session()
                message = 'Session Pomodoro réinitialisée'
                return Response({
                    'message': message,
                    'is_active': settings.is_active,
                    'current_session': {
                        "active": False,
                        "status": "inactive",
                        "remaining_time": 0
                    }
                })
            else:
                return Response({"error": "Action non reconnue"}, status=400)

            # Si l'action a échoué
            if not session:
                return Response({"error": "Impossible d'exécuter cette action dans l'état actuel"}, status=400)

            # Récupérer le statut de la session
            session_status = settings.get_session_status()

            return Response({
                'message': message,
                'is_active': settings.is_active,
                'current_session': session_status
            })

        except Exception as e:
            logger.error(f"Error in PomodoroControlView.post: {str(e)}")
            return Response({"error": str(e)}, status=500)
