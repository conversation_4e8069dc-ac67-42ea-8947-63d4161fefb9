from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..mongo_models import PersonalEvent, User
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class PersonalEventListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer un nouvel événement personnel (employé ou client)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de l'événement est requis"}, status=400)
            if not data.get('start_date') or not data.get('end_date'):
                return Response({"error": "Les dates de début et de fin sont requises"}, status=400)
            if not data.get('start_time') or not data.get('end_time'):
                return Response({"error": "Les heures de début et de fin sont requises"}, status=400)

            # Vérifier si un événement personnel avec le même titre existe déjà pour cet utilisateur
            if PersonalEvent.objects(title=data['title'], created_by=str(user.id)).first():
                return Response({"error": "Vous avez déjà un événement personnel avec ce titre"}, status=400)

            # Vérifier que les dates sont valides (pas dans le passé)
            try:
                start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
                now = datetime.now(timezone.utc)

                if start_date.date() < now.date():
                    return Response({"error": "La date de début ne peut pas être dans le passé"}, status=400)
                if end_date.date() < start_date.date():
                    return Response({"error": "La date de fin ne peut pas être antérieure à la date de début"}, status=400)
            except ValueError:
                return Response({"error": "Format de date invalide"}, status=400)

            # Créer l'événement personnel
            event = PersonalEvent(
                title=data['title'],
                description=data.get('description', ''),
                start_date=datetime.fromisoformat(data['start_date'].replace('Z', '+00:00')),
                end_date=datetime.fromisoformat(data['end_date'].replace('Z', '+00:00')),
                start_time=data['start_time'],
                end_time=data['end_time'],
                note=data.get('note', ''),
                status='pending',
                color=data.get('color', '#3788d8'),  # Utiliser la couleur fournie ou la couleur par défaut
                created_by=str(user.id),
                created_by_name=user.name
            )
            event.save()

            return Response({
                "message": "Événement personnel créé avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "description": event.description,
                    "start_date": event.start_date,
                    "end_date": event.end_date,
                    "start_time": event.start_time,
                    "end_time": event.end_time,
                    "note": event.note,
                    "status": event.status,
                    "color": event.color,
                    "created_by": event.created_by,
                    "created_by_name": event.created_by_name,
                    "created_at": event.created_at,
                    "updated_at": event.updated_at
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in PersonalEventListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de l'événement personnel"}, status=500)

    def get(self, request):
        """Liste des événements personnels de l'utilisateur connecté (EXCLUANT les archivés)"""
        try:
            user = request.user

            # Récupérer uniquement les événements personnels NON ARCHIVÉS créés par l'utilisateur connecté
            events = PersonalEvent.objects(
                created_by=str(user.id),
                status__ne='archived'  # Exclure les événements archivés
            )

            # Formater les événements
            events_data = []
            for event in events:
                events_data.append({
                    'id': str(event.id),
                    'title': event.title,
                    'description': event.description,
                    'start_date': event.start_date,
                    'end_date': event.end_date,
                    'start_time': event.start_time,
                    'end_time': event.end_time,
                    'note': event.note,
                    'status': event.status,
                    'color': event.color,
                    'created_by': event.created_by,
                    'created_by_name': event.created_by_name,
                    'created_at': event.created_at,
                    'updated_at': event.updated_at,
                    'can_manage': event.can_manage_event(user),
                    'can_update_status': event.can_update_status(user)
                })

            return Response(events_data)

        except Exception as e:
            logger.error(f"Error in PersonalEventListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalEventDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, event_id):
        """Récupérer les détails d'un événement personnel"""
        try:
            user = request.user

            try:
                event = PersonalEvent.objects.get(id=event_id)
            except PersonalEvent.DoesNotExist:
                return Response({"error": "Événement personnel non trouvé"}, status=404)

            # Vérifier que l'utilisateur est autorisé à voir cet événement
            if event.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à voir cet événement personnel"}, status=403)

            # Retourner les détails de l'événement
            return Response({
                'id': str(event.id),
                'title': event.title,
                'description': event.description,
                'start_date': event.start_date,
                'end_date': event.end_date,
                'start_time': event.start_time,
                'end_time': event.end_time,
                'note': event.note,
                'status': event.status,
                'color': event.color,
                'created_by': event.created_by,
                'created_by_name': event.created_by_name,
                'created_at': event.created_at,
                'updated_at': event.updated_at,
                'can_manage': event.can_manage_event(user),
                'can_update_status': event.can_update_status(user)
            })

        except Exception as e:
            logger.error(f"Error in PersonalEventDetailView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def put(self, request, event_id):
        """Mettre à jour un événement personnel"""
        try:
            user = request.user
            data = request.data

            try:
                event = PersonalEvent.objects.get(id=event_id)
            except PersonalEvent.DoesNotExist:
                return Response({"error": "Événement personnel non trouvé"}, status=404)

            # Vérifier que l'utilisateur est autorisé à modifier cet événement
            if not event.can_manage_event(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cet événement personnel"}, status=403)

            # Mettre à jour les champs de l'événement
            if 'title' in data:
                event.title = data['title']
            if 'description' in data:
                event.description = data['description']
            if 'start_date' in data:
                event.start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
            if 'end_date' in data:
                event.end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
            if 'start_time' in data:
                event.start_time = data['start_time']
            if 'end_time' in data:
                event.end_time = data['end_time']
            if 'note' in data:
                event.note = data['note']
            if 'color' in data:
                event.color = data['color']

            # Sauvegarder les modifications
            event.save()

            return Response({
                "message": "Événement personnel mis à jour avec succès",
                "event": {
                    'id': str(event.id),
                    'title': event.title,
                    'description': event.description,
                    'start_date': event.start_date,
                    'end_date': event.end_date,
                    'start_time': event.start_time,
                    'end_time': event.end_time,
                    'note': event.note,
                    'status': event.status,
                    'color': event.color,
                    'created_by': event.created_by,
                    'created_by_name': event.created_by_name,
                    'created_at': event.created_at,
                    'updated_at': event.updated_at
                }
            })

        except Exception as e:
            logger.error(f"Error in PersonalEventDetailView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def patch(self, request, event_id):
        """Mettre à jour le statut d'un événement personnel"""
        try:
            user = request.user
            data = request.data

            try:
                event = PersonalEvent.objects.get(id=event_id)
            except PersonalEvent.DoesNotExist:
                return Response({"error": "Événement personnel non trouvé"}, status=404)

            # Vérifier que l'utilisateur est autorisé à mettre à jour le statut
            if not event.can_update_status(user):
                return Response({"error": "Vous n'êtes pas autorisé à mettre à jour le statut de cet événement personnel"}, status=403)

            # Mettre à jour le statut
            if 'status' in data and data['status'] in ['pending', 'completed', 'archived']:
                event.status = data['status']
                event.save()

                return Response({
                    "message": "Statut de l'événement personnel mis à jour avec succès",
                    "status": event.status
                })
            else:
                return Response({"error": "Statut invalide. Les valeurs autorisées sont: pending, completed, archived"}, status=400)

        except Exception as e:
            logger.error(f"Error in PersonalEventDetailView.patch: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def delete(self, request, event_id):
        """Supprimer un événement personnel"""
        try:
            user = request.user

            try:
                event = PersonalEvent.objects.get(id=event_id)
            except PersonalEvent.DoesNotExist:
                return Response({"error": "Événement personnel non trouvé"}, status=404)

            # Vérifier que l'utilisateur est autorisé à supprimer cet événement
            if not event.can_manage_event(user):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cet événement personnel"}, status=403)

            # Supprimer l'événement
            event.delete()

            return Response({"message": "Événement personnel supprimé avec succès"}, status=200)

        except Exception as e:
            logger.error(f"Error in PersonalEventDetailView.delete: {str(e)}")
            return Response({"error": str(e)}, status=500)