# Guide de Test Manuel pour le Calendrier Personnel

## P<PERSON>requis
- Serveur backend en cours d'exécution
- Accès à un outil comme Postman ou à l'interface frontend si disponible
- Comptes utilisateurs de test (employé et client)

## Étapes de Test

### 1. Authentification

1. **Se connecter en tant qu'employé**
   - Méthode: POST
   - URL: `/api/login/`
   - Corps de la requête:
     ```json
     {
       "email": "<EMAIL>",
       "password": "password123"
     }
     ```
   - Conserver le token d'authentification retourné

2. **Se connecter en tant que client**
   - Méthode: POST
   - URL: `/api/login/`
   - Corps de la requête:
     ```json
     {
       "email": "<EMAIL>",
       "password": "password123"
     }
     ```
   - Conserver le token d'authentification retourné

### 2. Création d'Événements Personnels

1. **Créer un événement personnel en tant qu'employé**
   - Méthode: POST
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Corps de la requête:
     ```json
     {
       "title": "Réunion personnelle",
       "description": "Préparation du rapport mensuel",
       "start_date": "2023-12-15T00:00:00.000Z",
       "end_date": "2023-12-15T00:00:00.000Z",
       "start_time": "09:00",
       "end_time": "10:30",
       "note": "Apporter les documents nécessaires"
     }
     ```
   - Vérifier que la réponse a un statut 201 et contient les détails de l'événement créé

2. **Créer un événement personnel en tant que client**
   - Méthode: POST
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_client>`
   - Corps de la requête:
     ```json
     {
       "title": "Rendez-vous important",
       "description": "Discussion sur le nouveau projet",
       "start_date": "2023-12-16T00:00:00.000Z",
       "end_date": "2023-12-16T00:00:00.000Z",
       "start_time": "14:00",
       "end_time": "15:00",
       "note": "Préparer les questions"
     }
     ```
   - Vérifier que la réponse a un statut 201 et contient les détails de l'événement créé

### 3. Consultation des Événements Personnels

1. **Consulter les événements personnels en tant qu'employé**
   - Méthode: GET
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Vérifier que seuls les événements créés par l'employé sont retournés

2. **Consulter les événements personnels en tant que client**
   - Méthode: GET
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_client>`
   - Vérifier que seuls les événements créés par le client sont retournés

3. **Consulter un événement personnel spécifique**
   - Méthode: GET
   - URL: `/api/personal-events/<id_événement>/`
   - Headers: `Authorization: Bearer <token_employé>` (ou client)
   - Vérifier que les détails de l'événement sont correctement retournés

4. **Tenter d'accéder à un événement personnel d'un autre utilisateur**
   - Méthode: GET
   - URL: `/api/personal-events/<id_événement_client>/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Vérifier que la réponse a un statut 403 (Forbidden)

### 4. Modification des Événements Personnels

1. **Modifier un événement personnel**
   - Méthode: PUT
   - URL: `/api/personal-events/<id_événement>/`
   - Headers: `Authorization: Bearer <token_employé>` (ou client)
   - Corps de la requête:
     ```json
     {
       "title": "Réunion personnelle (modifié)",
       "description": "Nouvelle description",
       "start_time": "10:00",
       "end_time": "11:30"
     }
     ```
   - Vérifier que la réponse a un statut 200 et contient les détails mis à jour

2. **Mettre à jour le statut d'un événement personnel**
   - Méthode: PATCH
   - URL: `/api/personal-events/<id_événement>/`
   - Headers: `Authorization: Bearer <token_employé>` (ou client)
   - Corps de la requête:
     ```json
     {
       "status": "completed"
     }
     ```
   - Vérifier que la réponse a un statut 200 et que le statut a été mis à jour

3. **Tenter de modifier un événement personnel d'un autre utilisateur**
   - Méthode: PUT
   - URL: `/api/personal-events/<id_événement_client>/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Corps de la requête: (similaire à l'étape 1)
   - Vérifier que la réponse a un statut 403 (Forbidden)

### 5. Suppression des Événements Personnels

1. **Supprimer un événement personnel**
   - Méthode: DELETE
   - URL: `/api/personal-events/<id_événement>/`
   - Headers: `Authorization: Bearer <token_employé>` (ou client)
   - Vérifier que la réponse a un statut 200 et contient un message de succès

2. **Vérifier que l'événement a bien été supprimé**
   - Méthode: GET
   - URL: `/api/personal-events/<id_événement>/`
   - Headers: `Authorization: Bearer <token_employé>` (ou client)
   - Vérifier que la réponse a un statut 404 (Not Found)

3. **Tenter de supprimer un événement personnel d'un autre utilisateur**
   - Méthode: DELETE
   - URL: `/api/personal-events/<id_événement_client>/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Vérifier que la réponse a un statut 403 (Forbidden)

### 6. Tests de Validation des Données

1. **Tenter de créer un événement sans titre**
   - Méthode: POST
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Corps de la requête: (omettre le champ "title")
   - Vérifier que la réponse a un statut 400 (Bad Request)

2. **Tenter de créer un événement avec une date de début dans le passé**
   - Méthode: POST
   - URL: `/api/personal-events/`
   - Headers: `Authorization: Bearer <token_employé>`
   - Corps de la requête: (utiliser une date passée pour "start_date")
   - Vérifier que la réponse a un statut 400 (Bad Request)

## Vérification de la Séparation des Calendriers

1. **Vérifier que les événements d'équipe n'apparaissent pas dans le calendrier personnel**
   - Se connecter en tant qu'employé membre d'une équipe
   - Consulter les événements d'équipe via `/api/events/`
   - Consulter les événements personnels via `/api/personal-events/`
   - Vérifier que les deux listes sont distinctes

2. **Vérifier que les administrateurs ne peuvent pas voir les événements personnels des employés**
   - Se connecter en tant qu'administrateur
   - Tenter d'accéder aux événements personnels d'un employé
   - Vérifier que l'accès est refusé

## Conclusion

Ces tests manuels permettent de vérifier que la fonctionnalité de calendrier personnel fonctionne correctement et que les permissions sont bien configurées. Chaque utilisateur ne doit pouvoir voir et gérer que ses propres événements personnels, et les calendriers d'équipe et personnels doivent rester bien séparés.