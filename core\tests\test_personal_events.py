import unittest
from django.test import TestCase, Client
from django.urls import reverse
from rest_framework import status
from ..mongo_models import User, PersonalEvent
from datetime import datetime, timedelta, timezone
import json

class PersonalEventTests(TestCase):
    def setUp(self):
        # Créer un utilisateur pour les tests
        self.employee = User(
            name="Employé Test",
            email="<EMAIL>",
            role="employee"
        )
        self.employee.set_password("password123")
        self.employee.save()
        
        self.client = User(
            name="Client Test",
            email="<EMAIL>",
            role="client"
        )
        self.client.set_password("password123")
        self.client.save()
        
        self.admin = User(
            name="Admin Test",
            email="<EMAIL>",
            role="admin"
        )
        self.admin.set_password("password123")
        self.admin.save()
        
        # Créer un événement personnel pour l'employé
        tomorrow = datetime.now(timezone.utc) + timedelta(days=1)
        self.employee_event = PersonalEvent(
            title="Événement personnel de l'employé",
            description="Description de l'événement",
            start_date=tomorrow,
            end_date=tomorrow,
            start_time="09:00",
            end_time="10:00",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.employee_event.save()
        
        # Créer un événement personnel pour le client
        self.client_event = PersonalEvent(
            title="Événement personnel du client",
            description="Description de l'événement",
            start_date=tomorrow,
            end_date=tomorrow,
            start_time="14:00",
            end_time="15:00",
            created_by=str(self.client.id),
            created_by_name=self.client.name
        )
        self.client_event.save()
        
        # Client HTTP pour les tests
        self.http_client = Client()
    
    def get_auth_token(self, email, password):
        """Obtenir un token d'authentification"""
        response = self.http_client.post(
            reverse('login'),
            data=json.dumps({"email": email, "password": password}),
            content_type="application/json"
        )
        return response.json().get('token')
    
    def test_create_personal_event(self):
        """Test de création d'un événement personnel"""
        # Obtenir un token pour l'employé
        token = self.get_auth_token("<EMAIL>", "password123")
        
        # Données pour un nouvel événement
        tomorrow = (datetime.now(timezone.utc) + timedelta(days=1)).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        day_after = (datetime.now(timezone.utc) + timedelta(days=2)).strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
        data = {
            "title": "Nouvel événement personnel",
            "description": "Description du nouvel événement",
            "start_date": tomorrow,
            "end_date": day_after,
            "start_time": "08:00",
            "end_time": "09:00",
            "note": "Note importante"
        }
        
        # Créer l'événement
        response = self.http_client.post(
            reverse('personal-event-list-create'),
            data=json.dumps(data),
            content_type="application/json",
            HTTP_AUTHORIZATION=f"Bearer {token}"
        )
        
        # Vérifier que l'événement a été créé avec succès
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json().get('event').get('title'), "Nouvel événement personnel")
        self.assertEqual(response.json().get('event').get('created_by'), str(self.employee.id))
    
    def test_get_personal_events(self):
        """Test de récupération des événements personnels"""
        # Obtenir un token pour l'employé
        token = self.get_auth_token("<EMAIL>", "password123")
        
        # Récupérer les événements personnels
        response = self.http_client.get(
            reverse('personal-event-list-create'),
            HTTP_AUTHORIZATION=f"Bearer {token}"
        )
        
        # Vérifier que seuls les événements de l'employé sont retournés
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        events = response.json()
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['created_by'], str(self.employee.id))
    
    def test_update_personal_event(self):
        """Test de mise à jour d'un événement personnel"""
        # Obtenir un token pour l'employé
        token = self.get_auth_token("<EMAIL>", "password123")
        
        # Données pour la mise à jour
        data = {
            "title": "Événement mis à jour",
            "description": "Nouvelle description"
        }
        
        # Mettre à jour l'événement
        response = self.http_client.put(
            reverse('personal-event-detail', args=[str(self.employee_event.id)]),
            data=json.dumps(data),
            content_type="application/json",
            HTTP_AUTHORIZATION=f"Bearer {token}"
        )
        
        # Vérifier que l'événement a été mis à jour avec succès
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json().get('event').get('title'), "Événement mis à jour")
    
    def test_update_status(self):
        """Test de mise à jour du statut d'un événement personnel"""
        # Obtenir un token pour l'employé
        token = self.get_auth_token("<EMAIL>", "password123")
        
        # Données pour la mise à jour du statut
        data = {
            "status": "completed"
        }
        
        # Mettre à jour le statut
        response = self.http_client.patch(
            reverse('personal-event-detail', args=[str(self.employee_event.id)]),
            data=json.dumps(data),
            content_type="application/json",
            HTTP_AUTHORIZATION=f"Bearer {token}"
        )
        
        # Vérifier que le statut a été mis à jour avec succès
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json().get('status'), "completed")
    
    def test_delete_personal_event(self):
        """Test de suppression d'un événement personnel"""
        # Obtenir un token pour l'employé
        token = self.get_auth_token("<EMAIL>", "password123")
        
        # Supprimer l'événement
        response = self.http_client.delete(
            reverse('personal-event-detail', args=[str(self.employee_event.id)]),
            HTTP_AUTHORIZATION=f"Bearer {token}"
        )
        
        # Vérifier que l'événement a été supprimé avec succès
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Vérifier que l'événement n'existe plus dans la base de données
        with self.assertRaises(PersonalEvent.DoesNotExist):
            PersonalEvent.objects.get(id=str(self.employee_event.id))
    
    def test_access_control(self):
        """Test des contrôles d'accès aux événements personnels"""
        # Obtenir un token pour le client
        client_token = self.get_auth_token("<EMAIL>", "password123")
        
        # Tenter d'accéder à l'événement de l'employé
        response = self.http_client.get(
            reverse('personal-event-detail', args=[str(self.employee_event.id)]),
            HTTP_AUTHORIZATION=f"Bearer {client_token}"
        )
        
        # Vérifier que l'accès est refusé
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Obtenir un token pour l'admin
        admin_token = self.get_auth_token("<EMAIL>", "password123")
        
        # Tenter d'accéder à l'événement du client
        response = self.http_client.get(
            reverse('personal-event-detail', args=[str(self.client_event.id)]),
            HTTP_AUTHORIZATION=f"Bearer {admin_token}"
        )
        
        # Vérifier que l'accès est refusé
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

if __name__ == '__main__':
    unittest.main()