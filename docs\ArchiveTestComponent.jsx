import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const ArchiveTestComponent = () => {
  const { apiCall } = useAuth();
  const [archivedEvents, setArchivedEvents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [rawResponse, setRawResponse] = useState(null);

  const testArchivedAPI = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 Test de l\'API des événements archivés...');
      
      // Test direct de l'API
      const response = await fetch('/api/personal-events/archived/list/', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('📡 Statut de la réponse:', response.status);
      
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('📥 Données brutes reçues:', data);
      
      setRawResponse(data);
      
      if (data && data.archived_events) {
        setArchivedEvents(data.archived_events);
        console.log(`✅ ${data.archived_events.length} événement(s) archivé(s) trouvé(s)`);
      } else {
        console.log('⚠️ Structure de données inattendue');
        setArchivedEvents([]);
      }
      
    } catch (error) {
      console.error('❌ Erreur:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testArchivedAPI();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🧪 Test des Événements Archivés</h1>
      
      <div className="space-y-6">
        {/* Bouton de test */}
        <div>
          <button
            onClick={testArchivedAPI}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '🔄 Test en cours...' : '🔄 Tester l\'API'}
          </button>
        </div>

        {/* Erreur */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <strong>Erreur:</strong> {error}
          </div>
        )}

        {/* Réponse brute */}
        {rawResponse && (
          <div className="bg-gray-100 p-4 rounded">
            <h3 className="font-bold mb-2">📄 Réponse brute de l'API:</h3>
            <pre className="text-sm overflow-x-auto">
              {JSON.stringify(rawResponse, null, 2)}
            </pre>
          </div>
        )}

        {/* Résultats */}
        <div className="bg-white border rounded-lg shadow">
          <div className="px-4 py-3 border-b bg-gray-50">
            <h3 className="font-bold">📊 Résultats du test</h3>
          </div>
          
          <div className="p-4">
            {loading ? (
              <div className="text-center py-4">
                <div className="text-lg">🔄 Chargement...</div>
              </div>
            ) : archivedEvents.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                ❌ Aucun événement archivé trouvé
              </div>
            ) : (
              <div>
                <div className="mb-4 text-green-600 font-medium">
                  ✅ {archivedEvents.length} événement(s) archivé(s) trouvé(s)
                </div>
                
                <div className="space-y-3">
                  {archivedEvents.map((event, index) => (
                    <div key={event.id} className="border rounded p-3 bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-800 line-through">
                            {event.title}
                          </h4>
                          {event.description && (
                            <p className="text-sm text-gray-600 line-through">
                              {event.description}
                            </p>
                          )}
                          <div className="text-xs text-gray-500 mt-1">
                            📅 {new Date(event.start_date).toLocaleDateString('fr-FR')}
                            {event.start_time && ` à ${event.start_time}`}
                          </div>
                          <div className="text-xs text-gray-500">
                            🗂️ Archivé le: {new Date(event.archived_at).toLocaleString('fr-FR')}
                          </div>
                        </div>
                        
                        <div className="flex flex-col items-end text-xs">
                          <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded">
                            {event.status}
                          </span>
                          <div className="mt-1 text-gray-500">
                            ID: {event.id.slice(-8)}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Informations de debug */}
        <div className="bg-blue-50 p-4 rounded">
          <h3 className="font-bold mb-2">🔍 Informations de debug</h3>
          <div className="text-sm space-y-1">
            <div><strong>URL testée:</strong> /api/personal-events/archived/list/</div>
            <div><strong>Token présent:</strong> {localStorage.getItem('access_token') ? '✅ Oui' : '❌ Non'}</div>
            <div><strong>Nombre d'événements:</strong> {archivedEvents.length}</div>
            <div><strong>Statut du loading:</strong> {loading ? 'En cours' : 'Terminé'}</div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 p-4 rounded">
          <h3 className="font-bold mb-2">📝 Instructions</h3>
          <ol className="text-sm space-y-1 list-decimal list-inside">
            <li>Ouvrez la console du navigateur (F12)</li>
            <li>Cliquez sur "Tester l'API"</li>
            <li>Vérifiez les logs dans la console</li>
            <li>Comparez avec les données affichées ici</li>
            <li>Si les données apparaissent ici mais pas dans votre composant principal, le problème est dans la logique de votre composant</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default ArchiveTestComponent;
