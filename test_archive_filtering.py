#!/usr/bin/env python
"""
Script de test pour vérifier le filtrage des événements archivés
"""

import requests
import json
from datetime import datetime, timedelta

def test_archive_filtering():
    """Test complet du filtrage des événements archivés"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test du filtrage des événements archivés ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Créer 3 événements de test
    print("\n2. Création de 3 événements de test...")
    tomorrow = datetime.now() + timedelta(days=1)
    
    events_to_create = [
        {
            "title": "Événement Normal 1",
            "start_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
            "end_date": tomorrow.strftime("%Y-%m-%dT00:00:00Z"),
            "start_time": "09:00",
            "end_time": "10:00"
        },
        {
            "title": "Événement Normal 2",
            "start_date": (tomorrow + timedelta(days=1)).strftime("%Y-%m-%dT00:00:00Z"),
            "end_date": (tomorrow + timedelta(days=1)).strftime("%Y-%m-%dT00:00:00Z"),
            "start_time": "14:00",
            "end_time": "15:00"
        },
        {
            "title": "Événement à Archiver",
            "start_date": (tomorrow + timedelta(days=2)).strftime("%Y-%m-%dT00:00:00Z"),
            "end_date": (tomorrow + timedelta(days=2)).strftime("%Y-%m-%dT00:00:00Z"),
            "start_time": "16:00",
            "end_time": "17:00"
        }
    ]
    
    created_events = []
    for i, event_data in enumerate(events_to_create):
        response = requests.post(
            f"{base_url}/personal-events/",
            json=event_data,
            headers=headers
        )
        
        if response.status_code == 201:
            event_id = response.json()['event']['id']
            created_events.append({
                'id': event_id,
                'title': event_data['title']
            })
            print(f"   ✅ Créé: {event_data['title']} (ID: {event_id})")
        else:
            print(f"   ❌ Échec création: {response.json()}")
    
    # 3. Vérifier la liste initiale (tous non-archivés)
    print("\n3. Vérification de la liste initiale...")
    initial_list = requests.get(f"{base_url}/personal-events/", headers=headers)
    
    if initial_list.status_code == 200:
        initial_events = initial_list.json()
        print(f"✅ Liste initiale: {len(initial_events)} événement(s)")
        for event in initial_events:
            print(f"   - {event['title']} (statut: {event['status']})")
    else:
        print(f"❌ Échec récupération liste: {initial_list.json()}")
        return
    
    # 4. Archiver le 3ème événement
    print("\n4. Archivage du 3ème événement...")
    if len(created_events) >= 3:
        archive_id = created_events[2]['id']
        archive_response = requests.put(
            f"{base_url}/personal-events/{archive_id}/archive/",
            json={},
            headers=headers
        )
        
        if archive_response.status_code == 200:
            print(f"✅ Événement archivé: {created_events[2]['title']}")
        else:
            print(f"❌ Échec archivage: {archive_response.json()}")
            return
    
    # 5. Vérifier la liste principale (doit exclure l'archivé)
    print("\n5. Vérification de la liste principale après archivage...")
    main_list = requests.get(f"{base_url}/personal-events/", headers=headers)
    
    if main_list.status_code == 200:
        main_events = main_list.json()
        print(f"✅ Liste principale: {len(main_events)} événement(s)")
        
        archived_in_main = [e for e in main_events if e['status'] == 'archived']
        if len(archived_in_main) == 0:
            print("🎯 PARFAIT: Aucun événement archivé dans la liste principale !")
        else:
            print(f"❌ PROBLÈME: {len(archived_in_main)} événement(s) archivé(s) encore visible(s)")
        
        for event in main_events:
            print(f"   - {event['title']} (statut: {event['status']})")
    else:
        print(f"❌ Échec récupération liste principale: {main_list.json()}")
    
    # 6. Vérifier la liste des archivés
    print("\n6. Vérification de la liste des événements archivés...")
    archived_list = requests.get(f"{base_url}/personal-events/archived/list/", headers=headers)
    
    if archived_list.status_code == 200:
        archived_data = archived_list.json()
        archived_events = archived_data.get('archived_events', [])
        print(f"✅ Liste archivée: {len(archived_events)} événement(s)")
        print(f"   Message: {archived_data.get('message', 'N/A')}")
        
        if len(archived_events) > 0:
            print("🎯 PARFAIT: Les événements archivés sont dans la liste dédiée !")
            for event in archived_events:
                print(f"   - {event['title']} (archivé le: {event['archived_at']})")
        else:
            print("❌ PROBLÈME: Aucun événement dans la liste archivée")
    else:
        print(f"❌ Échec récupération liste archivée: {archived_list.json()}")
    
    # 7. Test de désarchivage
    print("\n7. Test de désarchivage...")
    if len(created_events) >= 3:
        unarchive_id = created_events[2]['id']
        unarchive_response = requests.put(
            f"{base_url}/personal-events/{unarchive_id}/unarchive/",
            json={},
            headers=headers
        )
        
        if unarchive_response.status_code == 200:
            print(f"✅ Événement désarchivé: {created_events[2]['title']}")
        else:
            print(f"❌ Échec désarchivage: {unarchive_response.json()}")
    
    # 8. Vérification finale
    print("\n8. Vérification finale après désarchivage...")
    final_main = requests.get(f"{base_url}/personal-events/", headers=headers)
    final_archived = requests.get(f"{base_url}/personal-events/archived/list/", headers=headers)
    
    if final_main.status_code == 200 and final_archived.status_code == 200:
        main_count = len(final_main.json())
        archived_count = len(final_archived.json().get('archived_events', []))
        
        print(f"✅ Liste principale finale: {main_count} événement(s)")
        print(f"✅ Liste archivée finale: {archived_count} événement(s)")
        
        if main_count == 3 and archived_count == 0:
            print("🎯 PARFAIT: Tous les événements sont revenus dans la liste principale !")
        else:
            print("❌ Répartition inattendue des événements")
    
    print("\n" + "="*70)
    print("🎯 TEST DE FILTRAGE TERMINÉ !")
    print("="*70)
    print("📋 Résumé des fonctionnalités testées:")
    print("   - ✅ Création d'événements")
    print("   - ✅ Liste principale (sans archivés)")
    print("   - ✅ Archivage d'événements")
    print("   - ✅ Liste dédiée aux archivés")
    print("   - ✅ Désarchivage d'événements")
    print("   - ✅ Filtrage automatique")

if __name__ == "__main__":
    test_archive_filtering()
