"""
Django settings for backend project.
"""
from pathlib import Path
import os
from mongoengine import connect  # 🔗 Connexion à MongoDB avec MongoEngine
from dotenv import load_dotenv  # 🔐 Sécurisation des variables d'environnement

# Charger les variables d'environnement
load_dotenv()


# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_DEBUG = True
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587  # Port TLS pour Gmail
EMAIL_USE_TLS = True  # Utiliser TLS pour une connexion sécurisée
EMAIL_USE_SSL = False  # Ne pas utiliser SSL
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', '')  # Mot de passe d'application Gmail
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

# Logging configuration pour déboguer les emails
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.core.mail': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}

# URL du frontend pour les liens dans les emails
FRONTEND_URL = os.getenv('FRONTEND_URL', 'http://localhost:5173')

# 📌 Définir le chemin du projet
BASE_DIR = Path(__file__).resolve().parent.parent





# 🔐 Sécurité : Déplacer SECRET_KEY et DEBUG vers .env
SECRET_KEY = os.getenv("SECRET_KEY", "changeme-in-env-file")
DEBUG = os.getenv("DEBUG", "True") == "True"

# 🔐 Autoriser les domaines en production
ALLOWED_HOSTS = os.getenv("ALLOWED_HOSTS", "localhost 127.0.0.1").split()




# ✅ Applications installées
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # 🛠️ Apps ajoutées
    'rest_framework',  # API Django Rest Framework
    'rest_framework_simplejwt',  # Authentification JWT
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',     # Gérer les requêtes du frontend React
    'core',            # App principale pour gérer MongoDB
]

# ✅ Middleware
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS middleware (avant tous les autres)
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# ✅ CORS pour React (remplace "*" par le domaine en production)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",  # Your React frontend URL
]

CORS_ALLOW_CREDENTIALS = True

# 📌 URLs et Templates
ROOT_URLCONF = 'backend.urls'

# Assurer que les URLs se terminent par un slash pour éviter les redirections avec perte de données POST
APPEND_SLASH = True

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'backend.wsgi.application'

# 🚨 **Désactiver SQL DATABASES car Django ne supporte pas MongoDB nativement**
DATABASES = {}


# 🔗 Connexion à MongoDB avec MongoEngine
MONGO_DB_NAME = 'NotoraDb'
MONGO_URI = 'mongodb://localhost:27017/'

connect(db=MONGO_DB_NAME, host=MONGO_URI)



# 📌 Validation des mots de passe
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# 📌 Internationalisation
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# 📌 Fichiers statiques
STATIC_URL = 'static/'

# 📌 Clé primaire par défaut
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]


# 🛠️ Configuration de Django Rest Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'core.auth.MongoJWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'EXCEPTION_HANDLER': 'rest_framework.views.exception_handler'
}

# 🛠️ Configuration de SimpleJWT
from datetime import timedelta

SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_ID_FIELD': 'id',
    'USER_ID_CLAIM': 'user_id',
    'AUTH_TOKEN_CLASSES': ('rest_framework_simplejwt.tokens.AccessToken',),
    'TOKEN_TYPE_CLAIM': 'token_type',
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'BLACKLIST_AFTER_ROTATION': True,
    'ROTATE_REFRESH_TOKENS': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'VERIFYING_KEY': None,
}

# Add these CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # For development only
CORS_ALLOW_CREDENTIALS = True  # Allow credentials
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
    'access-control-allow-origin',
    'access-control-allow-headers'
]

CORS_EXPOSE_HEADERS = ['content-type', 'x-requested-with']