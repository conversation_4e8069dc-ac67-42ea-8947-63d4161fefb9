#!/usr/bin/env python3
"""
Test rapide de l'endpoint BI
"""
import requests
import json

def test_bi_endpoint():
    print("🧪 Test rapide de l'endpoint BI")

    # 1. Se connecter - essayons différents mots de passe
    login_attempts = [
        {"email": "<EMAIL>", "password": "Syrine@2024"},
        {"email": "<EMAIL>", "password": "admin123"},
        {"email": "<EMAIL>", "password": "password123"}
    ]

    token = None

    # Essayer différents mots de passe
    for login_data in login_attempts:
        try:
            print(f"🔐 Tentative de connexion avec: {login_data['email']}")
            response = requests.post("http://localhost:8000/api/login/", json=login_data)

            if response.status_code == 200:
                token = response.json().get('access_token') or response.json().get('access')
                print("✅ Connexion réussie!")
                break
            else:
                print(f"❌ Échec: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Erreur de connexion: {str(e)}")

    if not token:
        print("❌ Impossible de se connecter avec tous les mots de passe testés")
        return

    # Test endpoint BI
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        bi_response = requests.get("http://localhost:8000/api/bi/super-admin/dashboard/", headers=headers)

        print(f"\n📊 Test endpoint BI:")
        print(f"Status: {bi_response.status_code}")

        if bi_response.status_code == 200:
            data = bi_response.json()
            print("✅ Endpoint BI fonctionne!")
            print(f"Total utilisateurs: {data.get('metric_cards', [{}])[0].get('value', 'N/A')}")
            print(f"Timestamp: {data.get('timestamp', 'N/A')}")
        else:
            print(f"❌ Erreur endpoint BI: {bi_response.text}")

    except Exception as e:
        print(f"❌ Exception: {str(e)}")

if __name__ == "__main__":
    test_bi_endpoint()
