from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import Team
from ..models.team_task_model import TeamTask
import logging

logger = logging.getLogger(__name__)

class TeamTaskUnarchiveView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, task_id):
        """Désarchiver une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user
            
            # Vérifier si l'admin a le droit de gérer cette tâche
            # ou si l'admin est celui qui a créé la tâche
            if not task.can_manage_task(user) and task.created_by != str(user.id):
                return Response({
                    "error": "Vous n'êtes pas autorisé à désarchiver cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."
                }, status=403)
            
            # Vérifier que la tâche est bien archivée
            if task.status != 'archived':
                return Response({
                    "error": "Cette tâche n'est pas archivée et ne peut donc pas être désarchivée."
                }, status=400)
            
            # Désarchiver la tâche (remettre à l'état "a_faire")
            task.status = 'a_faire'
            task.save()
            
            return Response({
                "message": "Tâche d'équipe désarchivée avec succès",
                "status": task.status
            })
            
        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskUnarchiveView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors du désarchivage de la tâche d'équipe"}, status=500)
