from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.personal_task_model import PersonalTask
import logging

logger = logging.getLogger(__name__)

class PersonalTaskUnarchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, task_id):
        """Désarchiver une tâche personnelle"""
        try:
            user = request.user
            
            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)
            
            # Vérifier que l'utilisateur est autorisé à désarchiver cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à désarchiver cette tâche personnelle"}, status=403)
            
            # Vérifier que la tâche est bien archivée
            if task.status != 'archived':
                return Response({"error": "Cette tâche n'est pas archivée et ne peut donc pas être désarchivée"}, status=400)
            
            # Désarchiver la tâche (remettre à l'état "a_faire")
            task.status = 'a_faire'
            task.save()
            
            return Response({
                "message": "Tâche personnelle désarchivée avec succès",
                "status": task.status
            })
            
        except Exception as e:
            logger.error(f"Error in PersonalTaskUnarchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)
