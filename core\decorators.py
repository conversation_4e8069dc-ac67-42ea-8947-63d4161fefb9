from functools import wraps
from rest_framework.response import Response
from rest_framework import status

def admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        if request.user.role not in ['admin', 'super_admin']:
            return Response({
                'error': 'Unauthorized',
                'message': 'Only admin or super admin can access this resource',
                'current_role': request.user.role,
                'required_role': 'admin or super_admin'
            }, status=status.HTTP_403_FORBIDDEN)
        return view_func(self, request, *args, **kwargs)
    return _wrapped_view

def super_admin_required(view_func):
    @wraps(view_func)
    def _wrapped_view(self, request, *args, **kwargs):
        if not request.user.role == 'super_admin':
            return Response({
                'error': 'Unauthorized',
                'message': 'Only super admin can access this resource',
                'current_role': request.user.role,
                'required_role': 'super_admin'
            }, status=status.HTTP_403_FORBIDDEN)
        return view_func(self, request, *args, **kwargs)
    return _wrapped_view 

def check_permission(permission_name):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            user = request.user
            if not user.permissions.get(permission_name, False):
                return Response({
                    'error': 'Permission denied',
                    'message': f'You need {permission_name} permission to access this resource',
                    'required_permission': permission_name
                }, status=status.HTTP_403_FORBIDDEN)
            return view_func(self, request, *args, **kwargs)
        return wrapper
    return decorator