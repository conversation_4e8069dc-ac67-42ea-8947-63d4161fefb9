# Guide Complet - Tableau de Bord BI Frontend

## 📋 Vue d'ensemble

Ce guide contient toutes les informations nécessaires pour implémenter le tableau de bord BI avec :
- ✅ Mise à jour manuelle (bouton de rafraîchissement)
- ✅ Filtrage par période (1h, 24h, 7j, 30j, aujourd'hui)
- ✅ Données en temps réel pour les connexions
- ✅ Tracking précis des utilisateurs connectés
- ✅ Sources de données différenciées selon la période

## 🔗 URLs des Endpoints

### 1. **Tableau de bord principal (avec filtres)**
```
GET /api/bi/super-admin/dashboard/
```

**Paramètres de requête :**
- `period` : `today` | `1h` | `24h` | `7d` | `30d` (défaut: `today`)
- `manual_refresh` : `true` | `false` (défaut: `true`)

**Exemples d'URLs :**
```
GET /api/bi/super-admin/dashboard/                           # Aujourd'hui
GET /api/bi/super-admin/dashboard/?period=1h                # Dernière heure
GET /api/bi/super-admin/dashboard/?period=24h               # Dernières 24h
GET /api/bi/super-admin/dashboard/?period=7d                # Derniers 7 jours
GET /api/bi/super-admin/dashboard/?period=30d               # Derniers 30 jours
GET /api/bi/super-admin/dashboard/?period=today&manual_refresh=true
```

### 2. **Statistiques de connexion en temps réel**
```
GET /api/bi/realtime/login-stats/
```

### 3. **Débogage des données de connexion**
```
GET /api/bi/debug/login-data/
```

### 4. **Métriques générales**
```
GET /api/bi/metrics/
```

### 5. **Données historiques**
```
GET /api/bi/historical-data/?data_type=active_users&period=7d
```

## 🏗️ Logique Implémentée

### **1. Système de Tracking des Connexions**

#### **DailyLoginTracker (Nouveau modèle)**
- Enregistre chaque connexion utilisateur
- Distingue utilisateurs uniques vs connexions totales
- Données précises pour "aujourd'hui"

```javascript
// Données retournées pour period=today
{
  "users_logged_today": 4,        // Utilisateurs uniques connectés
  "total_logins_today": 7,        // Connexions totales (peut être > users_logged_today)
  "inactive_users_today": 25      // Non connectés aujourd'hui
}
```

#### **Différence entre les métriques :**
- `users_logged_today: 4` = 4 utilisateurs différents se sont connectés
- `total_logins_today: 7` = 7 connexions au total (certains utilisateurs se sont reconnectés)

### **2. Sources de Données par Période**

| Période | Source de données | Précision | Temps réel |
|---------|------------------|-----------|------------|
| `today` | `DailyLoginTracker` | ✅ Précise | ✅ Oui |
| `1h` | `User.last_login` | ⚠️ Approximation | ❌ Non |
| `24h` | `User.last_login` | ⚠️ Approximation | ❌ Non |
| `7d` | `User.last_login` | ⚠️ Approximation | ❌ Non |
| `30d` | `User.last_login` | ⚠️ Approximation | ❌ Non |

### **3. Mise à Jour Manuelle**

#### **Comportement :**
- `manual_refresh=true` : Pas d'actualisation automatique
- `manual_refresh=false` : Actualisation toutes les 30 secondes
- Bouton "Actualiser" requis dans l'interface

#### **Métadonnées de contrôle :**
```javascript
{
  "metadata": {
    "refresh_mode": "manual",
    "refresh_interval": null,
    "manual_refresh": true
  }
}
```

## 📊 Structure de Réponse

### **Réponse complète du tableau de bord :**

```javascript
{
  "timestamp": "2024-01-15T14:30:25.123Z",
  "is_realtime": true,

  // Cartes de métriques (haut de l'interface)
  "metric_cards": [
    {
      "title": "Nombre total d'utilisateurs",
      "value": 29,
      "trend": "+15.2%",
      "trend_period": "ce mois",
      "icon": "users",
      "color": "#3B82F6",
      "manual_refresh": true
    },
    {
      "title": "Utilisateurs actifs",
      "subtitle": "Connectés (aujourd'hui)",
      "value": 4,
      "trend": "+13.8%",
      "trend_period": "aujourd'hui",
      "icon": "user-check",
      "color": "#10B981",
      "manual_refresh": true,
      "last_updated": "2024-01-15T14:30:25.123Z",
      "period": "today",
      "data_source": "DailyLoginTracker"
    },
    {
      "title": "Utilisateurs inactifs",
      "subtitle": "Non connectés (aujourd'hui)",
      "value": 25,
      "trend": "86.2%",
      "trend_period": "aujourd'hui",
      "icon": "user-x",
      "color": "#EF4444",
      "manual_refresh": true,
      "last_updated": "2024-01-15T14:30:25.123Z",
      "period": "today",
      "data_source": "DailyLoginTracker"
    }
  ],

  // Graphiques (bas de l'interface)
  "charts": {
    "active_vs_inactive": {
      "type": "doughnut",
      "title": "Connexions - Aujourd'hui",
      "subtitle": "Utilisateurs connectés vs non connectés (aujourd'hui)",
      "data": [
        {
          "name": "Connectés (aujourd'hui)",
          "value": 4,
          "color": "#10B981"
        },
        {
          "name": "Non connectés (aujourd'hui)",
          "value": 25,
          "color": "#EF4444"
        }
      ],
      "legend": [
        {"label": "Connectés (aujourd'hui)", "color": "#10B981"},
        {"label": "Non connectés (aujourd'hui)", "color": "#EF4444"}
      ],
      "period": "today",
      "period_name": "Aujourd'hui",
      "manual_refresh": true,
      "last_updated": "2024-01-15T14:30:25.123Z"
    },
    "role_distribution": {
      "type": "bar",
      "title": "Distribution des Utilisateurs par Rôle",
      "data": [
        {"name": "Super Admin", "value": 3, "color": "#8B5CF6"},
        {"name": "Admin", "value": 9, "color": "#3B82F6"},
        {"name": "Employés", "value": 14, "color": "#10B981"},
        {"name": "Clients", "value": 12, "color": "#F59E0B"}
      ],
      "max_value": 14
    }
  },

  // Métadonnées et configuration
  "metadata": {
    "last_updated": "2024-01-15T14:30:25.123Z",
    "data_source": "DailyLoginTracker",
    "refresh_mode": "manual",
    "refresh_interval": null,
    "dashboard_title": "Tableau de Bord Super Admin - Aujourd'hui",
    "dashboard_subtitle": "Connexions et analyses (aujourd'hui)",

    // Période actuelle
    "current_period": {
      "period": "today",
      "period_name": "Aujourd'hui",
      "period_start": "2024-01-15T00:00:00.000Z",
      "manual_refresh": true
    },

    // Périodes disponibles pour les boutons
    "available_periods": [
      {"value": "today", "label": "Aujourd'hui"},
      {"value": "1h", "label": "Dernière heure"},
      {"value": "24h", "label": "Dernières 24h"},
      {"value": "7d", "label": "Derniers 7 jours"},
      {"value": "30d", "label": "Derniers 30 jours"}
    ],

    // Fonctionnalités disponibles
    "features": {
      "period_filtering": true,
      "manual_refresh": true,
      "real_time_data": true
    },

    // Fraîcheur des données
    "data_freshness": {
      "login_data": "real_time",
      "user_counts": "real_time",
      "activity_stats": "real_time"
    }
  }
}
```

## 🎨 Interface Utilisateur Recommandée

### **1. Layout Principal**

```
┌─────────────────────────────────────────────────────────────────┐
│  [Aujourd'hui] [1h] [24h] [7j] [30j]           [🔄 Actualiser]  │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │📊 Total: 29│  │✅ Actifs: 4 │  │❌ Inactifs:25│             │
│  │+15.2% mois  │  │+13.8% auj.  │  │86.2% auj.   │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
│                                                                 │
│  ┌─────────────────────┐  ┌─────────────────────┐             │
│  │   Graphique 1       │  │   Graphique 2       │             │
│  │ Actifs/Inactifs     │  │ Distribution Rôles  │             │
│  │   (période)         │  │                     │             │
│  └─────────────────────┘  └─────────────────────┘             │
│                                                                 │
│  Dernière mise à jour: 15/01/2024 à 14:30:25                  │
└─────────────────────────────────────────────────────────────────┘
```

### **2. Boutons de Filtrage**

```javascript
const PeriodButtons = () => {
  const [activePeriod, setActivePeriod] = useState('today');

  const periods = [
    { value: 'today', label: 'Aujourd\'hui', color: '#10B981' },
    { value: '1h', label: '1h', color: '#3B82F6' },
    { value: '24h', label: '24h', color: '#8B5CF6' },
    { value: '7d', label: '7j', color: '#F59E0B' },
    { value: '30d', label: '30j', color: '#EF4444' }
  ];

  return (
    <div className="period-buttons">
      {periods.map(period => (
        <button
          key={period.value}
          className={`period-btn ${activePeriod === period.value ? 'active' : ''}`}
          onClick={() => {
            setActivePeriod(period.value);
            fetchDashboardData(period.value);
          }}
          style={{
            backgroundColor: activePeriod === period.value ? period.color : 'transparent',
            borderColor: period.color
          }}
        >
          {period.label}
        </button>
      ))}
    </div>
  );
};
```

### **3. Bouton de Rafraîchissement**

```javascript
const RefreshButton = ({ onRefresh, isLoading }) => {
  return (
    <button
      className="refresh-btn"
      onClick={onRefresh}
      disabled={isLoading}
    >
      <span className={`refresh-icon ${isLoading ? 'spinning' : ''}`}>🔄</span>
      Actualiser
    </button>
  );
};
```

## 💻 Code JavaScript Recommandé

### **1. Service API**

```javascript
class BiDashboardService {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async getDashboardData(period = 'today', manualRefresh = true) {
    const url = `${this.baseURL}/api/bi/super-admin/dashboard/?period=${period}&manual_refresh=${manualRefresh}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getRealTimeStats() {
    const url = `${this.baseURL}/api/bi/realtime/login-stats/`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }

  async getDebugData() {
    const url = `${this.baseURL}/api/bi/debug/login-data/`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }
}
```

### **2. Hook React Principal**

```javascript
import { useState, useEffect, useCallback } from 'react';

const useBiDashboard = (token) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [currentPeriod, setCurrentPeriod] = useState('today');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const service = new BiDashboardService('http://localhost:8000', token);

  const fetchDashboardData = useCallback(async (period = currentPeriod) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await service.getDashboardData(period, true);
      setDashboardData(data);
      setCurrentPeriod(period);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err.message);
      console.error('Erreur lors de la récupération des données:', err);
    } finally {
      setIsLoading(false);
    }
  }, [currentPeriod, service]);

  const refreshData = useCallback(() => {
    fetchDashboardData(currentPeriod);
  }, [fetchDashboardData, currentPeriod]);

  const changePeriod = useCallback((newPeriod) => {
    fetchDashboardData(newPeriod);
  }, [fetchDashboardData]);

  // Chargement initial
  useEffect(() => {
    fetchDashboardData();
  }, []);

  return {
    dashboardData,
    currentPeriod,
    isLoading,
    error,
    lastUpdated,
    refreshData,
    changePeriod,
    availablePeriods: [
      { value: 'today', label: 'Aujourd\'hui' },
      { value: '1h', label: 'Dernière heure' },
      { value: '24h', label: 'Dernières 24h' },
      { value: '7d', label: 'Derniers 7 jours' },
      { value: '30d', label: 'Derniers 30 jours' }
    ]
  };
};
```

### **3. Composant Principal**

```javascript
const BiDashboard = ({ token }) => {
  const {
    dashboardData,
    currentPeriod,
    isLoading,
    error,
    lastUpdated,
    refreshData,
    changePeriod,
    availablePeriods
  } = useBiDashboard(token);

  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }

  if (!dashboardData) {
    return <div className="loading">Chargement...</div>;
  }

  return (
    <div className="bi-dashboard">
      {/* Header avec filtres et bouton refresh */}
      <div className="dashboard-header">
        <div className="period-filters">
          {availablePeriods.map(period => (
            <button
              key={period.value}
              className={`period-btn ${currentPeriod === period.value ? 'active' : ''}`}
              onClick={() => changePeriod(period.value)}
              disabled={isLoading}
            >
              {period.label}
            </button>
          ))}
        </div>

        <button
          className="refresh-btn"
          onClick={refreshData}
          disabled={isLoading}
        >
          <span className={`refresh-icon ${isLoading ? 'spinning' : ''}`}>🔄</span>
          Actualiser
        </button>
      </div>

      {/* Titre dynamique */}
      <h1>{dashboardData.metadata.dashboard_title}</h1>
      <p>{dashboardData.metadata.dashboard_subtitle}</p>

      {/* Cartes de métriques */}
      <div className="metric-cards">
        {dashboardData.metric_cards.map((card, index) => (
          <MetricCard key={index} {...card} />
        ))}
      </div>

      {/* Graphiques */}
      <div className="charts-container">
        <ChartComponent
          data={dashboardData.charts.active_vs_inactive}
          type="doughnut"
        />
        <ChartComponent
          data={dashboardData.charts.role_distribution}
          type="bar"
        />
      </div>

      {/* Footer avec dernière mise à jour */}
      <div className="dashboard-footer">
        <span>
          Dernière mise à jour: {lastUpdated?.toLocaleString()}
        </span>
        <span>
          Source: {dashboardData.metadata.data_source}
        </span>
      </div>
    </div>
  );
};
```

## 🧪 Tests Recommandés

### **1. Test de tous les filtres de période**

```javascript
// Test chaque période
const periods = ['today', '1h', '24h', '7d', '30d'];

for (const period of periods) {
  console.log(`Testing period: ${period}`);
  const data = await service.getDashboardData(period);
  console.log(`✅ ${period}:`, data.metadata.current_period);
}
```

### **2. Test du bouton de rafraîchissement**

```javascript
// Test refresh manuel
const beforeRefresh = await service.getDashboardData('today');
await new Promise(resolve => setTimeout(resolve, 1000)); // Attendre 1s
const afterRefresh = await service.getDashboardData('today');

console.log('Timestamps différents:',
  beforeRefresh.timestamp !== afterRefresh.timestamp
);
```

### **3. Test de cohérence des données**

```javascript
// Vérifier la cohérence
const debugData = await service.getDebugData();
console.log('Cohérence des données:', debugData.data_comparison);
```

## 🚀 Déploiement

### **1. Variables d'environnement**

```javascript
// .env
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_BI_REFRESH_INTERVAL=30000
REACT_APP_DEFAULT_PERIOD=today
```

### **2. Configuration de production**

```javascript
const config = {
  apiBaseUrl: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000',
  defaultPeriod: process.env.REACT_APP_DEFAULT_PERIOD || 'today',
  refreshInterval: parseInt(process.env.REACT_APP_BI_REFRESH_INTERVAL) || 30000
};
```

## 📝 Notes Importantes

### **1. Gestion des erreurs**
- Toujours vérifier le statut HTTP
- Afficher des messages d'erreur utilisateur-friendly
- Implémenter un fallback en cas d'échec

### **2. Performance**
- Éviter les appels API trop fréquents
- Utiliser le cache pour les données statiques
- Implémenter un loading state

### **3. Sécurité**
- Toujours inclure le token d'authentification
- Vérifier les permissions côté frontend
- Valider les paramètres de requête

### **4. UX/UI**
- Indiquer clairement la période active
- Afficher l'heure de dernière mise à jour
- Utiliser des couleurs cohérentes pour les métriques

## 🔍 Débogage et Troubleshooting

### **1. Endpoint de débogage**

```javascript
// Vérifier la cohérence des données
const debugResponse = await fetch('/api/bi/debug/login-data/', {
  headers: { 'Authorization': `Bearer ${token}` }
});

const debugData = await debugResponse.json();
console.log('Debug info:', debugData);
```

**Réponse de débogage :**
```javascript
{
  "data_comparison": {
    "daily_tracker": {
      "users_logged_today": 4,
      "total_logins_today": 7
    },
    "last_login_based": {
      "users_logged_24h": 4,
      "users_logged_today": 4
    }
  },
  "calculations": {
    "total_users": 29,
    "inactive_users_today_tracker": 25,
    "inactive_users_today_lastlogin": 25
  },
  "recommendations": {
    "use_tracker_for_today": "Utiliser DailyLoginTracker pour les connexions d'aujourd'hui",
    "difference_explanation": "La différence peut venir du fait que last_login est mis à jour à chaque connexion, tandis que le tracker compte les utilisateurs uniques par jour"
  }
}
```

### **2. Problèmes courants et solutions**

| Problème | Cause | Solution |
|----------|-------|----------|
| Données incohérentes | Cache frontend | Forcer le refresh avec `?t=${Date.now()}` |
| Erreur 401 | Token expiré | Renouveler le token d'authentification |
| Erreur 403 | Permissions insuffisantes | Vérifier le rôle super_admin |
| Données vides | Aucune connexion | Normal si aucun utilisateur connecté |
| Graphiques ne s'affichent pas | Données malformées | Vérifier la structure des données |

---

**Version:** 1.0
**Date:** 15 Janvier 2024
**Auteur:** Backend API Team
**Contact:** Pour toute question, consulter l'endpoint de débogage `/api/bi/debug/login-data/`

## 📞 Support

- **Endpoint de test:** `GET /api/bi/debug/login-data/`
- **Documentation API:** Voir ce guide
- **Logs backend:** Vérifier les logs Django pour les erreurs
- **Validation des données:** Utiliser l'endpoint de débogage pour vérifier la cohérence
