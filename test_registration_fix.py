"""
Script de test pour vérifier que le problème d'inscription/connexion est résolu
"""

import requests


def test_registration_and_login():
    """Test complet d'inscription et de connexion avec validation du mot de passe"""
    base_url = "http://localhost:8000/api"

    print("=== Test d'inscription et de connexion (Auto-inscription) ===\n")

    # Test 1: Mot de passe trop faible
    print("1. Test avec mot de passe trop faible...")
    weak_password_user = {
        "name": "Test User Weak",
        "email": "<EMAIL>",
        "password": "123",  # Mot de passe trop faible
        "role": "client"
    }

    register_response = requests.post(
        f"{base_url}/register/",
        json=weak_password_user,
        headers={"Content-Type": "application/json"}
    )

    print(f"Statut: {register_response.status_code}")
    print(f"Réponse: {register_response.json()}")

    if register_response.status_code == 400:
        print("✅ Validation du mot de passe faible fonctionne")
    else:
        print("❌ La validation du mot de passe faible a échoué")

    print("\n" + "-"*50 + "\n")

    # Test 2: Inscription avec mot de passe fort
    print("2. Test d'inscription avec mot de passe fort...")
    strong_password_user = {
        "name": "Test User Strong",
        "email": "<EMAIL>",
        "password": "StrongPassword123!",
        "role": "client"
    }

    register_response = requests.post(
        f"{base_url}/register/",
        json=strong_password_user,
        headers={"Content-Type": "application/json"}
    )

    print(f"Statut: {register_response.status_code}")
    print(f"Réponse: {register_response.json()}")

    if register_response.status_code == 201:
        print("✅ Inscription avec mot de passe fort réussie")

        # Test de connexion immédiate
        print("\n3. Test de connexion avec le mot de passe fourni...")
        login_data = {
            "email": strong_password_user["email"],
            "password": strong_password_user["password"]
        }

        login_response = requests.post(
            f"{base_url}/login/",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )

        print(f"Statut: {login_response.status_code}")
        print(f"Réponse: {login_response.json()}")

        if login_response.status_code == 200:
            print("✅ Connexion immédiate réussie")
            token_data = login_response.json()
            access_token = token_data.get('access')

            # Test d'accès à une route protégée
            print("\n4. Test d'accès à une route protégée...")
            protected_response = requests.get(
                f"{base_url}/protected/",
                headers={"Authorization": f"Bearer {access_token}"}
            )

            print(f"Statut: {protected_response.status_code}")
            print(f"Réponse: {protected_response.json()}")

            if protected_response.status_code == 200:
                print("✅ Accès à la route protégée réussi")
            else:
                print("❌ Échec d'accès à la route protégée")
        else:
            print("❌ Échec de la connexion")
    else:
        print("❌ Échec de l'inscription avec mot de passe fort")

    print("\n" + "="*50 + "\n")


def test_super_admin_user_creation():
    """Test de création d'utilisateur par le super admin"""
    base_url = "http://localhost:8000/api"

    print("=== Test de création d'utilisateur par le super admin ===\n")

    # D'abord, se connecter en tant que super admin
    print("1. Connexion en tant que super admin...")
    super_admin_login = {
        "email": "<EMAIL>",  # Email du super admin
        "password": "SuperAdmin123!"  # Mot de passe du super admin
    }

    login_response = requests.post(
        f"{base_url}/login/",
        json=super_admin_login,
        headers={"Content-Type": "application/json"}
    )

    print(f"Statut: {login_response.status_code}")

    if login_response.status_code != 200:
        print("❌ Échec de la connexion du super admin")
        print(f"Réponse: {login_response.json()}")
        return

    print("✅ Connexion du super admin réussie")
    token_data = login_response.json()
    access_token = token_data.get('access')

    print("\n" + "-"*50 + "\n")

    # Créer un utilisateur via l'endpoint super admin
    print("2. Création d'un utilisateur par le super admin...")
    new_user_data = {
        "name": "Admin Created User",
        "email": "<EMAIL>",
        "role": "employee"
    }

    create_response = requests.post(
        f"{base_url}/users/create/",
        json=new_user_data,
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {access_token}"
        }
    )

    print(f"Statut: {create_response.status_code}")
    print(f"Réponse: {create_response.json()}")

    if create_response.status_code == 201:
        print("✅ Création d'utilisateur par le super admin réussie")
        print("📧 Vérifiez les logs du serveur pour voir l'email avec le mot de passe temporaire")

        # Vérifier que l'utilisateur a été créé avec temp_password_required=True
        user_data = create_response.json()
        print(f"Utilisateur créé: {user_data.get('user', {}).get('email')}")
    else:
        print("❌ Échec de la création d'utilisateur par le super admin")

    print("\n" + "="*50 + "\n")


def test_existing_user_login():
    """Test de connexion avec l'utilisateur existant (<EMAIL>)"""
    base_url = "http://localhost:8000/api"

    print("=== Test de connexion avec l'utilisateur existant ===\n")

    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }

    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )

    print(f"Statut: {login_response.status_code}")
    print(f"Réponse: {login_response.json()}")

    if login_response.status_code == 200:
        print("✅ Connexion de l'utilisateur existant réussie")
    else:
        print("❌ Échec de la connexion de l'utilisateur existant")

    print("\n" + "="*50 + "\n")


if __name__ == "__main__":
    print("Démarrage des tests...\n")

    # Test de l'utilisateur existant d'abord
    test_existing_user_login()

    # Test de nouvelle inscription avec mot de passe personnel (auto-inscription)
    test_registration_and_login()

    # Test de création d'utilisateur par le super admin (mot de passe temporaire)
    test_super_admin_user_creation()

    print("Tests terminés.")
