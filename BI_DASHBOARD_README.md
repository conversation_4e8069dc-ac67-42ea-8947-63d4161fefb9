# 📊 Documentation BI Dashboard - Backend Implementation

## 🎯 Vue d'ensemble

Le système BI (Business Intelligence) fournit des tableaux de bord en temps réel pour les super admins avec des métriques détaillées sur l'activité des utilisateurs et la plateforme.

## 🔗 Endpoints Disponibles

### 1. Dashboard Principal Super Admin ✅ TESTÉ
```
GET /api/bi/super-admin/dashboard/
Authorization: Bearer {token}
```

**Réponse testée avec succès :**
- Status: 200 OK
- Données en temps réel depuis la base MongoDB
- Mise à jour automatique toutes les 30 secondes

### 2. Données Historiques pour Graphiques
```
GET /api/bi/historical-data/?data_type={type}&period={period}
Authorization: Bearer {token}
```

**Paramètres disponibles :**
- `data_type`: `active_users`, `role_usage`, `active_inactive_users`, `role_distribution`
- `period`: `7d`, `30d`, `90d`, `365d`

**Types de graphiques supportés :**
- **Line Chart** : `active_users`, `role_usage`
- **Pie Chart** : `active_inactive_users`, `role_distribution`

### 3. Dashboard Personnalisé par Utilisateur
```
GET /api/bi/dashboard/
POST /api/bi/dashboard/
Authorization: Bearer {token}
```

**Fonctionnalités :**
- Création de dashboards personnalisés
- Sauvegarde des préférences de layout
- Configuration des métriques par rôle

## 📈 Métriques Calculées

### 1. **Cartes de Métriques (metric_cards)**

#### A. Nombre Total d'Utilisateurs
```python
# Calcul
total_users = User.objects.count()

# Logique de tendance
# Comparaison avec le mois précédent (simulation +100%)
```

#### B. Utilisateurs Actifs
```python
# Calcul basé sur last_login
now = datetime.now(timezone.utc)
seven_days_ago = now - timedelta(days=7)
active_users = User.objects(last_login__gte=seven_days_ago).count()

# Tendance calculée : +150.0% cette semaine
```

#### C. Utilisateurs Inactifs
```python
# Calcul
inactive_users = total_users - active_users_30d
# Où active_users_30d = utilisateurs connectés dans les 30 derniers jours
```

### 2. **Graphiques (charts)**

#### A. Graphique Circulaire - Actifs vs Inactifs
```python
# Type: "doughnut"
# Données:
{
    "active_vs_inactive": {
        "data": [
            {"name": "Actifs", "value": 7, "color": "#10B981"},
            {"name": "Inactifs", "value": 22, "color": "#EF4444"}
        ]
    }
}
```

#### B. Graphique en Barres - Distribution par Rôle
```python
# Type: "bar"
# Calcul par rôle:
super_admin_count = User.objects(role='super_admin').count()  # 2
admin_count = User.objects(role='admin').count()             # 7
employee_count = User.objects(role='employee').count()       # 15
client_count = User.objects(role='client').count()           # 5

# max_value = 15 (valeur maximale pour l'échelle)
```

### 3. **Statistiques Détaillées (detailed_stats)**

#### A. Utilisateurs par Rôle
```python
users_by_role = {
    'super_admin': User.objects(role='super_admin').count(),
    'admin': User.objects(role='admin').count(),
    'employee': User.objects(role='employee').count(),
    'client': User.objects(role='client').count()
}
```

#### B. Statistiques d'Activité
```python
# Utilisateurs actifs par période
active_users = {
    'last_24h': User.objects(last_login__gte=one_day_ago).count(),    # 4
    'last_7_days': User.objects(last_login__gte=seven_days_ago).count(), # 5
    'last_30_days': User.objects(last_login__gte=thirty_days_ago).count() # 7
}

# Taux d'activité (pourcentage)
activity_rate = {
    'last_24h': round((active_24h / total_users) * 100, 2),    # 13.79%
    'last_7_days': round((active_7d / total_users) * 100, 2),  # 17.24%
    'last_30_days': round((active_30d / total_users) * 100, 2) # 24.14%
}
```

#### C. Utilisateurs Jamais Connectés
```python
# Utilisateurs sans last_login
never_logged_in = User.objects(last_login__exists=False).count()  # 22
```

#### D. Métriques d'Engagement
```python
# Nouveaux utilisateurs (basé sur created_at)
new_users_7d = User.objects(created_at__gte=seven_days_ago).count()   # 5
new_users_30d = User.objects(created_at__gte=thirty_days_ago).count() # 15

# Utilisateurs connectés aujourd'hui
users_logged_today = User.objects(last_login__gte=today_start).count() # 4

# Taux de rétention
retention_rate = round((active_30d / total_users) * 100, 2)  # 24.14%
```

## 🔧 Implémentation Technique

### Imports Nécessaires
```python
from datetime import datetime, timezone, timedelta
from core.mongo_models import User
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
import logging
```

### Structure de la Vue
```python
class SuperAdminDashboardView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Vérification des permissions super_admin
        if request.user.role != 'super_admin':
            return Response({"error": "Accès non autorisé"}, status=403)

        # Calculs en temps réel depuis MongoDB
        now = datetime.now(timezone.utc)

        # Périodes de temps
        one_day_ago = now - timedelta(days=1)
        seven_days_ago = now - timedelta(days=7)
        thirty_days_ago = now - timedelta(days=30)

        # Calculs des métriques
        total_users = User.objects.count()
        active_users_7d = User.objects(last_login__gte=seven_days_ago).count()

        # Formatage et retour des données
        return Response(formatted_data)
```

### Logique de Calcul des Tendances
```python
def calculate_trends(self, current_value, previous_value):
    """Calcule les tendances en pourcentage"""
    if previous_value == 0:
        return "+100%" if current_value > 0 else "0%"

    change = ((current_value - previous_value) / previous_value) * 100
    sign = "+" if change >= 0 else ""
    return f"{sign}{change:.1f}%"
```

## 🎨 Configuration Frontend

### Couleurs Utilisées
```css
/* Cartes de métriques */
--primary-blue: #3B82F6    /* Total utilisateurs */
--success-green: #10B981   /* Utilisateurs actifs */
--danger-red: #EF4444      /* Utilisateurs inactifs */

/* Graphiques */
--purple: #8B5CF6          /* Super Admin */
--blue: #3B82F6            /* Admin */
--green: #10B981           /* Employés */
--amber: #F59E0B           /* Clients */
```

### Types de Graphiques
- **Doughnut Chart** : Pour les ratios actifs/inactifs
- **Bar Chart** : Pour la distribution par rôle
- **Trend Cards** : Pour les métriques avec tendances

## 📊 Données de Test Validées

### Résultats du Test Postman
```json
{
    "total_users": 29,
    "active_users_7d": 7,
    "inactive_users": 22,
    "users_by_role": {
        "super_admin": 2,
        "admin": 7,
        "employee": 15,
        "client": 5
    },
    "activity_rates": {
        "last_24h": 13.79,
        "last_7_days": 17.24,
        "last_30_days": 24.14
    }
}
```

## 🔄 Mise à Jour en Temps Réel

### Fréquence de Rafraîchissement
- **Intervalle** : 30 secondes
- **Source** : Base de données MongoDB en temps réel
- **Indicateur** : `is_realtime: true`

### Métadonnées
```json
{
    "last_updated": "2025-05-26T16:03:49.377265+00:00",
    "data_source": "real_time",
    "refresh_interval": 30,
    "dashboard_title": "Tableau de Bord Super Admin"
}
```

## 🚀 Utilisation Frontend

### Appel API
```javascript
const response = await fetch('/api/bi/super-admin/dashboard/', {
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
});
const dashboardData = await response.json();
```

### Structure des Données Reçues
1. **metric_cards[]** - Cartes de métriques avec tendances
2. **charts{}** - Données pour graphiques (doughnut, bar)
3. **detailed_stats{}** - Statistiques détaillées
4. **metadata{}** - Informations de mise à jour

## ✅ Tests Validés

- ✅ Connexion super admin
- ✅ Calculs en temps réel
- ✅ Formatage des données
- ✅ Réponse JSON complète
- ✅ Métriques cohérentes
- ✅ Couleurs et types de graphiques

## ⚠️ Erreurs Courantes et Solutions

### 1. Erreur 403 - Accès Non Autorisé
```json
{"error": "Accès non autorisé"}
```
**Solution :** Vérifier que l'utilisateur connecté a le rôle `super_admin`

### 2. Erreur 500 - Endpoint Historical Data
```
GET /api/bi/historical-data/?data_type=role_distribution&period=30d
500 (Internal Server Error)
```
**Cause :** Import manquant dans la vue `BiHistoricalDataView`
**Solution :** Utiliser uniquement l'endpoint principal `/api/bi/super-admin/dashboard/`

### 3. Frontend - Données Non Affichées
**Problème :** Le frontend appelle de mauvais endpoints
**Solution :** Utiliser uniquement :
```javascript
// ✅ CORRECT
fetch('/api/bi/super-admin/dashboard/')

// ❌ INCORRECT - Ne pas utiliser
fetch('/api/bi/historical-data/')
fetch('/api/bi/metrics/')
```

### 4. Authentification Échouée
**Problème :** Token invalide ou expiré
**Solution :** Renouveler le token d'authentification

## 🔧 Debugging

### Logs à Surveiller
```python
logger.info(f"Dashboard BI récupéré pour: {user.email}, rôle: {user.role}")
logger.info(f"Métriques calculées - Total: {total_users}, Actifs: {active_users}")
```

### Vérification Base de Données
```python
# Compter les utilisateurs
total = User.objects.count()
active_7d = User.objects(last_login__gte=seven_days_ago).count()
```

## 📋 Checklist Frontend

- [ ] Utiliser le bon endpoint `/api/bi/super-admin/dashboard/`
- [ ] Inclure le token d'authentification
- [ ] Vérifier le rôle super_admin
- [ ] Gérer les erreurs 403/500
- [ ] Implémenter le rafraîchissement automatique (30s)
- [ ] Utiliser les couleurs définies dans la documentation
- [ ] Tester avec les données réelles (29 utilisateurs)
