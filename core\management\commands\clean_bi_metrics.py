from django.core.management.base import BaseCommand
from core.models.bi_model import BiMetric
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Nettoie les métriques BI dupliquées'
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Nettoyage des métriques BI dupliquées...'))
        
        # Récupérer tous les types de métriques
        metric_types = BiMetric.objects.distinct('metric_type')
        
        for metric_type in metric_types:
            # Compter les métriques de ce type
            count = BiMetric.objects(metric_type=metric_type).count()
            
            if count > 1:
                self.stdout.write(self.style.WARNING(f'Trouvé {count} métriques de type {metric_type}'))
                
                # Conserver uniquement la métrique la plus récente
                metrics = BiMetric.objects(metric_type=metric_type).order_by('-updated_at')
                latest_metric = metrics.first()
                
                # Supprimer les autres métriques
                for metric in metrics[1:]:
                    metric.delete()
                
                self.stdout.write(self.style.SUCCESS(f'Conservé la métrique la plus récente (ID: {latest_metric.id}) et supprimé {count-1} métriques dupliquées'))
            else:
                self.stdout.write(self.style.SUCCESS(f'Aucune métrique dupliquée trouvée pour le type {metric_type}'))
        
        self.stdout.write(self.style.SUCCESS('Nettoyage des métriques BI terminé'))
