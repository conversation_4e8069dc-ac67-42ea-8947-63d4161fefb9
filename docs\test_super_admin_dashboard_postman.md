# Tests Postman - Tableau de Bord Super Admin en Temps Réel

## Vue d'ensemble

Ce guide vous permet de tester le nouveau tableau de bord interactif en temps réel pour le super admin avec les métriques suivantes :

### 📊 Métriques Implémentées :
1. **Nombre total d'utilisateurs** - Importé en temps réel depuis la collection `users`
2. **Utilisateurs actifs/inactifs** - Calculé selon les connexions récentes
3. **Distribution par rôle** - Répartition des utilisateurs par rôle (super_admin, admin, employee, client)
4. **Graphiques interactifs** - Données formatées pour les visualisations frontend

## 🔐 Prérequis

1. **Serveur démarré** : `python manage.py runserver`
2. **Base de données avec des utilisateurs**
3. **Token super admin** pour accéder aux métriques

## 📋 Étape 1 : Authentification Super Admin

### Connexion Super Admin
**URL** : `POST http://localhost:8000/api/login/`
**Body** :
```json
{
  "email": "<EMAIL>",
  "password": "votre_mot_de_passe_super_admin"
}
```

**Réponse attendue** :
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "super_admin_id",
    "email": "<EMAIL>",
    "name": "Super Admin",
    "role": "super_admin"
  }
}
```

**⚠️ Important** : Copiez le token `access` pour les requêtes suivantes.

## 🎯 Étape 2 : Test du Tableau de Bord Super Admin

### Récupérer les métriques en temps réel
**URL** : `GET http://localhost:8000/api/bi/super-admin/dashboard/`
**Headers** :
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Réponse attendue** :
```json
{
  "timestamp": "2025-01-27T10:30:00.000Z",
  "is_realtime": true,
  "summary": {
    "total_users": 38,
    "active_users_30d": 25,
    "inactive_users": 13,
    "new_users_7d": 3,
    "activity_rate_30d": 65.79
  },
  "users_by_role": {
    "super_admin": 3,
    "admin": 9,
    "employee": 14,
    "client": 12
  },
  "activity_stats": {
    "total_users": 38,
    "active_users": {
      "last_24h": 8,
      "last_7_days": 18,
      "last_30_days": 25
    },
    "inactive_users": 13,
    "never_logged_in": 5,
    "activity_rate": {
      "last_24h": 21.05,
      "last_7_days": 47.37,
      "last_30_days": 65.79
    }
  },
  "users_with_permissions": {
    "super_admin": {
      "count": 3,
      "percentage": 7.89
    },
    "admin": {
      "count": 9,
      "percentage": 23.68
    },
    "employee": {
      "count": 14,
      "percentage": 36.84
    },
    "client": {
      "count": 12,
      "percentage": 31.58
    }
  },
  "charts": {
    "role_distribution": {
      "type": "pie",
      "title": "Distribution des utilisateurs par rôle",
      "data": [
        {
          "name": "Super Admins",
          "value": 3,
          "color": "#8B5CF6"
        },
        {
          "name": "Administrateurs",
          "value": 9,
          "color": "#3B82F6"
        },
        {
          "name": "Employés",
          "value": 14,
          "color": "#10B981"
        },
        {
          "name": "Clients",
          "value": 12,
          "color": "#F59E0B"
        }
      ]
    },
    "activity_overview": {
      "type": "bar",
      "title": "Activité des utilisateurs",
      "data": [
        {
          "period": "Dernières 24h",
          "active": 8,
          "inactive": 30
        },
        {
          "period": "Derniers 7 jours",
          "active": 18,
          "inactive": 20
        },
        {
          "period": "Derniers 30 jours",
          "active": 25,
          "inactive": 13
        }
      ]
    },
    "user_status": {
      "type": "doughnut",
      "title": "Statut des utilisateurs",
      "data": [
        {
          "name": "Actifs (30j)",
          "value": 25,
          "color": "#10B981"
        },
        {
          "name": "Inactifs",
          "value": 13,
          "color": "#EF4444"
        },
        {
          "name": "Jamais connectés",
          "value": 5,
          "color": "#6B7280"
        }
      ]
    }
  },
  "metadata": {
    "last_updated": "2025-01-27T10:30:00.000Z",
    "data_source": "real_time",
    "refresh_interval": 30
  }
}
```

## 🔍 Étape 3 : Vérification des Données

### Test 1 : Vérifier le nombre total d'utilisateurs
**URL** : `GET http://localhost:8000/api/users/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Vérification** : Le nombre d'utilisateurs retournés doit correspondre à `summary.total_users` dans le dashboard.

### Test 2 : Vérifier la distribution par rôle
Comparez les valeurs dans `users_by_role` avec les utilisateurs réels dans votre base de données.

### Test 3 : Test en temps réel
1. **Première requête** : Notez le `timestamp` de la réponse
2. **Attendez 30 secondes**
3. **Deuxième requête** : Le `timestamp` doit être différent et les données mises à jour

## 🚫 Étape 4 : Tests de Sécurité

### Test 4 : Accès refusé pour non super admin

**Créer un utilisateur admin/employee/client et tester :**

**URL** : `GET http://localhost:8000/api/bi/super-admin/dashboard/`
**Headers** :
```
Authorization: Bearer {token_non_super_admin}
```

**Réponse attendue** :
```json
{
  "error": "Unauthorized",
  "message": "Only super admin can access this resource",
  "current_role": "admin",
  "required_role": "super_admin"
}
```

### Test 5 : Accès sans authentification

**URL** : `GET http://localhost:8000/api/bi/super-admin/dashboard/`
**Headers** : (Aucun header Authorization)

**Réponse attendue** :
```json
{
  "detail": "Authentication credentials were not provided."
}
```

## 📊 Étape 5 : Tests de Performance

### Test 6 : Temps de réponse
- **Objectif** : Réponse en moins de 2 secondes
- **Méthode** : Vérifier le temps de réponse dans Postman
- **Critère** : Acceptable si < 2000ms

### Test 7 : Rafraîchissement multiple
1. **Effectuer 5 requêtes consécutives** espacées de 5 secondes
2. **Vérifier** que chaque réponse a un `timestamp` différent
3. **Vérifier** que les données sont cohérentes

## 🎨 Étape 6 : Validation des Données pour Graphiques

### Graphique en secteurs (Distribution par rôle)
**Vérifications** :
- ✅ Somme des valeurs = `total_users`
- ✅ Couleurs définies pour chaque segment
- ✅ Noms en français

### Graphique en barres (Activité)
**Vérifications** :
- ✅ Données pour 3 périodes (24h, 7j, 30j)
- ✅ `active + inactive = total_users` pour chaque période
- ✅ Progression logique (24h ≤ 7j ≤ 30j pour les actifs)

### Graphique en anneau (Statut)
**Vérifications** :
- ✅ `Actifs + Inactifs + Jamais connectés = total_users`
- ✅ Couleurs distinctes pour chaque statut

## ✅ Checklist de Validation

### Données de Base
- [ ] Nombre total d'utilisateurs correct
- [ ] Distribution par rôle exacte
- [ ] Calculs d'activité cohérents
- [ ] Pourcentages corrects (somme = 100%)

### Temps Réel
- [ ] Timestamp mis à jour à chaque requête
- [ ] Données rafraîchies automatiquement
- [ ] Indicateur `is_realtime: true`

### Sécurité
- [ ] Accès restreint aux super admins
- [ ] Authentification requise
- [ ] Messages d'erreur appropriés

### Performance
- [ ] Temps de réponse < 2 secondes
- [ ] Pas d'erreurs 500
- [ ] Logs serveur propres

### Graphiques
- [ ] Données formatées pour les visualisations
- [ ] Couleurs définies
- [ ] Types de graphiques spécifiés
- [ ] Titres en français

## 🔧 Dépannage

### Erreur 500 Internal Server Error
1. **Vérifier les logs serveur** pour l'erreur exacte
2. **Vérifier la base de données** MongoDB
3. **Redémarrer le serveur** si nécessaire

### Données incohérentes
1. **Vérifier les critères d'activité** (30 jours par défaut)
2. **Vérifier les dates** `last_login` dans la base
3. **Recalculer manuellement** quelques valeurs

### Accès refusé
1. **Vérifier le token** JWT
2. **Vérifier le rôle** de l'utilisateur connecté
3. **Vérifier l'expiration** du token

## 🎯 Résultats Attendus

Le tableau de bord super admin doit fournir :
- **Métriques en temps réel** mises à jour automatiquement
- **Visualisations prêtes** pour le frontend
- **Sécurité robuste** avec accès restreint
- **Performance optimale** avec réponses rapides
- **Données cohérentes** et fiables pour la prise de décision
