#!/usr/bin/env python
"""
Script de test pour les contrôles de duplication des événements et tâches d'équipe
"""

import requests
import json

def test_team_duplication_controls():
    """Test des contrôles de duplication pour les éléments d'équipe"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion admin (remplacez par un admin réel)
    admin_login_data = {
        "email": "<EMAIL>",  # Remplacez par un email admin réel
        "password": "AdminPassword123$"  # Remplacez par le mot de passe admin réel
    }
    
    print("=== Test des contrôles de duplication d'équipe ===\n")
    
    # 1. Connexion admin
    print("1. Connexion admin...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=admin_login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion admin: {login_response.json()}")
        print("ℹ️  Veuillez modifier les identifiants admin dans le script")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion admin réussie")
    
    # 2. Récupérer les équipes de l'admin
    print("\n2. Récupération des équipes...")
    teams_response = requests.get(f"{base_url}/teams/", headers=headers)
    
    if teams_response.status_code != 200:
        print(f"❌ Échec de récupération des équipes: {teams_response.json()}")
        return
    
    teams = teams_response.json()
    if not teams:
        print("❌ Aucune équipe trouvée pour cet admin")
        print("ℹ️  Veuillez créer une équipe pour cet admin avant de lancer le test")
        return
    
    team_id = teams[0]['id']
    team_name = teams[0]['name']
    print(f"✅ Équipe trouvée: {team_name} (ID: {team_id})")
    
    # 3. Test des événements d'équipe
    print("\n3. Test de duplication - Événements d'équipe...")
    
    event_data = {
        "title": "Test Événement Équipe Duplication",
        "description": "Test de duplication pour équipe",
        "start_date": "2024-12-25T10:00:00Z",
        "end_date": "2024-12-25T12:00:00Z",
        "start_time": "10:00",
        "end_time": "12:00",
        "team_id": team_id,
        "color": "#ff0000"
    }
    
    # Première création
    first_event = requests.post(
        f"{base_url}/events/",
        json=event_data,
        headers=headers
    )
    
    if first_event.status_code == 201:
        print("✅ Premier événement d'équipe créé avec succès")
        
        # Tentative de duplication pour la même équipe
        duplicate_event = requests.post(
            f"{base_url}/events/",
            json=event_data,
            headers=headers
        )
        
        if duplicate_event.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les événements d'équipe")
            print(f"📄 Message: {duplicate_event.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les événements d'équipe")
            print(f"📄 Réponse: {duplicate_event.json()}")
    else:
        print(f"❌ Échec de création du premier événement d'équipe: {first_event.json()}")
    
    # 4. Test des tâches d'équipe
    print("\n4. Test de duplication - Tâches d'équipe...")
    
    # Récupérer les admins pour le champ responsable
    users_response = requests.get(f"{base_url}/users/", headers=headers)
    if users_response.status_code != 200:
        print(f"❌ Échec de récupération des utilisateurs: {users_response.json()}")
        return
    
    users = users_response.json()
    admin_users = [user for user in users if user.get('role') == 'admin']
    
    if not admin_users:
        print("❌ Aucun admin trouvé pour assigner comme responsable")
        return
    
    responsable_id = admin_users[0]['id']
    
    task_data = {
        "title": "Test Tâche Équipe Duplication",
        "description": "Test de duplication pour tâche d'équipe",
        "start_date": "2024-12-25T10:00:00Z",
        "end_date": "2024-12-25T12:00:00Z",
        "team_id": team_id,
        "responsable": responsable_id,
        "priority": "moyenne"
    }
    
    # Première création
    first_task = requests.post(
        f"{base_url}/team-tasks/",
        json=task_data,
        headers=headers
    )
    
    if first_task.status_code == 201:
        print("✅ Première tâche d'équipe créée avec succès")
        
        # Tentative de duplication pour la même équipe
        duplicate_task = requests.post(
            f"{base_url}/team-tasks/",
            json=task_data,
            headers=headers
        )
        
        if duplicate_task.status_code == 400:
            print("✅ Contrôle de duplication fonctionne pour les tâches d'équipe")
            print(f"📄 Message: {duplicate_task.json().get('error')}")
        else:
            print("❌ Contrôle de duplication ne fonctionne pas pour les tâches d'équipe")
            print(f"📄 Réponse: {duplicate_task.json()}")
    else:
        print(f"❌ Échec de création de la première tâche d'équipe: {first_task.json()}")
    
    # 5. Test avec une équipe différente (si disponible)
    if len(teams) > 1:
        print("\n5. Test avec équipe différente...")
        
        different_team_id = teams[1]['id']
        different_team_name = teams[1]['name']
        
        # Même titre mais équipe différente (doit fonctionner)
        different_team_event_data = {
            "title": "Test Événement Équipe Duplication",  # Même titre
            "description": "Test avec équipe différente",
            "start_date": "2024-12-26T10:00:00Z",
            "end_date": "2024-12-26T12:00:00Z",
            "start_time": "10:00",
            "end_time": "12:00",
            "team_id": different_team_id,
            "color": "#00ff00"
        }
        
        different_team_event = requests.post(
            f"{base_url}/events/",
            json=different_team_event_data,
            headers=headers
        )
        
        if different_team_event.status_code == 201:
            print(f"✅ Événement avec même titre créé pour équipe différente ({different_team_name})")
        else:
            print(f"❌ Échec de création d'événement pour équipe différente: {different_team_event.json()}")
    else:
        print("\n5. Test avec équipe différente...")
        print("ℹ️  Une seule équipe disponible, test d'équipe différente ignoré")
    
    # 6. Test avec titres différents (doit fonctionner)
    print("\n6. Test avec titres différents pour la même équipe...")
    
    different_title_event_data = {
        "title": "Test Événement Équipe Différent",  # Titre différent
        "description": "Test avec titre différent",
        "start_date": "2024-12-27T10:00:00Z",
        "end_date": "2024-12-27T12:00:00Z",
        "start_time": "10:00",
        "end_time": "12:00",
        "team_id": team_id,
        "color": "#0000ff"
    }
    
    different_title_event = requests.post(
        f"{base_url}/events/",
        json=different_title_event_data,
        headers=headers
    )
    
    if different_title_event.status_code == 201:
        print("✅ Événement avec titre différent créé avec succès pour la même équipe")
    else:
        print(f"❌ Échec de création d'événement avec titre différent: {different_title_event.json()}")
    
    print("\n" + "="*60)
    print("🎯 TEST DES CONTRÔLES DE DUPLICATION D'ÉQUIPE TERMINÉ")
    print("="*60)
    print("\n📋 Résumé des contrôles d'équipe:")
    print("✅ Événements d'équipe - Titre unique par équipe/membre")
    print("✅ Tâches d'équipe - Titre unique par équipe")
    print("✅ Flexibilité - Même titre autorisé pour équipes différentes")

if __name__ == "__main__":
    test_team_duplication_controls()
