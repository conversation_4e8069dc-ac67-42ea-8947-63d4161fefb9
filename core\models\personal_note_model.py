from mongoengine import Document, <PERSON><PERSON><PERSON>, DateTimeField, <PERSON><PERSON>an<PERSON>ield
from datetime import datetime, timezone
from bson import ObjectId

class PersonalNote(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    content = StringField(required=True)
    created_by = StringField(required=True)  # ID de l'utilisateur qui a créé la note (employé ou client)
    created_by_name = StringField(required=False)  # Nom de l'utilisateur qui a créé la note
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))
    is_archived = BooleanField(default=False)  # Indique si la note est archivée
    
    meta = {
        'collection': 'personal_notes',
        'indexes': [
            {'fields': ['created_by']},
            {'fields': ['created_at']},
            {'fields': ['is_archived']}
        ]
    }
    
    def save(self, *args, **kwargs):
        # <PERSON><PERSON><PERSON>ir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(PersonalNote, self).save(*args, **kwargs)
    
    def can_manage_note(self, user):
        """Vérifie si un utilisateur peut gérer cette note personnelle"""
        # Un utilisateur ne peut gérer que ses propres notes personnelles
        return str(user.id) == self.created_by
