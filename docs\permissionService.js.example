/**
 * Service de gestion des permissions pour le frontend
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/services/
 */

/**
 * Vérifie si l'utilisateur a une permission spécifique
 * @param {Object} user - L'utilisateur courant
 * @param {String} permission - La permission à vérifier
 * @returns {Boolean} - True si l'utilisateur a la permission
 */
export const hasPermission = (user, permission) => {
  if (!user || !user.permissions) return false;
  return user.permissions[permission] === true;
};

/**
 * Vérifie si l'utilisateur peut gérer une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier
 * @returns {Boolean} - True si l'utilisateur peut gérer l'équipe
 */
export const canManageTeam = (user, team) => {
  if (!user || !team) return false;
  
  // Vérifier si l'utilisateur est un admin et le responsable de l'équipe
  return user.role === 'admin' && user.id === team.responsable?.id;
};

/**
 * Vérifie si l'utilisateur peut voir une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier
 * @returns {Boolean} - True si l'utilisateur peut voir l'équipe
 */
export const canViewTeam = (user, team) => {
  if (!user || !team) return false;
  
  // Les admins peuvent voir toutes les équipes
  if (user.role === 'admin') return true;
  
  // Les employés peuvent voir les équipes dont ils sont membres
  if (user.role === 'employee') {
    return team.members?.some(member => member.id === user.id);
  }
  
  return false;
};

/**
 * Vérifie les permissions complètes pour une équipe
 * @param {Object} user - L'utilisateur courant
 * @param {Object} team - L'équipe à vérifier (peut être null pour vérifier les permissions générales)
 * @returns {Object} - Objet contenant toutes les permissions
 */
export const checkTeamPermissions = (user, team = null) => {
  // Permissions par défaut
  const defaultPermissions = {
    canView: false,
    canManage: false,
    canAddMembers: false,
    canRemoveMembers: false,
    canCreateTeam: false,
    isResponsable: false,
    isMember: false
  };
  
  // Si pas d'utilisateur, retourner les permissions par défaut
  if (!user) return defaultPermissions;
  
  const isAdmin = user.role === 'admin';
  const hasTeamManagementPermission = hasPermission(user, 'manage_teams');
  
  // Permissions générales (sans équipe spécifique)
  if (!team) {
    return {
      ...defaultPermissions,
      canCreateTeam: isAdmin && hasTeamManagementPermission
    };
  }
  
  // Vérifications spécifiques à l'équipe
  const isResponsable = user.id === team.responsable?.id;
  const isMember = team.members?.some(member => member.id === user.id) || false;
  
  return {
    canView: isAdmin || isMember,
    canManage: isAdmin && isResponsable,
    canAddMembers: isAdmin && isResponsable && hasTeamManagementPermission,
    canRemoveMembers: isAdmin && isResponsable && hasTeamManagementPermission,
    canCreateTeam: isAdmin && hasTeamManagementPermission,
    isResponsable,
    isMember
  };
};

/**
 * Vérifie si l'utilisateur a les permissions d'administrateur
 * @param {Object} user - L'utilisateur courant
 * @returns {Boolean} - True si l'utilisateur a les permissions d'administrateur
 */
export const checkAdminPermissions = (user) => {
  if (!user) return false;
  
  // Vérifier si l'utilisateur est un admin
  if (user.role !== 'admin') {
    throw new Error("Vous devez être administrateur pour effectuer cette action");
  }
  
  // Vérifier si l'admin a la permission de gérer les équipes
  if (!hasPermission(user, 'manage_teams')) {
    throw new Error("Vous n'avez pas les permissions nécessaires pour gérer les équipes");
  }
  
  return true;
};

/**
 * Vérifie si l'utilisateur a les permissions pour gérer une équipe spécifique
 * @param {Object} user - L'utilisateur courant
 * @param {String} teamId - ID de l'équipe
 * @param {Object} team - Objet équipe (optionnel)
 * @returns {Boolean} - True si l'utilisateur peut gérer l'équipe
 */
export const checkTeamManagementPermissions = (user, teamId, team = null) => {
  // Vérifier d'abord les permissions d'admin
  checkAdminPermissions(user);
  
  // Si l'équipe est fournie, vérifier si l'utilisateur est le responsable
  if (team) {
    if (user.id !== team.responsable?.id) {
      throw new Error("Seul le responsable de l'équipe peut effectuer cette action");
    }
  }
  
  return true;
};