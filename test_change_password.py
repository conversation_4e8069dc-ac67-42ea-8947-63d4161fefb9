#!/usr/bin/env python
"""
Script de test pour le changement de mot de passe utilisateur
"""

import requests
import json

def test_change_password():
    """Test de l'API de changement de mot de passe"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test de changement de mot de passe ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Test de l'API de changement de mot de passe (GET)
    print("\n2. Test d'accessibilité de l'API...")
    get_response = requests.get(f"{base_url}/profile/change-password/", headers=headers)
    
    if get_response.status_code == 200:
        print("✅ API accessible")
        print(f"📄 Réponse: {get_response.json()}")
    else:
        print(f"❌ API non accessible: {get_response.status_code}")
        return
    
    # 3. Test avec données manquantes
    print("\n3. Test avec données manquantes...")
    
    incomplete_data = {
        "current_password": "Sarra123$"
        # Manque new_password
    }
    
    incomplete_response = requests.post(
        f"{base_url}/profile/change-password/",
        json=incomplete_data,
        headers=headers
    )
    
    print(f"📡 Statut avec données incomplètes: {incomplete_response.status_code}")
    if incomplete_response.status_code == 400:
        print("✅ Validation des champs requis fonctionne")
        print(f"📄 Erreur: {incomplete_response.json()}")
    else:
        print("❌ La validation des champs requis ne fonctionne pas")
    
    # 4. Test avec mot de passe actuel incorrect
    print("\n4. Test avec mot de passe actuel incorrect...")
    
    wrong_password_data = {
        "current_password": "MauvaisMotDePasse",
        "new_password": "NouveauMotDePasse123$"
    }
    
    wrong_response = requests.post(
        f"{base_url}/profile/change-password/",
        json=wrong_password_data,
        headers=headers
    )
    
    print(f"📡 Statut avec mauvais mot de passe: {wrong_response.status_code}")
    if wrong_response.status_code == 400:
        print("✅ Vérification du mot de passe actuel fonctionne")
        print(f"📄 Erreur: {wrong_response.json()}")
    else:
        print("❌ La vérification du mot de passe actuel ne fonctionne pas")
    
    # 5. Test avec nouveau mot de passe trop court
    print("\n5. Test avec nouveau mot de passe trop court...")
    
    short_password_data = {
        "current_password": "Sarra123$",
        "new_password": "123"  # Trop court
    }
    
    short_response = requests.post(
        f"{base_url}/profile/change-password/",
        json=short_password_data,
        headers=headers
    )
    
    print(f"📡 Statut avec mot de passe court: {short_response.status_code}")
    if short_response.status_code == 400:
        print("✅ Validation de la longueur du mot de passe fonctionne")
        print(f"📄 Erreur: {short_response.json()}")
    else:
        print("❌ La validation de la longueur ne fonctionne pas")
    
    # 6. Test de changement de mot de passe valide
    print("\n6. Test de changement de mot de passe valide...")
    
    valid_data = {
        "current_password": "Sarra123$",
        "new_password": "NouveauMotDePasse123$"
    }
    
    print(f"📤 Données à envoyer:")
    print(json.dumps({
        "current_password": "***",
        "new_password": "***"
    }, indent=2))
    
    change_response = requests.post(
        f"{base_url}/profile/change-password/",
        json=valid_data,
        headers=headers
    )
    
    print(f"📡 Statut du changement: {change_response.status_code}")
    
    if change_response.status_code == 200:
        response_data = change_response.json()
        print("✅ Changement de mot de passe réussi!")
        print(f"📄 Réponse: {response_data}")
        
        # 7. Test de connexion avec le nouveau mot de passe
        print("\n7. Test de connexion avec le nouveau mot de passe...")
        
        new_login_data = {
            "email": "<EMAIL>",
            "password": "NouveauMotDePasse123$"
        }
        
        new_login_response = requests.post(
            f"{base_url}/login/",
            json=new_login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if new_login_response.status_code == 200:
            print("✅ Connexion avec le nouveau mot de passe réussie!")
            
            # 8. Remettre l'ancien mot de passe pour les autres tests
            print("\n8. Remise de l'ancien mot de passe...")
            
            new_token = new_login_response.json()['access']
            new_headers = {
                "Authorization": f"Bearer {new_token}",
                "Content-Type": "application/json"
            }
            
            restore_data = {
                "current_password": "NouveauMotDePasse123$",
                "new_password": "Sarra123$"
            }
            
            restore_response = requests.post(
                f"{base_url}/profile/change-password/",
                json=restore_data,
                headers=new_headers
            )
            
            if restore_response.status_code == 200:
                print("✅ Mot de passe original restauré")
            else:
                print("❌ Erreur lors de la restauration du mot de passe")
                print(f"📄 Erreur: {restore_response.json()}")
        else:
            print("❌ Échec de la connexion avec le nouveau mot de passe")
            print(f"📄 Erreur: {new_login_response.json()}")
    else:
        print(f"❌ Erreur {change_response.status_code}:")
        try:
            error_data = change_response.json()
            print(json.dumps(error_data, indent=2))
        except:
            print(change_response.text)
    
    print("\n" + "="*60)
    print("🎯 TEST DE CHANGEMENT DE MOT DE PASSE TERMINÉ")
    print("="*60)

if __name__ == "__main__":
    test_change_password()
