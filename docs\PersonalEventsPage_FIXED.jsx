import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

const PersonalEventsPage = () => {
  const { user, apiCall } = useAuth();

  // États séparés pour les événements actifs et archivés
  const [activeEvents, setActiveEvents] = useState([]);
  const [archivedEvents, setArchivedEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentView, setCurrentView] = useState('agenda'); // 'agenda' ou 'archives'

  // Charger les événements actifs
  const loadActiveEvents = async () => {
    try {
      const data = await apiCall('/personal-events/', 'GET');
      setActiveEvents(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des événements actifs:', error);
      setError('Erreur lors du chargement des événements');
    }
  };

  // Charger les événements archivés
  const loadArchivedEvents = async () => {
    try {
      console.log('🔄 Chargement des événements archivés...');
      const data = await apiCall('/personal-events/archived/list/', 'GET');
      console.log('📥 Données reçues:', data);

      if (data && data.archived_events) {
        setArchivedEvents(data.archived_events);
        console.log(`✅ ${data.archived_events.length} événement(s) archivé(s) chargé(s)`);
      } else {
        console.log('⚠️ Aucun événement archivé dans la réponse');
        setArchivedEvents([]);
      }
    } catch (error) {
      console.error('❌ Erreur lors du chargement des événements archivés:', error);
      setError('Erreur lors du chargement des événements archivés');
    }
  };

  // Charger tous les événements
  const loadAllEvents = async () => {
    setLoading(true);
    try {
      await Promise.all([loadActiveEvents(), loadArchivedEvents()]);
    } catch (error) {
      setError('Erreur lors du chargement des événements');
    } finally {
      setLoading(false);
    }
  };

  // Effet initial
  useEffect(() => {
    loadAllEvents();
  }, []);

  // Archiver un événement
  const handleArchiveEvent = async (eventId) => {
    try {
      const response = await apiCall(`/personal-events/${eventId}/archive/`, 'PUT');

      if (response.message) {
        // Supprimer l'événement de la liste active
        setActiveEvents(prev => prev.filter(event => event.id !== eventId));

        // Recharger les événements archivés pour inclure le nouvel archivé
        await loadArchivedEvents();

        alert('Événement archivé avec succès !');
      }
    } catch (error) {
      console.error('Erreur lors de l\'archivage:', error);
      alert('Erreur lors de l\'archivage de l\'événement');
    }
  };

  // Désarchiver un événement
  const handleUnarchiveEvent = async (eventId) => {
    try {
      const response = await apiCall(`/personal-events/${eventId}/unarchive/`, 'PUT');

      if (response.message) {
        // Supprimer l'événement de la liste archivée
        setArchivedEvents(prev => prev.filter(event => event.id !== eventId));

        // Recharger les événements actifs pour inclure le désarchivé
        await loadActiveEvents();

        alert('Événement désarchivé avec succès !');
      }
    } catch (error) {
      console.error('Erreur lors du désarchivage:', error);
      alert('Erreur lors du désarchivage de l\'événement');
    }
  };

  // Supprimer définitivement un événement
  const handleDeleteEvent = async (eventId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer définitivement cet événement ?')) {
      return;
    }

    try {
      await apiCall(`/personal-events/${eventId}/`, 'DELETE');

      // Supprimer de la liste appropriée
      if (currentView === 'agenda') {
        setActiveEvents(prev => prev.filter(event => event.id !== eventId));
      } else {
        setArchivedEvents(prev => prev.filter(event => event.id !== eventId));
      }

      alert('Événement supprimé avec succès !');
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      alert('Erreur lors de la suppression de l\'événement');
    }
  };

  // Formater la date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  // Obtenir le style du statut
  const getStatusStyle = (status) => {
    const styles = {
      pending: { backgroundColor: '#fef3c7', color: '#92400e', border: '1px solid #fbbf24' },
      completed: { backgroundColor: '#d1fae5', color: '#065f46', border: '1px solid #10b981' },
      archived: { backgroundColor: '#f3f4f6', color: '#6b7280', border: '1px solid #d1d5db' }
    };
    return styles[status] || styles.pending;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-lg">Chargement des événements...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* En-tête avec navigation */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          {currentView === 'agenda' ? 'Agenda des événements' : 'Archives'}
        </h1>

        <div className="flex space-x-4">
          <button
            onClick={() => setCurrentView('agenda')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${currentView === 'agenda'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
          >
            📅 Agenda ({activeEvents.length})
          </button>

          <button
            onClick={() => setCurrentView('archives')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${currentView === 'archives'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
          >
            🗂️ Archives ({archivedEvents.length})
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Vue Agenda */}
      {currentView === 'agenda' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Liste de tous les événements à venir
            </h2>
          </div>

          {activeEvents.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              Aucun événement actif trouvé
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {activeEvents.map((event) => (
                    <tr key={event.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {event.title}
                        </div>
                        {event.description && (
                          <div className="text-sm text-gray-500">
                            {event.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(event.start_date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          style={getStatusStyle(event.status)}
                        >
                          {event.status === 'pending' ? 'En attente' :
                            event.status === 'completed' ? 'Terminé' : 'Actif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => handleArchiveEvent(event.id)}
                          className="text-yellow-600 hover:text-yellow-900 bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded"
                          title="Archiver"
                        >
                          🗂️
                        </button>
                        <button
                          onClick={() => handleDeleteEvent(event.id)}
                          className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded"
                          title="Supprimer"
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Vue Archives */}
      {currentView === 'archives' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">
              Liste des événements archivés
            </h2>
            <button
              onClick={() => setCurrentView('agenda')}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              ← Retour au calendrier
            </button>
          </div>

          {archivedEvents.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              Aucun événement archivé trouvé
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Titre
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {archivedEvents.map((event) => (
                    <tr key={event.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-500 line-through">
                          {event.title}
                        </div>
                        {event.description && (
                          <div className="text-sm text-gray-400 line-through">
                            {event.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(event.start_date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button
                          onClick={() => handleUnarchiveEvent(event.id)}
                          className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-3 py-1 rounded"
                          title="Désarchiver"
                        >
                          ↩️
                        </button>
                        <button
                          onClick={() => handleDeleteEvent(event.id)}
                          className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-3 py-1 rounded"
                          title="Supprimer définitivement"
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PersonalEventsPage;
