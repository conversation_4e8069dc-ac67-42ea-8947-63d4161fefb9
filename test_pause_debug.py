#!/usr/bin/env python
"""
Script de debug pour tester spécifiquement la fonction pause
"""

import requests
import time
import json

def test_pause_functionality():
    """Test spécifique de la fonctionnalité pause"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== DEBUG : Test de la fonction PAUSE ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. État initial
    print("\n2. Vérification de l'état initial...")
    initial_response = requests.get(f"{base_url}/pomodoro/settings/", headers=headers)
    initial_data = initial_response.json()
    
    print(f"📊 État initial:")
    print(f"   - is_active: {initial_data['is_active']}")
    print(f"   - status: {initial_data['current_session']['status']}")
    print(f"   - remaining_time: {initial_data['current_session']['remaining_time']}")
    
    # 3. Démarrer une session
    print("\n3. Démarrage d'une session...")
    start_response = requests.post(f"{base_url}/pomodoro/control/start/", headers=headers)
    
    if start_response.status_code != 200:
        print(f"❌ Échec du démarrage: {start_response.json()}")
        return
    
    start_data = start_response.json()
    print(f"✅ Session démarrée:")
    print(f"   - is_active: {start_data['is_active']}")
    print(f"   - status: {start_data['current_session']['status']}")
    print(f"   - remaining_time: {start_data['current_session']['remaining_time']}")
    
    # 4. Attendre quelques secondes
    print("\n4. Attente de 5 secondes...")
    time.sleep(5)
    
    # 5. Vérifier l'état après attente
    print("\n5. État après 5 secondes...")
    check_response = requests.get(f"{base_url}/pomodoro/settings/", headers=headers)
    check_data = check_response.json()
    
    print(f"📊 État après attente:")
    print(f"   - is_active: {check_data['is_active']}")
    print(f"   - status: {check_data['current_session']['status']}")
    print(f"   - remaining_time: {check_data['current_session']['remaining_time']}")
    
    # 6. PAUSE - Le test critique
    print("\n6. 🔍 TEST CRITIQUE : PAUSE...")
    pause_response = requests.post(f"{base_url}/pomodoro/control/pause/", headers=headers)
    
    print(f"📡 Réponse HTTP: {pause_response.status_code}")
    
    if pause_response.status_code == 200:
        pause_data = pause_response.json()
        print(f"✅ Pause réussie:")
        print(f"   - Message: {pause_data['message']}")
        print(f"   - is_active: {pause_data['is_active']}")
        print(f"   - status: {pause_data['current_session']['status']}")
        print(f"   - remaining_time: {pause_data['current_session']['remaining_time']}")
        
        # 7. Vérifier immédiatement après pause
        print("\n7. Vérification immédiate après pause...")
        verify_response = requests.get(f"{base_url}/pomodoro/settings/", headers=headers)
        verify_data = verify_response.json()
        
        print(f"📊 État vérifié:")
        print(f"   - is_active: {verify_data['is_active']}")
        print(f"   - status: {verify_data['current_session']['status']}")
        print(f"   - remaining_time: {verify_data['current_session']['remaining_time']}")
        
        # 8. Attendre encore et vérifier que le temps ne change pas
        print("\n8. Test : le temps doit rester figé pendant la pause...")
        time_before_wait = verify_data['current_session']['remaining_time']
        print(f"⏰ Temps avant attente: {time_before_wait}")
        
        time.sleep(3)
        
        final_check_response = requests.get(f"{base_url}/pomodoro/settings/", headers=headers)
        final_check_data = final_check_response.json()
        time_after_wait = final_check_data['current_session']['remaining_time']
        
        print(f"⏰ Temps après 3 secondes: {time_after_wait}")
        
        if time_before_wait == time_after_wait:
            print("✅ SUCCÈS : Le temps est bien figé pendant la pause !")
        else:
            print("❌ PROBLÈME : Le temps continue de s'écouler pendant la pause !")
            
        # 9. Test de reprise
        print("\n9. Test de reprise...")
        resume_response = requests.post(f"{base_url}/pomodoro/control/resume/", headers=headers)
        
        if resume_response.status_code == 200:
            resume_data = resume_response.json()
            print(f"✅ Reprise réussie:")
            print(f"   - Message: {resume_data['message']}")
            print(f"   - status: {resume_data['current_session']['status']}")
        else:
            print(f"❌ Échec de la reprise: {resume_response.json()}")
            
    else:
        print(f"❌ Échec de la pause: {pause_response.json()}")
    
    # 10. Nettoyage - Reset
    print("\n10. Nettoyage...")
    reset_response = requests.post(f"{base_url}/pomodoro/control/reset/", headers=headers)
    if reset_response.status_code == 200:
        print("✅ Session réinitialisée")
    
    print("\n" + "="*60)
    print("🔍 DIAGNOSTIC COMPLET TERMINÉ")
    print("="*60)

if __name__ == "__main__":
    test_pause_functionality()
