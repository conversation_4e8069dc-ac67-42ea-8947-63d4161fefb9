# Guide de Test - Affichage des Noms de Membres

## 🎯 Objectif
Vérifier que les tâches et événements d'équipe affichent correctement :
- **Le nom du membre** quand assigné à un membre spécifique
- **"Toute l'équipe"** quand assigné à toute l'équipe

## ✅ Corrections Apportées

### 1. **Création de Tâches/Événements** (`team_task_views.py` & `event_views.py`)
- ✅ Récupération automatique du nom du membre lors de la création
- ✅ Stockage du `member_name` dans la base de données
- ✅ Validation que le membre existe avant assignation
- ✅ Gestion des erreurs si le membre n'est pas trouvé

### 2. **Affichage des Tâches/Événements** (Méthodes GET)
- ✅ Logique pour distinguer membre spécifique vs toute l'équipe :
  - Si `member_id` existe et non vide → Affiche le nom du membre
  - Si `member_id` vide ou null → Affiche "Toute l'équipe"
- ✅ Récupération dynamique du nom si manquant dans la DB
- ✅ Fallback "Membre inconnu" si l'ID n'existe plus

### 3. **Mise à Jour des Tâches/Événements** (Méthodes PUT/PATCH)
- ✅ Mise à jour automatique du `member_name` lors des modifications
- ✅ Gestion des changements d'assignation (membre → équipe ou équipe → membre)
- ✅ Validation du nouveau membre lors des modifications

### 4. **Migration des Données Existantes**
- ✅ **8 tâches** analysées et corrigées automatiquement
- ✅ **9 événements** analysés et corrigés automatiquement
- ✅ Récupération des noms manquants depuis la base de données

### 5. **Correction Critique du Filtrage** 🔥
- ✅ **PROBLÈME RÉSOLU** : Les tâches/événements assignés à "toute l'équipe" n'apparaissaient pour personne
- ✅ **Nouvelle logique** : Filtrage corrigé pour distinguer assignations spécifiques vs équipe
- ✅ **Isolation** : Les tâches assignées à un membre spécifique n'apparaissent que pour ce membre
- ✅ **Visibilité équipe** : Les tâches sans `member_id` apparaissent pour tous les membres de l'équipe

## 🧪 Tests avec Postman

### Étape 1: Connexion Admin
```
POST http://127.0.0.1:8000/api/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "[MOT_DE_PASSE_ADMIN]"
}
```

### Étape 2: Récupérer les Tâches d'Équipe
```
GET http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
```

**Vérifications à faire :**
- Si `member_id` est rempli → `member_name` doit afficher le nom du membre
- Si `member_id` est vide → `member_name` doit afficher "Toute l'équipe"

### Étape 3: Récupérer les Événements d'Équipe
```
GET http://127.0.0.1:8000/api/events/
Authorization: Bearer [TOKEN]
```

**Vérifications à faire :**
- Si `member_id` est rempli → `member_name` doit afficher le nom du membre
- Si `member_id` est vide → `member_name` doit afficher "Toute l'équipe"

### Étape 4: Créer une Nouvelle Tâche avec Membre Spécifique
```
POST http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test - Tâche avec membre spécifique",
    "description": "Test de l'affichage du nom de membre",
    "start_date": "2025-05-28T10:00:00Z",
    "end_date": "2025-05-28T18:00:00Z",
    "team_id": "[TEAM_ID]",
    "member_id": "[MEMBER_ID]",
    "responsable": "[ADMIN_ID]",
    "priority": "moyenne"
}
```

**Vérification :** La réponse doit inclure le `member_name` avec le nom du membre.

### Étape 5: Créer une Nouvelle Tâche pour Toute l'Équipe
```
POST http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test - Tâche pour toute l'équipe",
    "description": "Test de l'affichage pour toute l'équipe",
    "start_date": "2025-05-28T10:00:00Z",
    "end_date": "2025-05-28T18:00:00Z",
    "team_id": "[TEAM_ID]",
    "responsable": "[ADMIN_ID]",
    "priority": "moyenne"
}
```

**Vérification :** Dans la liste des tâches, cette tâche doit afficher `member_name: "Toute l'équipe"`.

### Étape 6: Créer un Nouvel Événement avec Membre Spécifique
```
POST http://127.0.0.1:8000/api/events/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test - Événement avec membre spécifique",
    "description": "Test de l'affichage du nom de membre",
    "start_date": "2025-05-28T10:00:00Z",
    "end_date": "2025-05-28T18:00:00Z",
    "start_time": "10:00",
    "end_time": "18:00",
    "team_id": "[TEAM_ID]",
    "member_id": "[MEMBER_ID]",
    "color": "#3788d8"
}
```

**Vérification :** La réponse doit inclure le `member_name` avec le nom du membre.

### Étape 7: Tester la Modification d'Assignation
```
PUT http://127.0.0.1:8000/api/team-tasks/[TASK_ID]/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "member_id": "[NOUVEAU_MEMBER_ID]"
}
```

**Vérification :** Le `member_name` doit être automatiquement mis à jour avec le nom du nouveau membre.

### Étape 8: Test Critique - Vérifier la Visibilité pour les Employés 🔥
```
# Se connecter en tant qu'employé membre d'une équipe
POST http://127.0.0.1:8000/api/login/
Content-Type: application/json

{
    "email": "[EMAIL_EMPLOYE]",
    "password": "[MOT_DE_PASSE_EMPLOYE]"
}

# Récupérer les tâches visibles pour cet employé
GET http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN_EMPLOYE]
```

**Vérifications critiques :**
1. ✅ **Tâches assignées à toute l'équipe** (`member_id: ""`) doivent apparaître
2. ✅ **Tâches assignées spécifiquement à cet employé** doivent apparaître
3. ❌ **Tâches assignées à d'autres membres** ne doivent PAS apparaître

**Exemple de résultat attendu :**
- Tâche "Formation équipe" (member_id: "") → ✅ Visible (toute l'équipe)
- Tâche "Développement API" (member_id: "ID_CET_EMPLOYE") → ✅ Visible (assignée à lui)
- Tâche "Tests unitaires" (member_id: "ID_AUTRE_EMPLOYE") → ❌ Non visible (assignée à un autre)

## 📊 Résultats Attendus

### Exemple de Réponse Correcte - Membre Spécifique
```json
{
    "id": "...",
    "title": "Créer un tableau de bord Power BI",
    "team_name": "Equipe développement front",
    "member_id": "6813a449808d5dc234a13fff",
    "member_name": "Khalil Ben ammar",
    "responsable_name": "amine ben amor"
}
```

### Exemple de Réponse Correcte - Toute l'Équipe
```json
{
    "id": "...",
    "title": "Implémenter l'authentification avec JWT",
    "team_name": "Equipe développement front",
    "member_id": "",
    "member_name": "Toute l'équipe",
    "responsable_name": "amine ben amor"
}
```

## 🔧 Données de Test Disponibles

D'après les tests effectués, voici les données disponibles :

### Équipes
- **Agile Force** (ID: 6835fad27032f1c9d8586226)
- **Equipe développement front** (ID: 683359a2e92180867a9169d5)
- **Equipe R&D** (ID: 6835ff747032f1c9d8586229)
- **Equipe devops** (ID: 681ab5da43c4401b0d63c998)

### Membres Testés
- **Taha ben selma** (ID: 681210700f227d9df07d5c68)
- **Khalil Ben ammar** (ID: 6813a449808d5dc234a13fff)
- **salma baccouche** (ID: 6811fe142bd9630da932d4af)

## ✅ Validation Réussie

### 🧪 Résultats des Tests Automatiques

**Test Direct de la Base de Données :**
- ✅ **8 tâches d'équipe** analysées
  - 1 tâche avec `member_id` mais `member_name` vide → **Corrigée automatiquement**
  - 7 tâches assignées à toute l'équipe → **Identifiées correctement**
- ✅ **9 événements d'équipe** analysés
  - 2 événements avec `member_id` mais `member_name` vide → **Corrigés automatiquement**
  - 7 événements assignés à toute l'équipe → **Identifiés correctement**

**Exemples de Corrections Automatiques :**
1. **Tâche "test a effectuer des application web"**
   - Avant : `member_id: "681210700f227d9df07d5c68"`, `member_name: ""`
   - Après : `member_id: "681210700f227d9df07d5c68"`, `member_name: "Taha ben selma"`

2. **Événement "Réunion de lancement de projet"**
   - Avant : `member_id: "6813a449808d5dc234a13fff"`, `member_name: ""`
   - Après : `member_id: "6813a449808d5dc234a13fff"`, `member_name: "Khalil Ben ammar"`

**Test de Création :**
- ✅ Nouvelle tâche créée avec `member_name` automatiquement rempli
- ✅ Validation que le processus fonctionne de bout en bout

## 🚀 Prochaines Étapes

1. **Tester avec Postman** en utilisant ce guide
2. **Vérifier l'interface frontend** pour s'assurer que les noms s'affichent correctement
3. **Tester les modifications** d'assignation (changer de membre spécifique à toute l'équipe et vice versa)

---

**Note :** Si vous rencontrez des problèmes de connexion, vérifiez les mots de passe des admins dans la base de données ou créez un nouvel admin pour les tests.
