# Guide de Test - Affichage des Noms de Membres

## 🎯 Objectif
Vérifier que les tâches et événements d'équipe affichent correctement :
- **Le nom du membre** quand assigné à un membre spécifique
- **"Toute l'équipe"** quand assigné à toute l'équipe

## ✅ Corrections Apportées

### 1. **Création de Tâches/Événements**
- ✅ Récupération automatique du nom du membre lors de la création
- ✅ Stockage du `member_name` dans la base de données

### 2. **Affichage des Tâches/Événements**
- ✅ Logique pour distinguer membre spécifique vs toute l'équipe
- ✅ Récupération dynamique du nom si manquant
- ✅ Affichage "Toute l'équipe" pour les assignations globales

### 3. **Mise à Jour des Tâches/Événements**
- ✅ Mise à jour automatique du `member_name` lors des modifications
- ✅ Gestion des changements d'assignation

## 🧪 Tests avec Postman

### Étape 1: Connexion Admin
```
POST http://127.0.0.1:8000/api/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "[MOT_DE_PASSE_ADMIN]"
}
```

### Étape 2: Récupérer les Tâches d'Équipe
```
GET http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
```

**Vérifications à faire :**
- Si `member_id` est rempli → `member_name` doit afficher le nom du membre
- Si `member_id` est vide → `member_name` doit afficher "Toute l'équipe"

### Étape 3: Récupérer les Événements d'Équipe
```
GET http://127.0.0.1:8000/api/events/
Authorization: Bearer [TOKEN]
```

**Vérifications à faire :**
- Si `member_id` est rempli → `member_name` doit afficher le nom du membre
- Si `member_id` est vide → `member_name` doit afficher "Toute l'équipe"

### Étape 4: Créer une Nouvelle Tâche avec Membre Spécifique
```
POST http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test - Tâche avec membre spécifique",
    "description": "Test de l'affichage du nom de membre",
    "start_date": "2025-05-28T10:00:00Z",
    "end_date": "2025-05-28T18:00:00Z",
    "team_id": "[TEAM_ID]",
    "member_id": "[MEMBER_ID]",
    "responsable": "[ADMIN_ID]",
    "priority": "moyenne"
}
```

**Vérification :** La réponse doit inclure le `member_name` avec le nom du membre.

### Étape 5: Créer une Nouvelle Tâche pour Toute l'Équipe
```
POST http://127.0.0.1:8000/api/team-tasks/
Authorization: Bearer [TOKEN]
Content-Type: application/json

{
    "title": "Test - Tâche pour toute l'équipe",
    "description": "Test de l'affichage pour toute l'équipe",
    "start_date": "2025-05-28T10:00:00Z",
    "end_date": "2025-05-28T18:00:00Z",
    "team_id": "[TEAM_ID]",
    "responsable": "[ADMIN_ID]",
    "priority": "moyenne"
}
```

**Vérification :** Dans la liste des tâches, cette tâche doit afficher `member_name: "Toute l'équipe"`.

## 📊 Résultats Attendus

### Exemple de Réponse Correcte - Membre Spécifique
```json
{
    "id": "...",
    "title": "Créer un tableau de bord Power BI",
    "team_name": "Equipe développement front",
    "member_id": "6813a449808d5dc234a13fff",
    "member_name": "Khalil Ben ammar",
    "responsable_name": "amine ben amor"
}
```

### Exemple de Réponse Correcte - Toute l'Équipe
```json
{
    "id": "...",
    "title": "Implémenter l'authentification avec JWT",
    "team_name": "Equipe développement front",
    "member_id": "",
    "member_name": "Toute l'équipe",
    "responsable_name": "amine ben amor"
}
```

## 🔧 Données de Test Disponibles

D'après les tests effectués, voici les données disponibles :

### Équipes
- **Agile Force** (ID: 6835fad27032f1c9d8586226)
- **Equipe développement front** (ID: 683359a2e92180867a9169d5)
- **Equipe R&D** (ID: 6835ff747032f1c9d8586229)
- **Equipe devops** (ID: 681ab5da43c4401b0d63c998)

### Membres Testés
- **Taha ben selma** (ID: 681210700f227d9df07d5c68)
- **Khalil Ben ammar** (ID: 6813a449808d5dc234a13fff)
- **salma baccouche** (ID: 6811fe142bd9630da932d4af)

## ✅ Validation Réussie

Le test direct a confirmé que :
1. ✅ Les tâches/événements existants avec `member_id` mais sans `member_name` ont été automatiquement corrigés
2. ✅ La création de nouvelles tâches/événements fonctionne correctement
3. ✅ La logique d'affichage distingue bien membre spécifique vs toute l'équipe

## 🚀 Prochaines Étapes

1. **Tester avec Postman** en utilisant ce guide
2. **Vérifier l'interface frontend** pour s'assurer que les noms s'affichent correctement
3. **Tester les modifications** d'assignation (changer de membre spécifique à toute l'équipe et vice versa)

---

**Note :** Si vous rencontrez des problèmes de connexion, vérifiez les mots de passe des admins dans la base de données ou créez un nouvel admin pour les tests.
