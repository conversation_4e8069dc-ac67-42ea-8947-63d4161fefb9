"""
Script pour corriger les mots de passe des utilisateurs qui ont été créés avec le système de mot de passe temporaire
mais qui veulent utiliser leur propre mot de passe.
"""

from django.core.management.base import BaseCommand
from core.mongo_models import User
import getpass


class Command(BaseCommand):
    help = 'Fix user passwords for users who cannot login due to temporary password system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--email',
            type=str,
            help='Email of the user to fix',
        )
        parser.add_argument(
            '--password',
            type=str,
            help='New password for the user',
        )
        parser.add_argument(
            '--list-temp-users',
            action='store_true',
            help='List all users with temporary passwords',
        )

    def handle(self, *args, **options):
        if options['list_temp_users']:
            self.list_temp_password_users()
            return

        email = options['email']
        password = options['password']

        if not email:
            email = input("Entrez l'email de l'utilisateur: ")

        if not password:
            password = getpass.getpass("Entrez le nouveau mot de passe: ")

        self.fix_user_password(email, password)

    def list_temp_password_users(self):
        """Liste tous les utilisateurs avec des mots de passe temporaires"""
        users = User.objects.filter(temp_password_required=True)
        
        if not users:
            self.stdout.write(self.style.SUCCESS("Aucun utilisateur avec mot de passe temporaire trouvé."))
            return

        self.stdout.write(self.style.WARNING(f"Utilisateurs avec mots de passe temporaires ({len(users)}):"))
        self.stdout.write("-" * 60)
        
        for user in users:
            status = "Utilisé" if user.temp_password_used else "Non utilisé"
            self.stdout.write(f"Email: {user.email}")
            self.stdout.write(f"Nom: {user.name}")
            self.stdout.write(f"Rôle: {user.role}")
            self.stdout.write(f"Statut: {status}")
            self.stdout.write(f"Créé le: {user.created_at}")
            self.stdout.write("-" * 60)

    def fix_user_password(self, email, password):
        """Corrige le mot de passe d'un utilisateur"""
        try:
            user = User.objects.filter(email=email).first()
            
            if not user:
                self.stdout.write(self.style.ERROR(f"Utilisateur avec l'email {email} non trouvé."))
                return

            # Vérifier la longueur du mot de passe
            if len(password) < 8:
                self.stdout.write(self.style.ERROR("Le mot de passe doit contenir au moins 8 caractères."))
                return

            # Afficher les informations actuelles de l'utilisateur
            self.stdout.write(f"Utilisateur trouvé:")
            self.stdout.write(f"  Nom: {user.name}")
            self.stdout.write(f"  Email: {user.email}")
            self.stdout.write(f"  Rôle: {user.role}")
            self.stdout.write(f"  Mot de passe temporaire requis: {user.temp_password_required}")
            self.stdout.write(f"  Mot de passe temporaire utilisé: {user.temp_password_used}")

            # Demander confirmation
            confirm = input("\nVoulez-vous vraiment changer le mot de passe de cet utilisateur? (oui/non): ")
            if confirm.lower() not in ['oui', 'o', 'yes', 'y']:
                self.stdout.write(self.style.WARNING("Opération annulée."))
                return

            # Mettre à jour le mot de passe
            user.set_password(password)
            user.temp_password_required = False
            user.temp_password_used = False
            user.save()

            # Vérifier que le mot de passe fonctionne
            if user.check_password(password):
                self.stdout.write(self.style.SUCCESS(f"Mot de passe mis à jour avec succès pour {email}"))
                self.stdout.write(self.style.SUCCESS("L'utilisateur peut maintenant se connecter avec son nouveau mot de passe."))
            else:
                self.stdout.write(self.style.ERROR("Erreur: Le mot de passe n'a pas été correctement défini."))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Erreur lors de la mise à jour du mot de passe: {str(e)}"))
