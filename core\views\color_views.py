"""
Vues pour la gestion des couleurs des événements et tâches.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..utils.color_palette import (
    get_color_palette, 
    get_color_categories, 
    get_recommended_colors,
    suggest_color_by_title,
    validate_color
)
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_palette_view(request):
    """
    Récupère la palette complète de couleurs disponibles.
    
    Returns:
        - palette: Dictionnaire complet des couleurs avec noms et descriptions
        - categories: Couleurs organisées par catégories
        - recommended: Couleurs recommandées par type d'événement
    """
    try:
        palette = get_color_palette()
        categories = get_color_categories()
        recommended = get_recommended_colors()
        
        return Response({
            "success": True,
            "data": {
                "palette": palette,
                "categories": categories,
                "recommended": recommended,
                "default_color": "#3788d8"
            }
        }, status=200)
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la palette: {str(e)}")
        return Response({
            "success": False,
            "error": "Erreur lors de la récupération de la palette de couleurs"
        }, status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def suggest_color_view(request):
    """
    Suggère une couleur basée sur le titre de l'événement.
    
    Body:
        - title: Titre de l'événement
    
    Returns:
        - suggested_color: Code hexadécimal de la couleur suggérée
        - color_name: Nom de la couleur suggérée
    """
    try:
        data = request.data
        title = data.get('title', '')
        
        if not title:
            return Response({
                "success": False,
                "error": "Titre requis pour la suggestion de couleur"
            }, status=400)
        
        suggested_hex = suggest_color_by_title(title)
        
        # Trouver le nom de la couleur suggérée
        palette = get_color_palette()
        color_name = "Bleu Principal"  # Par défaut
        
        for color_key, color_info in palette.items():
            if color_info["hex"] == suggested_hex:
                color_name = color_info["name"]
                break
        
        return Response({
            "success": True,
            "data": {
                "suggested_color": suggested_hex,
                "color_name": color_name,
                "title_analyzed": title
            }
        }, status=200)
        
    except Exception as e:
        logger.error(f"Erreur lors de la suggestion de couleur: {str(e)}")
        return Response({
            "success": False,
            "error": "Erreur lors de la suggestion de couleur"
        }, status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_color_view(request):
    """
    Valide qu'une couleur est acceptable.
    
    Body:
        - color: Code couleur à valider (hex ou nom de la palette)
    
    Returns:
        - is_valid: Boolean indiquant si la couleur est valide
        - message: Message de validation
        - hex_code: Code hexadécimal de la couleur (si valide)
    """
    try:
        data = request.data
        color = data.get('color', '')
        
        if not color:
            return Response({
                "success": False,
                "error": "Couleur requise pour la validation"
            }, status=400)
        
        is_valid, message = validate_color(color)
        
        response_data = {
            "success": True,
            "data": {
                "is_valid": is_valid,
                "message": message,
                "color_input": color
            }
        }
        
        if is_valid:
            # Ajouter le code hex si la couleur est valide
            from ..utils.color_palette import get_color_hex
            response_data["data"]["hex_code"] = get_color_hex(color)
        
        return Response(response_data, status=200)
        
    except Exception as e:
        logger.error(f"Erreur lors de la validation de couleur: {str(e)}")
        return Response({
            "success": False,
            "error": "Erreur lors de la validation de couleur"
        }, status=500)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_categories_view(request):
    """
    Récupère les couleurs organisées par catégories.
    
    Returns:
        - categories: Couleurs organisées par catégories (urgence, statut, type, personnel)
    """
    try:
        categories = get_color_categories()
        palette = get_color_palette()
        
        # Enrichir les catégories avec les informations complètes des couleurs
        enriched_categories = {}
        
        for category_name, color_keys in categories.items():
            enriched_categories[category_name] = []
            for color_key in color_keys:
                if color_key in palette:
                    color_info = palette[color_key].copy()
                    color_info["key"] = color_key
                    enriched_categories[category_name].append(color_info)
        
        return Response({
            "success": True,
            "data": {
                "categories": enriched_categories
            }
        }, status=200)
        
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des catégories: {str(e)}")
        return Response({
            "success": False,
            "error": "Erreur lors de la récupération des catégories de couleurs"
        }, status=500)
