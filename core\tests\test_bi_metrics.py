from django.test import TestCase
from rest_framework.test import APIClient
from ..mongo_models import User, Team
from ..models.event_model import Event
from ..models.personal_event_model import PersonalEvent
from ..models.team_task_model import TeamTask
from ..models.personal_task_model import PersonalTask
from ..models.bi_model import BiMetric, BiDashboard
from ..tasks.bi_tasks import update_bi_metrics
from datetime import datetime, timezone, timedelta
import json

class BiMetricsTestCase(TestCase):
    def setUp(self):
        # Créer des utilisateurs de test
        self.super_admin = User(
            name="Super Admin",
            email="<EMAIL>",
            password="password123",
            role="super_admin"
        )
        self.super_admin.save()
        
        self.admin = User(
            name="Admin",
            email="<EMAIL>",
            password="password123",
            role="admin"
        )
        self.admin.save()
        
        self.employee = User(
            name="Employee",
            email="<EMAIL>",
            password="password123",
            role="employee"
        )
        self.employee.save()
        
        self.client_user = User(
            name="Client",
            email="<EMAIL>",
            password="password123",
            role="client"
        )
        self.client_user.save()
        
        # Créer une équipe
        self.team = Team(
            name="Test Team",
            description="Test Team Description",
            responsable=str(self.admin.id),
            members=[str(self.employee.id)]
        )
        self.team.save()
        
        # Créer des tâches d'équipe
        self.team_task_1 = TeamTask(
            title="Team Task 1",
            description="Team Task 1 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            status="a_faire",
            priority="moyenne",
            team_id=str(self.team.id),
            team_name=self.team.name,
            member_id=str(self.employee.id),
            responsable=str(self.admin.id),
            responsable_name=self.admin.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_task_1.save()
        
        self.team_task_2 = TeamTask(
            title="Team Task 2",
            description="Team Task 2 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            status="en_cours",
            priority="haute",
            team_id=str(self.team.id),
            team_name=self.team.name,
            member_id=str(self.employee.id),
            responsable=str(self.admin.id),
            responsable_name=self.admin.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_task_2.save()
        
        self.team_task_3 = TeamTask(
            title="Team Task 3",
            description="Team Task 3 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            status="achevee",
            priority="faible",
            team_id=str(self.team.id),
            team_name=self.team.name,
            member_id=str(self.employee.id),
            responsable=str(self.admin.id),
            responsable_name=self.admin.name,
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_task_3.save()
        
        # Créer des événements d'équipe
        self.team_event_1 = Event(
            title="Team Event 1",
            description="Team Event 1 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            start_time="09:00",
            end_time="10:00",
            status="pending",
            team_id=str(self.team.id),
            team_name=self.team.name,
            member_id=str(self.employee.id),
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_event_1.save()
        
        self.team_event_2 = Event(
            title="Team Event 2",
            description="Team Event 2 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            start_time="11:00",
            end_time="12:00",
            status="completed",
            team_id=str(self.team.id),
            team_name=self.team.name,
            member_id=str(self.employee.id),
            created_by=str(self.admin.id),
            created_by_name=self.admin.name
        )
        self.team_event_2.save()
        
        # Créer des tâches personnelles
        self.personal_task_1 = PersonalTask(
            title="Personal Task 1",
            description="Personal Task 1 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            status="a_faire",
            priority="moyenne",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_task_1.save()
        
        self.personal_task_2 = PersonalTask(
            title="Personal Task 2",
            description="Personal Task 2 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            status="achevee",
            priority="haute",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_task_2.save()
        
        # Créer des événements personnels
        self.personal_event_1 = PersonalEvent(
            title="Personal Event 1",
            description="Personal Event 1 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            start_time="09:00",
            end_time="10:00",
            status="pending",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_event_1.save()
        
        self.personal_event_2 = PersonalEvent(
            title="Personal Event 2",
            description="Personal Event 2 Description",
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=7),
            start_time="11:00",
            end_time="12:00",
            status="completed",
            created_by=str(self.employee.id),
            created_by_name=self.employee.name
        )
        self.personal_event_2.save()
        
        # Mettre à jour les métriques BI
        update_bi_metrics()
        
        # Créer un client API
        self.client = APIClient()
    
    def test_super_admin_metrics(self):
        """
        Test des métriques pour les super admins
        """
        # Authentifier le super admin
        self.client.force_authenticate(user=self.super_admin)
        
        # Récupérer les métriques
        response = self.client.get('/api/bi/metrics/')
        self.assertEqual(response.status_code, 200)
        
        # Vérifier les métriques
        data = response.json()
        self.assertIn('user_activity', data)
        self.assertIn('platform_activity', data)
        
        # Vérifier les métriques d'activité des utilisateurs
        user_activity = data['user_activity']
        self.assertEqual(user_activity['total_users'], 4)
        self.assertIn('active_users', user_activity)
        self.assertIn('active_percentage', user_activity)
        self.assertIn('users_by_role', user_activity)
        
        # Vérifier les métriques d'activité de la plateforme
        platform_activity = data['platform_activity']
        self.assertEqual(platform_activity['total_teams'], 1)
        self.assertEqual(platform_activity['total_events'], 2)
        self.assertEqual(platform_activity['total_personal_events'], 2)
        self.assertEqual(platform_activity['total_team_tasks'], 3)
        self.assertEqual(platform_activity['total_personal_tasks'], 2)
    
    def test_admin_metrics(self):
        """
        Test des métriques pour les admins
        """
        # Authentifier l'admin
        self.client.force_authenticate(user=self.admin)
        
        # Récupérer les métriques
        response = self.client.get('/api/bi/metrics/')
        self.assertEqual(response.status_code, 200)
        
        # Vérifier les métriques
        data = response.json()
        self.assertIn('teams_count', data)
        self.assertIn('teams', data)
        
        # Vérifier les métriques d'équipe
        teams = data['teams']
        self.assertEqual(len(teams), 1)
        
        team = teams[0]
        self.assertEqual(team['team_id'], str(self.team.id))
        self.assertEqual(team['team_name'], self.team.name)
        self.assertEqual(team['members_count'], 1)
        
        # Vérifier les métriques des tâches d'équipe
        tasks = team['tasks']
        self.assertEqual(tasks['total'], 3)
        self.assertIn('by_status', tasks)
        self.assertIn('completion_rate', tasks)
        
        # Vérifier les métriques des événements d'équipe
        events = team['events']
        self.assertEqual(events['total'], 2)
        self.assertIn('by_status', events)
        self.assertIn('completion_rate', events)
    
    def test_employee_metrics(self):
        """
        Test des métriques pour les employés
        """
        # Authentifier l'employé
        self.client.force_authenticate(user=self.employee)
        
        # Récupérer les métriques
        response = self.client.get('/api/bi/metrics/')
        self.assertEqual(response.status_code, 200)
        
        # Vérifier les métriques
        data = response.json()
        self.assertIn('personal_tasks', data)
        self.assertIn('personal_events', data)
        self.assertIn('team_tasks', data)
        self.assertIn('team_events', data)
        
        # Vérifier les métriques des tâches personnelles
        personal_tasks = data['personal_tasks']
        self.assertEqual(personal_tasks['total'], 2)
        self.assertIn('by_status', personal_tasks)
        self.assertIn('completion_rate', personal_tasks)
        
        # Vérifier les métriques des événements personnels
        personal_events = data['personal_events']
        self.assertEqual(personal_events['total'], 2)
        self.assertIn('by_status', personal_events)
        self.assertIn('completion_rate', personal_events)
        
        # Vérifier les métriques des tâches d'équipe
        team_tasks = data['team_tasks']
        self.assertEqual(team_tasks['total'], 3)
        self.assertIn('by_status', team_tasks)
        self.assertIn('completion_rate', team_tasks)
        
        # Vérifier les métriques des événements d'équipe
        team_events = data['team_events']
        self.assertEqual(team_events['total'], 2)
        self.assertIn('by_status', team_events)
        self.assertIn('completion_rate', team_events)
    
    def test_dashboard(self):
        """
        Test du tableau de bord BI
        """
        # Authentifier l'employé
        self.client.force_authenticate(user=self.employee)
        
        # Récupérer le tableau de bord
        response = self.client.get('/api/bi/dashboard/')
        self.assertEqual(response.status_code, 200)
        
        # Vérifier le tableau de bord
        data = response.json()
        self.assertIn('id', data)
        self.assertIn('title', data)
        self.assertIn('dashboard_type', data)
        self.assertIn('layout', data)
        self.assertIn('metrics', data)
        
        # Vérifier le type de tableau de bord
        self.assertEqual(data['dashboard_type'], 'employee')
        
        # Vérifier les métriques du tableau de bord
        metrics = data['metrics']
        self.assertIn('personal_tasks', metrics)
        self.assertIn('personal_events', metrics)
        self.assertIn('team_tasks', metrics)
        self.assertIn('team_events', metrics)
