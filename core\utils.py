from django.core.mail import send_mail
from django.conf import settings
import random
import string
import uuid
from datetime import datetime, timed<PERSON><PERSON>

def generate_temp_password(length=10):
    characters = string.ascii_letters + string.digits + string.punctuation
    while True:
        password = ''.join(random.choice(characters) for i in range(length))
        if (any(c.isdigit() for c in password) and 
            any(c in string.punctuation for c in password)):
            return password

def generate_reset_token():
    return str(uuid.uuid4())

def send_temp_password_email(user_email, temp_password, name):
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        subject = 'Votre mot de passe temporaire'
        from_email = settings.DEFAULT_FROM_EMAIL
        app_url = settings.FRONTEND_URL
        
        logger.debug(f"Préparation de l'email avec mot de passe temporaire pour {user_email}")
        logger.debug(f"Utilisation de l'adresse d'expédition: {from_email}")
        
        # C<PERSON>er le contenu HTML de l'email
        from django.template.loader import render_to_string
        from django.utils.html import strip_tags
        
        html_message = render_to_string('emails/temp_password_email.html', {
            'name': name,
            'temp_password': temp_password,
            'app_url': app_url
        })
        
        # Version texte brut de l'email
        plain_message = strip_tags(html_message)
        
        logger.debug("Tentative d'envoi de l'email...")
        
        # Envoyer l'email
        send_mail(
            subject,
            plain_message,
            from_email,
            [user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.debug("Email envoyé avec succès")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
        raise Exception(f"Erreur lors de l'envoi de l'email: {str(e)}")

def send_reset_password_email(user_email, reset_token, name):
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # Construction du lien de réinitialisation avec le bon format
        reset_link = f"{settings.FRONTEND_URL}/reset-password/{reset_token}/"
        subject = 'Demande de réinitialisation de mot de passe'
        from_email = settings.DEFAULT_FROM_EMAIL
        
        logger.debug(f"Préparation de l'email de réinitialisation pour {user_email}")
        logger.debug(f"Lien de réinitialisation généré: {reset_link}")
        
        # Créer le contenu HTML de l'email
        from django.template.loader import render_to_string
        from django.utils.html import strip_tags
        
        html_message = render_to_string('emails/reset_password_email.html', {
            'name': name,
            'reset_link': reset_link
        })
        
        # Version texte brut de l'email
        plain_message = strip_tags(html_message)
        
        logger.debug("Tentative d'envoi de l'email...")
        
        # Envoyer l'email
        send_mail(
            subject,
            plain_message,
            from_email,
            [user_email],
            html_message=html_message,
            fail_silently=False,
        )
        
        logger.debug("Email envoyé avec succès")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation: {str(e)}")
        raise Exception(f"Erreur lors de l'envoi de l'email de réinitialisation: {str(e)}")