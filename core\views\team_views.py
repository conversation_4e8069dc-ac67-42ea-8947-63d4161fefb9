from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import User, Team
from mongoengine.errors import ValidationError, NotUniqueError
import logging

logger = logging.getLogger(__name__)

class TeamListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request):
        """Créer une nouvelle équipe"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('name'):
                return Response({"error": "Le nom de l'équipe est requis"}, status=400)

            # Vérifier si le nom est unique
            if Team.objects(name=data['name']).first():
                return Response({"error": "Une équipe avec ce nom existe déjà"}, status=400)

            # Créer l'équipe
            team = Team(
                name=data['name'],
                description=data.get('description', ''),
                responsable=str(user.id),
                responsable_name=user.name
            )
            team.save()

            return Response({
                "message": "Équipe créée avec succès",
                "team": {
                    "id": str(team.id),
                    "name": team.name,
                    "description": team.description,
                    "responsable": {
                        "id": team.responsable,
                        "name": team.responsable_name
                    },
                    "members": [],
                    "created_at": team.created_at,
                    "updated_at": team.updated_at,
                    "can_manage": True,
                    "is_responsable": True
                }
            }, status=201)

        except ValidationError as e:
            logger.error(f"Validation error in TeamListCreateView.post: {str(e)}")
            return Response({"error": "Données invalides", "detail": str(e)}, status=400)
        except NotUniqueError as e:
            logger.error(f"Uniqueness error in TeamListCreateView.post: {str(e)}")
            return Response({"error": "Une équipe avec ces informations existe déjà"}, status=400)
        except Exception as e:
            logger.error(f"Error in TeamListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de l'équipe"}, status=500)

    def get(self, request):
        """Liste des équipes (filtrée selon le rôle)"""
        try:
            user = request.user

            # Les super_admin et admins voient toutes les équipes
            if user.role in ['super_admin', 'admin']:
                teams = Team.objects.all()
            # Les employés voient uniquement leurs équipes
            else:
                user_id = str(user.id)
                # Ajout de logs pour déboguer
                logger.debug(f"Recherche d'équipes pour l'employé ID: {user_id}")
                logger.debug(f"Type de l'ID utilisateur: {type(user_id)}")

                # Vérifier si l'utilisateur existe dans des équipes
                all_teams = Team.objects.all()
                for team in all_teams:
                    logger.debug(f"Équipe {team.name} - membres: {team.members.keys()}")
                    if user_id in team.members:
                        logger.debug(f"L'utilisateur {user_id} est membre de l'équipe {team.name}")

                # Modification de la requête pour chercher dans les membres avec plus de flexibilité
                teams = Team.objects.filter(__raw__={'members.' + user_id: {'$exists': True}})
                logger.debug(f"Nombre d'équipes trouvées: {len(teams)}")

                # Si aucune équipe n'est trouvée, essayer une autre approche
                if len(teams) == 0:
                    logger.debug("Tentative avec une autre méthode de requête")
                    teams = Team.objects()
                    teams = [team for team in teams if user_id in team.members]
                    logger.debug(f"Nombre d'équipes trouvées avec la méthode alternative: {len(teams)}")

            teams_data = []
            for team in teams:
                # Préparer les détails des membres
                member_details = []
                for member_id, member_info in team.members.items():
                    if isinstance(member_info, dict):
                        member_details.append({
                            'id': member_id,
                            'name': member_info.get('name', ''),
                            'role': member_info.get('role', 'employee'),
                            'added_at': member_info.get('added_at')
                        })
                    else:
                        member_details.append({
                            'id': member_id,
                            'name': member_info,
                            'role': 'employee',
                            'added_at': None
                        })

                teams_data.append({
                    'id': str(team.id),
                    'name': team.name,
                    'description': team.description,
                    'responsable': {
                        'id': team.responsable,
                        'name': team.responsable_name
                    },
                    'members': member_details,
                    'created_at': team.created_at,
                    'updated_at': team.updated_at,
                    'can_manage': team.can_manage_team(user),
                    'is_member': str(user.id) in team.members,
                    'is_responsable': str(user.id) == team.responsable
                })

            return Response(teams_data)

        except Exception as e:
            logger.error(f"Error in TeamListCreateView.get: {str(e)}")
            return Response({'error': str(e)}, status=500)

class TeamDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, team_id):
        """Récupérer les détails d'une équipe"""
        try:
            # Récupérer l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier les droits de visualisation
            if not team.can_view_team(request.user):
                return Response({"error": "Vous n'avez pas les droits pour voir cette équipe"}, status=403)

            # Préparer les détails des membres
            member_details = []
            for member_id, member_info in team.members.items():
                if isinstance(member_info, dict):
                    member_details.append({
                        'id': member_id,
                        'name': member_info.get('name', ''),
                        'role': member_info.get('role', 'employee'),
                        'added_at': member_info.get('added_at')
                    })
                else:
                    member_details.append({
                        'id': member_id,
                        'name': member_info,
                        'role': 'employee',
                        'added_at': None
                    })

            # Retourner les détails de l'équipe
            return Response({
                'id': str(team.id),
                'name': team.name,
                'description': team.description,
                'responsable': {
                    'id': team.responsable,
                    'name': team.responsable_name
                },
                'members': member_details,
                'created_at': team.created_at,
                'updated_at': team.updated_at,
                'can_manage': team.can_manage_team(request.user),
                'is_member': str(request.user.id) in team.members,
                'is_responsable': str(request.user.id) == team.responsable
            })

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamDetailView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la récupération des détails de l'équipe"}, status=500)

    @admin_required
    def put(self, request, team_id):
        """Modifier une équipe"""
        try:
            # Récupérer l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier les droits
            if not team.can_manage_team(request.user):
                return Response({"error": "Vous n'avez pas les droits pour modifier cette équipe"}, status=403)

            data = request.data

            # Vérifier si le nom est fourni et unique
            if 'name' in data:
                if not data['name']:
                    return Response({"error": "Le nom de l'équipe est requis"}, status=400)

                # Vérifier si le nouveau nom existe déjà pour une autre équipe
                existing_team = Team.objects(name=data['name'], id__ne=team_id).first()
                if existing_team:
                    return Response({"error": "Une équipe avec ce nom existe déjà"}, status=400)

                team.name = data['name']

            # Mettre à jour la description si fournie
            if 'description' in data:
                team.description = data['description']

            team.save()

            return Response({
                "message": "Équipe modifiée avec succès",
                "team": {
                    "id": str(team.id),
                    "name": team.name,
                    "description": team.description,
                    "responsable": {
                        "id": team.responsable,
                        "name": team.responsable_name
                    },
                    "created_at": team.created_at,
                    "updated_at": team.updated_at
                }
            })

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except ValidationError as e:
            logger.error(f"Validation error in TeamDetailView.put: {str(e)}")
            return Response({"error": "Données invalides", "detail": str(e)}, status=400)
        except Exception as e:
            logger.error(f"Error in TeamDetailView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la modification de l'équipe"}, status=500)

    @admin_required
    def delete(self, request, team_id):
        """Supprimer une équipe"""
        try:
            # Récupérer l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier les droits
            if not team.can_manage_team(request.user):
                return Response({"error": "Vous n'avez pas les droits pour supprimer cette équipe"}, status=403)

            # Effectuer la suppression en cascade (événements, tâches, etc.)
            team_name = team.name
            team.cascade_delete()

            # Supprimer l'équipe
            team.delete()

            return Response({
                "message": f"Équipe '{team_name}' et toutes ses données associées (événements, tâches) supprimées avec succès"
            })

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'équipe: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression de l'équipe"}, status=500)

class TeamMemberView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, team_id):
        # Code existant pour la liste des membres
        pass

    @admin_required
    def post(self, request, team_id):
        # Code existant pour l'ajout d'un membre
        pass

    @admin_required
    def delete(self, request, team_id, member_id):
        try:
            # Vérifier que les IDs sont valides
            if not team_id or not member_id:
                return Response({"error": "Les identifiants de l'équipe et du membre sont requis"}, status=400)

            # Récupérer l'équipe
            team = Team.objects.get(id=team_id)

            # Vérifier d'abord si le membre existe dans l'équipe
            if member_id not in team.members:
                return Response({"error": "Ce membre n'appartient pas à l'équipe"}, status=404)

            # Vérifier que l'utilisateur est un admin
            if not request.user.role == 'admin':
                return Response({"error": "Seul un administrateur peut gérer les membres d'une équipe"}, status=403)

            # Vérifier que l'utilisateur est le responsable de l'équipe
            if str(request.user.id) != team.responsable:
                return Response({"error": "Seul l'administrateur responsable de l'équipe peut gérer ses membres"}, status=403)

            # Supprimer directement le membre du dictionnaire et sauvegarder
            try:
                del team.members[member_id]
                team.save()
                return Response({"message": "Membre retiré avec succès"}, status=200)
            except Exception as e:
                logger.error(f"Erreur lors de la suppression du membre: {str(e)}")
                return Response({"error": "Erreur lors de la suppression du membre"}, status=500)

        except Team.DoesNotExist:
            return Response({"error": "Équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de la suppression du membre: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression"}, status=500)

class TeamResponsableView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, team_id):
        # Code existant pour le changement de responsable
        pass