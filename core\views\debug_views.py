from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..mongo_models import User
from ..utils import generate_temp_password
import logging

logger = logging.getLogger(__name__)

class DebugEmployeesView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Vue de débogage pour vérifier les employés dans la base de données"""
        try:
            # Récupérer tous les utilisateurs
            all_users = User.objects.all()
            total_users = len(all_users)

            # Récupérer les employés
            employees = User.objects(role='employee')
            total_employees = len(employees)

            # Préparer les données de réponse
            user_data = []
            for user in all_users:
                user_data.append({
                    'id': str(user.id),
                    'name': user.name,
                    'email': user.email,
                    'role': user.role,
                    'created_at': user.created_at,
                    'last_login': user.last_login
                })

            # Préparer les données des employés
            employee_data = []
            for employee in employees:
                employee_data.append({
                    'id': str(employee.id),
                    'name': employee.name,
                    'email': employee.email,
                    'created_at': employee.created_at,
                    'last_login': employee.last_login
                })

            return Response({
                'total_users': total_users,
                'total_employees': total_employees,
                'users': user_data,
                'employees': employee_data
            })
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des employés: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def post(self, request):
        """Créer un employé de test pour le débogage"""
        try:
            # Vérifier si l'employé de test existe déjà
            test_email = "<EMAIL>"
            existing_user = User.objects(email=test_email).first()

            if existing_user:
                return Response({
                    "message": "L'employé de test existe déjà",
                    "user": {
                        "id": str(existing_user.id),
                        "name": existing_user.name,
                        "email": existing_user.email,
                        "role": existing_user.role
                    }
                })

            # Créer un nouvel employé de test
            temp_password = generate_temp_password()
            user = User(
                name="Employé Test",
                email=test_email,
                role='employee',
                temp_password_required=True
            )
            user.set_password(temp_password)
            user.save()

            return Response({
                "message": "Employé de test créé avec succès",
                "user": {
                    "id": str(user.id),
                    "name": user.name,
                    "email": user.email,
                    "role": user.role
                },
                "password": temp_password  # Uniquement pour le débogage
            }, status=201)

        except Exception as e:
            logger.error(f"Erreur lors du débogage des employés: {str(e)}")
            return Response({"error": str(e)}, status=500)
