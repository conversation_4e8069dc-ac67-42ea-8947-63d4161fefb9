from mongoengine import Document, String<PERSON>ield, DateTimeField, ReferenceField, BooleanField, ObjectIdField
from datetime import datetime, timezone
from bson import ObjectId

class Event(Document):
    id = StringField(primary_key=True, default=lambda: str(ObjectId()))
    title = StringField(required=True)
    description = StringField(required=False)
    start_date = DateTimeField(required=True)
    end_date = DateTimeField(required=True)
    start_time = StringField(required=True)  # Format HH:MM
    end_time = StringField(required=True)    # Format HH:MM
    note = StringField(required=False)
    status = StringField(default='pending', choices=['pending', 'completed', 'archived'])
    color = StringField(required=False, default="#3788d8")  # Couleur de l'événement au format hexadécimal
    team_id = StringField(required=False)  # ID de l'équipe si l'événement est assigné à une équipe
    team_name = StringField(required=False)  # Nom de l'équipe si l'événement est assigné à une équipe
    member_id = StringField(required=False, default="")  # ID du membre si l'événement est assigné à un membre spécifique
    member_name = StringField(required=False, default="")  # Nom du membre si l'événement est assigné à un membre spécifique
    created_by = StringField(required=True, default="")  # ID de l'admin qui a créé l'événement
    created_by_name = StringField(required=False, default="")  # Nom de l'admin qui a créé l'événement
    created_at = DateTimeField(default=datetime.now(timezone.utc))
    updated_at = DateTimeField(default=datetime.now(timezone.utc))

    meta = {
        'collection': 'events',
        'indexes': [
            {'fields': ['team_id']},
            {'fields': ['member_id']},
            {'fields': ['start_date']},
            {'fields': ['status']}
        ]
    }

    def save(self, *args, **kwargs):
        # Définir created_at uniquement à la création
        if not self.created_at:
            self.created_at = datetime.now(timezone.utc)
            # À la création, updated_at est identique à created_at
            self.updated_at = self.created_at
        else:
            # Mise à jour de updated_at lors des modifications
            if self._get_changed_fields():
                self.updated_at = datetime.now(timezone.utc)
        return super(Event, self).save(*args, **kwargs)

    def can_manage_event(self, user):
        """Vérifie si un utilisateur peut gérer cet événement"""
        # Seuls les admins responsables de l'équipe associée à l'événement peuvent le gérer
        if user.role == 'admin':
            # Si l'événement est associé à une équipe, vérifier si l'admin est responsable de cette équipe
            if self.team_id:
                from ..mongo_models import Team
                try:
                    team = Team.objects.get(id=self.team_id)
                    # Vérifier si l'admin est le responsable de l'équipe
                    return str(user.id) == team.responsable
                except Team.DoesNotExist:
                    # L'équipe n'existe pas
                    return False
                except Exception:
                    # Autre erreur lors de la vérification de l'équipe
                    return False
            # Si l'événement est associé à un membre spécifique, l'admin peut le gérer
            # si ce membre fait partie d'une équipe dont il est responsable
            elif self.member_id:
                from ..mongo_models import Team
                # Trouver les équipes dont l'admin est responsable
                admin_teams = Team.objects(responsable=str(user.id)).values_list('id')
                admin_team_ids = [str(team_id) for team_id in admin_teams]

                # Vérifier si le membre fait partie d'une de ces équipes
                for team_id in admin_team_ids:
                    try:
                        team = Team.objects.get(id=team_id)
                        if self.member_id in team.members:
                            return True
                    except Exception:
                        continue
                return False
            # Si l'événement n'est associé ni à une équipe ni à un membre, l'admin ne peut pas le gérer
            return False
        return False

    def can_update_status(self, user):
        """Vérifie si un utilisateur peut mettre à jour le statut de l'événement"""
        # Les admins ne peuvent pas mettre à jour le statut, ils peuvent seulement consulter
        if user.role == 'admin':
            return False
        # Les employés peuvent mettre à jour le statut s'ils sont assignés à l'événement
        if user.role == 'employee':
            # Si l'événement est assigné à un membre spécifique
            if self.member_id and self.member_id == str(user.id):
                return True
            # Si l'événement est assigné à une équipe dont l'utilisateur est membre
            if self.team_id:
                from ..mongo_models import Team
                try:
                    team = Team.objects.get(id=self.team_id)
                    return str(user.id) in team.members
                except Team.DoesNotExist:
                    # L'équipe n'existe pas
                    return False
                except Exception:
                    # Autre erreur lors de la vérification de l'équipe
                    return False
        return False