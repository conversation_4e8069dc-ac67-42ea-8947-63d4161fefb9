"""
Script pour créer des utilisateurs de test pour le tableau de bord super admin
À exécuter dans le shell Django : python manage.py shell
"""

from core.mongo_models import User
from datetime import datetime, timezone, timedelta
import random

def create_test_users_for_dashboard():
    """
    Crée des utilisateurs de test avec différents statuts d'activité
    pour tester le tableau de bord super admin
    """
    
    print("🚀 Création d'utilisateurs de test pour le tableau de bord...")
    
    # Dates de référence
    now = datetime.now(timezone.utc)
    one_day_ago = now - timedelta(days=1)
    seven_days_ago = now - timedelta(days=7)
    thirty_days_ago = now - timedelta(days=30)
    ninety_days_ago = now - timedelta(days=90)
    
    # Définir les utilisateurs de test
    test_users = [
        # Super Admins (3 total)
        {
            'name': 'Super Admin Principal',
            'email': '<EMAIL>',
            'role': 'super_admin',
            'last_login': now - timedelta(hours=2),  # Actif récent
            'created_at': now - timedelta(days=365)
        },
        {
            'name': 'Super Admin Secondaire',
            'email': '<EMAIL>',
            'role': 'super_admin',
            'last_login': now - timedelta(days=5),  # Actif 7j
            'created_at': now - timedelta(days=200)
        },
        {
            'name': 'Super Admin Backup',
            'email': '<EMAIL>',
            'role': 'super_admin',
            'last_login': None,  # Jamais connecté
            'created_at': now - timedelta(days=30)
        },
        
        # Administrateurs (9 total)
        {
            'name': 'Admin Équipe Marketing',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(hours=6),  # Actif récent
            'created_at': now - timedelta(days=120)
        },
        {
            'name': 'Admin Équipe Développement',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=2),  # Actif 7j
            'created_at': now - timedelta(days=90)
        },
        {
            'name': 'Admin Équipe Design',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=10),  # Actif 30j
            'created_at': now - timedelta(days=150)
        },
        {
            'name': 'Admin Équipe Support',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=45),  # Inactif
            'created_at': now - timedelta(days=180)
        },
        {
            'name': 'Admin Équipe Ventes',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=1),  # Actif récent
            'created_at': now - timedelta(days=60)
        },
        {
            'name': 'Admin Équipe RH',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=20),  # Actif 30j
            'created_at': now - timedelta(days=100)
        },
        {
            'name': 'Admin Équipe Finance',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': None,  # Jamais connecté
            'created_at': now - timedelta(days=15)
        },
        {
            'name': 'Admin Équipe Logistique',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=3),  # Actif 7j
            'created_at': now - timedelta(days=75)
        },
        {
            'name': 'Admin Équipe Qualité',
            'email': '<EMAIL>',
            'role': 'admin',
            'last_login': now - timedelta(days=60),  # Inactif
            'created_at': now - timedelta(days=200)
        },
        
        # Employés (14 total)
        {
            'name': 'Employé Marketing 1',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(hours=1),  # Très actif
            'created_at': now - timedelta(days=45)
        },
        {
            'name': 'Employé Marketing 2',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=4),  # Actif 7j
            'created_at': now - timedelta(days=80)
        },
        {
            'name': 'Développeur Frontend',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(hours=8),  # Actif récent
            'created_at': now - timedelta(days=120)
        },
        {
            'name': 'Développeur Backend',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=1),  # Actif récent
            'created_at': now - timedelta(days=95)
        },
        {
            'name': 'Designer UX/UI',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=15),  # Actif 30j
            'created_at': now - timedelta(days=110)
        },
        {
            'name': 'Analyste Données',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=25),  # Actif 30j
            'created_at': now - timedelta(days=140)
        },
        {
            'name': 'Spécialiste Support',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=50),  # Inactif
            'created_at': now - timedelta(days=160)
        },
        {
            'name': 'Commercial Senior',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(hours=12),  # Actif récent
            'created_at': now - timedelta(days=70)
        },
        {
            'name': 'Gestionnaire RH',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=6),  # Actif 7j
            'created_at': now - timedelta(days=85)
        },
        {
            'name': 'Comptable',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=12),  # Actif 30j
            'created_at': now - timedelta(days=130)
        },
        {
            'name': 'Coordinateur Logistique',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': None,  # Jamais connecté
            'created_at': now - timedelta(days=20)
        },
        {
            'name': 'Testeur QA',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=8),  # Actif 30j
            'created_at': now - timedelta(days=105)
        },
        {
            'name': 'Rédacteur Contenu',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=40),  # Inactif
            'created_at': now - timedelta(days=170)
        },
        {
            'name': 'Photographe',
            'email': '<EMAIL>',
            'role': 'employee',
            'last_login': now - timedelta(days=2),  # Actif 7j
            'created_at': now - timedelta(days=55)
        },
        
        # Clients (12 total)
        {
            'name': 'Client Entreprise A',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(hours=3),  # Très actif
            'created_at': now - timedelta(days=30)
        },
        {
            'name': 'Client Startup B',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=5),  # Actif 7j
            'created_at': now - timedelta(days=60)
        },
        {
            'name': 'Client Freelance C',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=18),  # Actif 30j
            'created_at': now - timedelta(days=90)
        },
        {
            'name': 'Client PME D',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=35),  # Inactif
            'created_at': now - timedelta(days=120)
        },
        {
            'name': 'Client Association E',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(hours=24),  # Actif récent
            'created_at': now - timedelta(days=45)
        },
        {
            'name': 'Client Consultant F',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=10),  # Actif 30j
            'created_at': now - timedelta(days=75)
        },
        {
            'name': 'Client Agence G',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': None,  # Jamais connecté
            'created_at': now - timedelta(days=10)
        },
        {
            'name': 'Client Particulier H',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=7),  # Actif 30j
            'created_at': now - timedelta(days=100)
        },
        {
            'name': 'Client E-commerce I',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=50),  # Inactif
            'created_at': now - timedelta(days=150)
        },
        {
            'name': 'Client Tech J',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=3),  # Actif 7j
            'created_at': now - timedelta(days=65)
        },
        {
            'name': 'Client Retail K',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(days=22),  # Actif 30j
            'created_at': now - timedelta(days=110)
        },
        {
            'name': 'Client International L',
            'email': '<EMAIL>',
            'role': 'client',
            'last_login': now - timedelta(hours=6),  # Très actif
            'created_at': now - timedelta(days=25)
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for user_data in test_users:
        try:
            # Vérifier si l'utilisateur existe déjà
            existing_user = User.objects(email=user_data['email']).first()
            
            if existing_user:
                # Mettre à jour l'utilisateur existant
                existing_user.name = user_data['name']
                existing_user.role = user_data['role']
                existing_user.last_login = user_data['last_login']
                existing_user.created_at = user_data['created_at']
                existing_user.save()
                updated_count += 1
                print(f"✅ Mis à jour: {user_data['name']} ({user_data['role']})")
            else:
                # Créer un nouvel utilisateur
                user = User(
                    name=user_data['name'],
                    email=user_data['email'],
                    password='password123',  # Mot de passe par défaut
                    role=user_data['role'],
                    last_login=user_data['last_login'],
                    created_at=user_data['created_at']
                )
                user.save()
                created_count += 1
                print(f"🆕 Créé: {user_data['name']} ({user_data['role']})")
                
        except Exception as e:
            print(f"❌ Erreur pour {user_data['name']}: {str(e)}")
    
    print(f"\n📊 Résumé:")
    print(f"   - Utilisateurs créés: {created_count}")
    print(f"   - Utilisateurs mis à jour: {updated_count}")
    print(f"   - Total traité: {len(test_users)}")
    
    # Afficher les statistiques finales
    total_users = User.objects.count()
    active_30d = User.objects(last_login__gte=thirty_days_ago).count()
    never_logged = User.objects(last_login__exists=False).count()
    
    print(f"\n📈 Statistiques finales:")
    print(f"   - Total utilisateurs: {total_users}")
    print(f"   - Actifs (30j): {active_30d}")
    print(f"   - Jamais connectés: {never_logged}")
    print(f"   - Super admins: {User.objects(role='super_admin').count()}")
    print(f"   - Admins: {User.objects(role='admin').count()}")
    print(f"   - Employés: {User.objects(role='employee').count()}")
    print(f"   - Clients: {User.objects(role='client').count()}")
    
    print(f"\n🎯 Prêt pour tester le tableau de bord!")
    print(f"   URL: GET http://localhost:8000/api/bi/super-admin/dashboard/")

if __name__ == "__main__":
    create_test_users_for_dashboard()
