from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.personal_task_model import PersonalTask
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class PersonalTaskListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Créer une nouvelle tâche personnelle (employé ou client)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de la tâche est requis"}, status=400)
            if not data.get('start_date') or not data.get('end_date'):
                return Response({"error": "Les dates de début et de fin sont requises"}, status=400)

            # Vérifier si une tâche personnelle avec le même titre existe déjà pour cet utilisateur
            if PersonalTask.objects(title=data['title'], created_by=str(user.id)).first():
                return Response({"error": "Vous avez déjà une tâche personnelle avec ce titre"}, status=400)

            # Vérifier que les dates sont valides (pas dans le passé)
            try:
                start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
                now = datetime.now(timezone.utc)

                if start_date.date() < now.date():
                    return Response({"error": "La date de début ne peut pas être dans le passé"}, status=400)
                if end_date.date() < start_date.date():
                    return Response({"error": "La date de fin ne peut pas être antérieure à la date de début"}, status=400)
            except ValueError:
                return Response({"error": "Format de date invalide"}, status=400)

            # Créer la tâche personnelle
            task = PersonalTask(
                title=data['title'],
                description=data.get('description', ''),
                start_date=start_date,
                end_date=end_date,
                status='a_faire',
                priority=data.get('priority', 'moyenne'),
                created_by=str(user.id),
                created_by_name=user.name,
                display_mode=data.get('display_mode', 'list')
            )
            task.save()

            return Response({
                "message": "Tâche personnelle créée avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "description": task.description,
                    "start_date": task.start_date,
                    "end_date": task.end_date,
                    "status": task.status,
                    "priority": task.priority,
                    "created_by": task.created_by,
                    "created_by_name": task.created_by_name,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,
                    "display_mode": task.display_mode
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in PersonalTaskListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de la tâche personnelle"}, status=500)

    def get(self, request):
        """Liste des tâches personnelles de l'utilisateur connecté"""
        try:
            user = request.user
            status_filter = request.query_params.get('status')

            # Récupérer uniquement les tâches personnelles créées par l'utilisateur connecté
            tasks = PersonalTask.objects(created_by=str(user.id))

            # Filtrer par statut si spécifié
            if status_filter:
                tasks = tasks.filter(status=status_filter)

            # Formater les tâches
            tasks_data = []
            for task in tasks:
                tasks_data.append({
                    'id': str(task.id),
                    'title': task.title,
                    'description': task.description,
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'status': task.status,
                    'priority': task.priority,
                    'created_by': task.created_by,
                    'created_by_name': task.created_by_name,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'display_mode': task.display_mode,
                    'can_manage': task.can_manage_task(user),
                    'can_update_status': task.can_update_status(user)
                })

            return Response(tasks_data)

        except Exception as e:
            logger.error(f"Error in PersonalTaskListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalTaskDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, task_id):
        """Récupérer les détails d'une tâche personnelle"""
        try:
            user = request.user

            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à voir cette tâche
            if task.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à voir cette tâche personnelle"}, status=403)

            # Retourner les détails de la tâche
            return Response({
                'id': str(task.id),
                'title': task.title,
                'description': task.description,
                'start_date': task.start_date,
                'end_date': task.end_date,
                'status': task.status,
                'priority': task.priority,
                'created_by': task.created_by,
                'created_by_name': task.created_by_name,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'display_mode': task.display_mode,
                'can_manage': task.can_manage_task(user),
                'can_update_status': task.can_update_status(user)
            })

        except Exception as e:
            logger.error(f"Error in PersonalTaskDetailView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def put(self, request, task_id):
        """Mettre à jour une tâche personnelle"""
        try:
            user = request.user
            data = request.data

            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à modifier cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cette tâche personnelle"}, status=403)

            # Vérifier si la tâche est archivée
            if task.status == 'archived':
                return Response({"error": "Vous ne pouvez pas modifier une tâche archivée"}, status=400)

            # Mettre à jour les champs de la tâche
            if 'title' in data:
                task.title = data['title']
            if 'description' in data:
                task.description = data['description']
            if 'start_date' in data:
                task.start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
            if 'end_date' in data:
                task.end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
            if 'priority' in data and data['priority'] in ['faible', 'moyenne', 'haute']:
                task.priority = data['priority']
            if 'display_mode' in data and data['display_mode'] in ['list', 'card', 'kanban']:
                task.display_mode = data['display_mode']

            # Sauvegarder les modifications
            task.save()

            return Response({
                "message": "Tâche personnelle mise à jour avec succès",
                "task": {
                    'id': str(task.id),
                    'title': task.title,
                    'description': task.description,
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'status': task.status,
                    'priority': task.priority,
                    'created_by': task.created_by,
                    'created_by_name': task.created_by_name,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'display_mode': task.display_mode
                }
            })

        except Exception as e:
            logger.error(f"Error in PersonalTaskDetailView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def patch(self, request, task_id):
        """Mettre à jour le statut d'une tâche personnelle"""
        try:
            user = request.user
            data = request.data

            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à mettre à jour le statut
            if not task.can_update_status(user):
                return Response({"error": "Vous n'êtes pas autorisé à mettre à jour le statut de cette tâche personnelle"}, status=403)

            # Mettre à jour le statut
            if 'status' in data and data['status'] in ['a_faire', 'en_cours', 'en_revision', 'achevee', 'archived']:
                task.status = data['status']
                task.save()

                return Response({
                    "message": "Statut de la tâche personnelle mis à jour avec succès",
                    "status": task.status
                })
            else:
                return Response({"error": "Statut invalide. Les valeurs autorisées sont: a_faire, en_cours, en_revision, achevee, archived"}, status=400)

        except Exception as e:
            logger.error(f"Error in PersonalTaskDetailView.patch: {str(e)}")
            return Response({"error": str(e)}, status=500)

    def delete(self, request, task_id):
        """Supprimer une tâche personnelle"""
        try:
            user = request.user

            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à supprimer cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cette tâche personnelle"}, status=403)

            # Supprimer la tâche
            task.delete()

            return Response({"message": "Tâche personnelle supprimée avec succès"}, status=200)

        except Exception as e:
            logger.error(f"Error in PersonalTaskDetailView.delete: {str(e)}")
            return Response({"error": str(e)}, status=500)

class PersonalTaskArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, task_id):
        """Archiver une tâche personnelle"""
        try:
            user = request.user

            try:
                task = PersonalTask.objects.get(id=task_id)
            except PersonalTask.DoesNotExist:
                return Response({"error": "Tâche personnelle non trouvée"}, status=404)

            # Vérifier que l'utilisateur est autorisé à archiver cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cette tâche personnelle"}, status=403)

            # Archiver la tâche
            task.status = 'archived'
            task.save()

            return Response({
                "message": "Tâche personnelle archivée avec succès",
                "status": task.status
            })

        except Exception as e:
            logger.error(f"Error in PersonalTaskArchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)