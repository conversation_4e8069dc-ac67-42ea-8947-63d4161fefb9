# Tests Postman - Suppression en Cascade des Utilisateurs

## Vue d'ensemble

Ce guide vous permet de tester la suppression en cascade des utilisateurs selon les spécifications :

### Scénarios de suppression :

1. **Admin supprimé** → Suppression de son compte + ses équipes + tâches/événements d'équipes créés
2. **Employé supprimé** → Suppression de son compte + retrait des équipes + ses tâches/événements personnels
3. **Client supprimé** → Suppression de son compte + ses tâches/événements/journaux/notes personnels

## Prérequis

1. **Serveur démarré** : `python manage.py runserver`
2. **Base de données avec des données de test**
3. **Token super admin** pour effectuer les suppressions

## 🔐 Étape 1 : Authentification Super Admin

### Connexion Super Admin
**URL** : `POST http://localhost:8000/api/login/`
**Body** :
```json
{
  "email": "<EMAIL>",
  "password": "votre_mot_de_passe_super_admin"
}
```

**Réponse attendue** :
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "super_admin_id",
    "email": "<EMAIL>",
    "name": "Super Admin",
    "role": "super_admin"
  }
}
```

**⚠️ Important** : Copiez le token `access` pour les requêtes suivantes.

## 📋 Étape 2 : Lister les Utilisateurs

### Récupérer la liste des utilisateurs
**URL** : `GET http://localhost:8000/api/users/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
[
  {
    "id": "admin_user_id",
    "email": "<EMAIL>",
    "name": "Admin User",
    "role": "admin",
    "created_at": "2025-01-01T10:00:00Z",
    "last_login": "2025-01-01T10:00:00Z"
  },
  {
    "id": "employee_user_id",
    "email": "<EMAIL>",
    "name": "Employee User",
    "role": "employee",
    "created_at": "2025-01-01T10:00:00Z",
    "last_login": "2025-01-01T10:00:00Z"
  },
  {
    "id": "client_user_id",
    "email": "<EMAIL>",
    "name": "Client User",
    "role": "client",
    "created_at": "2025-01-01T10:00:00Z",
    "last_login": "2025-01-01T10:00:00Z"
  }
]
```

**📝 Note** : Notez les IDs des utilisateurs que vous voulez supprimer.

## 🧪 Étape 3 : Tests de Suppression en Cascade

### Test 1 : Suppression d'un Admin

#### 3.1 Vérifier les données avant suppression

**Équipes de l'admin** :
```
GET http://localhost:8000/api/teams/
Headers: Authorization: Bearer {access_token}
```

**Événements d'équipe** :
```
GET http://localhost:8000/api/events/
Headers: Authorization: Bearer {access_token}
```

**Tâches d'équipe** :
```
GET http://localhost:8000/api/team-tasks/
Headers: Authorization: Bearer {access_token}
```

#### 3.2 Supprimer l'admin
**URL** : `DELETE http://localhost:8000/api/users/{admin_user_id}/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
{
  "message": "L'utilisateur <EMAIL> a été supprimé avec succès, ainsi que toutes ses données associées"
}
```

#### 3.3 Vérifier la suppression en cascade
- **Équipes** : Doivent être supprimées
- **Événements d'équipe** : Doivent être supprimés
- **Tâches d'équipe** : Doivent être supprimées

### Test 2 : Suppression d'un Employé

#### 3.1 Vérifier les données avant suppression

**Événements personnels** :
```
GET http://localhost:8000/api/personal-events/
Headers: Authorization: Bearer {access_token}
```

**Tâches personnelles** :
```
GET http://localhost:8000/api/personal-tasks/
Headers: Authorization: Bearer {access_token}
```

**Équipes (vérifier membership)** :
```
GET http://localhost:8000/api/teams/
Headers: Authorization: Bearer {access_token}
```

#### 3.2 Supprimer l'employé
**URL** : `DELETE http://localhost:8000/api/users/{employee_user_id}/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
{
  "message": "L'utilisateur <EMAIL> a été supprimé avec succès, ainsi que toutes ses données associées"
}
```

#### 3.3 Vérifier la suppression en cascade
- **Événements personnels** : Doivent être supprimés
- **Tâches personnelles** : Doivent être supprimées
- **Membership équipes** : L'employé doit être retiré des équipes
- **Événements d'équipe assignés** : Le nom de l'employé doit être effacé

### Test 3 : Suppression d'un Client

#### 3.1 Vérifier les données avant suppression

**Tâches personnelles** :
```
GET http://localhost:8000/api/personal-tasks/
Headers: Authorization: Bearer {access_token}
```

**Événements personnels** :
```
GET http://localhost:8000/api/personal-events/
Headers: Authorization: Bearer {access_token}
```

**Notes personnelles** :
```
GET http://localhost:8000/api/personal-notes/
Headers: Authorization: Bearer {access_token}
```

**Journaux personnels** :
```
GET http://localhost:8000/api/personal-journals/
Headers: Authorization: Bearer {access_token}
```

#### 3.2 Supprimer le client
**URL** : `DELETE http://localhost:8000/api/users/{client_user_id}/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
{
  "message": "L'utilisateur <EMAIL> a été supprimé avec succès, ainsi que toutes ses données associées"
}
```

#### 3.3 Vérifier la suppression en cascade
- **Tâches personnelles** : Doivent être supprimées
- **Événements personnels** : Doivent être supprimés
- **Notes personnelles** : Doivent être supprimées
- **Journaux personnels** : Doivent être supprimés

## 🚫 Étape 4 : Tests de Sécurité

### Test 4 : Tentative de suppression d'un Super Admin

**URL** : `DELETE http://localhost:8000/api/users/{super_admin_user_id}/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
{
  "error": "Il est interdit de supprimer un super_admin"
}
```

### Test 5 : Tentative de suppression de son propre compte

**URL** : `DELETE http://localhost:8000/api/users/{current_super_admin_id}/`
**Headers** :
```
Authorization: Bearer {access_token}
```

**Réponse attendue** :
```json
{
  "error": "Vous ne pouvez pas supprimer votre propre compte"
}
```

## ✅ Checklist de Vérification

### Pour un Admin supprimé :
- [ ] Compte utilisateur supprimé
- [ ] Équipes créées supprimées
- [ ] Événements d'équipe supprimés
- [ ] Tâches d'équipe supprimées

### Pour un Employé supprimé :
- [ ] Compte utilisateur supprimé
- [ ] Événements personnels supprimés
- [ ] Tâches personnelles supprimées
- [ ] Retiré des équipes (membership)
- [ ] Nom effacé des assignations d'équipe

### Pour un Client supprimé :
- [ ] Compte utilisateur supprimé
- [ ] Tâches personnelles supprimées
- [ ] Événements personnels supprimés
- [ ] Notes personnelles supprimées
- [ ] Journaux personnels supprimés

### Sécurité :
- [ ] Super admin ne peut pas être supprimé
- [ ] Utilisateur ne peut pas supprimer son propre compte

## 🔍 Vérification Avancée

### Vérifier les logs du serveur
Consultez les logs du serveur pour voir les détails de la suppression en cascade :
```
[INFO] Début de la suppression en cascade RADICALE pour l'utilisateur <EMAIL>
[INFO] Suppression de X équipes associées à l'admin
[INFO] Suppression de X événements créés par l'utilisateur
[INFO] Suppression de X tâches d'équipe créées par l'utilisateur
[INFO] Utilisateur supprimé avec succès: <EMAIL>
```

### Vérifier la base de données
Si vous avez accès à MongoDB, vous pouvez vérifier directement :
```javascript
// Vérifier que l'utilisateur n'existe plus
db.users.find({email: "<EMAIL>"})

// Vérifier que les données associées sont supprimées
db.teams.find({responsable: "user_id"})
db.events.find({created_by: "user_id"})
db.personal_tasks.find({created_by: "user_id"})
```

## 🎯 Résultats Attendus

La suppression en cascade doit être **complète et automatique** :
- Aucune donnée orpheline ne doit rester
- Toutes les références à l'utilisateur doivent être nettoyées
- Les équipes et leurs données doivent être supprimées pour les admins
- Les données personnelles doivent être supprimées pour tous les utilisateurs
