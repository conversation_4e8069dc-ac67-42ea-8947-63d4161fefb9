from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required, super_admin_required
from ..mongo_models import User, Team
from ..models.event_model import Event
from ..models.personal_event_model import PersonalEvent
from ..models.team_task_model import TeamTask
from ..models.personal_task_model import PersonalTask
from ..utils import generate_temp_password, send_temp_password_email
from mongoengine.errors import ValidationError, NotUniqueError
import logging

logger = logging.getLogger(__name__)

class UserView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            user = request.user
            return Response({
                'id': str(user.id),
                'email': user.email,
                'name': user.name,
                'role': user.role,
                'permissions': user.permissions
            })
        except Exception as e:
            return Response({'error': str(e)}, status=500)

class UserListView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        try:
            # Seul le super_admin peut accéder à cette ressource
            users = User.objects.all()

            return Response([
                {
                    'id': str(user.id),
                    'email': user.email,
                    'name': user.name,
                    'role': user.role,
                    'created_at': user.created_at,
                    'last_login': user.last_login
                } for user in users
            ])
        except Exception as e:
            return Response({'error': str(e)}, status=500)

class UserDetailView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)

            return Response({
                'id': str(user.id),
                'email': user.email,
                'name': user.name,
                'role': user.role,
                'permissions': user.permissions,
                'created_at': user.created_at,
                'last_login': user.last_login
            })
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    @super_admin_required
    def delete(self, request, user_id):
        try:
            logger.info(f"Tentative de suppression de l'utilisateur avec l'ID: {user_id}")
            user = User.objects.get(id=user_id)

            # Empêcher la suppression de son propre compte
            if str(request.user.id) == user_id:
                logger.warning(f"Tentative de suppression de son propre compte par {request.user.email}")
                return Response({'error': 'Vous ne pouvez pas supprimer votre propre compte'}, status=400)

            # Empêcher la suppression d'un super_admin
            if user.role == 'super_admin':
                logger.warning(f"Tentative de suppression d'un super_admin ({user.email}) par {request.user.email}")
                return Response({'error': 'Il est interdit de supprimer un super_admin'}, status=403)

            # Stocker les informations de l'utilisateur pour la journalisation
            user_email = user.email
            user_role = user.role
            user_id_str = str(user.id)
            user_name = user.name

            logger.info(f"Début du processus de suppression RADICALE pour l'utilisateur {user_email} (ID: {user_id_str}, rôle: {user_role})")

            # Effectuer la suppression en cascade
            try:
                # Appel explicite à la méthode cascade_delete améliorée
                logger.info(f"Appel de la méthode cascade_delete RADICALE pour l'utilisateur {user_email}")
                result = user.cascade_delete()

                if result:
                    logger.info(f"Suppression en cascade RADICALE réussie pour l'utilisateur {user_email}")
                else:
                    logger.warning(f"La méthode cascade_delete a retourné False pour l'utilisateur {user_email}")
                    return Response({
                        'error': 'La suppression en cascade a échoué pour une raison inconnue'
                    }, status=500)

            except Exception as e:
                logger.error(f"Erreur lors de la suppression en cascade pour l'utilisateur {user_email}: {str(e)}")
                # Même en cas d'erreur, on continue pour supprimer l'utilisateur
                logger.warning(f"Tentative de suppression de l'utilisateur malgré l'erreur de cascade_delete")

            # Vérification supplémentaire pour s'assurer que toutes les références ont été supprimées

            # Dernière vérification et nettoyage radical
            # 1. Équipes
            Team.objects(responsable=user_id_str).update(responsable="", responsable_name="Responsable supprimé")
            Team.objects(responsable_name=user_name).update(responsable_name="Responsable supprimé")

            # 2. Événements
            # Supprimer les événements créés par l'utilisateur
            Event.objects(created_by=user_id_str).delete()
            # Mettre à jour les événements où l'utilisateur est assigné
            Event.objects(member_id=user_id_str).update(member_id="", member_name="")
            # Mettre à jour TOUS les événements avec le nom de l'utilisateur
            Event.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

            # 3. Événements personnels
            PersonalEvent.objects(created_by=user_id_str).delete()
            PersonalEvent.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

            # 4. Tâches d'équipe
            # Supprimer les tâches créées par l'utilisateur
            TeamTask.objects(created_by=user_id_str).delete()
            # Mettre à jour les tâches où l'utilisateur est responsable
            TeamTask.objects(responsable=user_id_str).update(responsable="", responsable_name="Responsable supprimé")
            TeamTask.objects(responsable_name=user_name).update(responsable_name="Responsable supprimé")
            # Mettre à jour les tâches où l'utilisateur est assigné
            TeamTask.objects(member_id=user_id_str).update(member_id="", member_name="")
            # Mettre à jour les tâches avec le nom de l'utilisateur
            TeamTask.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

            # 5. Tâches personnelles
            PersonalTask.objects(created_by=user_id_str).delete()
            PersonalTask.objects(created_by_name=user_name).update(created_by_name="Utilisateur supprimé")

            # 6. Paramètres Pomodoro
            from ..models.pomodoro_model import PomodoroSettings
            try:
                pomodoro_settings = PomodoroSettings.objects.get(user_id=user_id_str)
                pomodoro_settings.delete()
                logger.info(f"Suppression des paramètres Pomodoro de l'utilisateur {user_email}")
            except PomodoroSettings.DoesNotExist:
                logger.info(f"Aucun paramètre Pomodoro trouvé pour l'utilisateur {user_email}")

            # 7. Métriques BI
            from ..models.bi_model import BiMetric, BiDashboard
            # Supprimer les métriques BI de l'utilisateur
            bi_metrics = BiMetric.objects(user_id=user_id_str)
            bi_metrics_count = bi_metrics.count()
            bi_metrics.delete()
            logger.info(f"Suppression de {bi_metrics_count} métriques BI de l'utilisateur")

            # Supprimer les tableaux de bord BI de l'utilisateur
            bi_dashboards = BiDashboard.objects(user_id=user_id_str)
            bi_dashboards_count = bi_dashboards.count()
            bi_dashboards.delete()
            logger.info(f"Suppression de {bi_dashboards_count} tableaux de bord BI de l'utilisateur")

            # Journalisation des opérations de nettoyage
            logger.info(f"Nettoyage final effectué pour l'utilisateur {user_email} (ID: {user_id_str})")

            # Supprimer l'utilisateur des équipes (approche radicale)
            for team in Team.objects(members__has_key=user_id_str):
                if user_id_str in team.members:
                    del team.members[user_id_str]
                    team.save()
                    logger.info(f"Utilisateur {user_email} retiré de l'équipe {team.name} (nettoyage final)")

            # Supprimer l'utilisateur lui-même
            logger.info(f"Suppression de l'utilisateur {user_email} de la base de données")
            user.delete()

            logger.info(f"Utilisateur supprimé avec succès: {user_email} (ID: {user_id_str}, rôle: {user_role})")
            return Response({
                'message': f'L\'utilisateur {user_email} a été supprimé avec succès, ainsi que toutes ses données associées'
            }, status=200)
        except User.DoesNotExist:
            return Response({'error': 'Utilisateur non trouvé'}, status=404)
        except Exception as e:
            logger.error(f"Erreur lors de la suppression de l'utilisateur: {str(e)}")
            return Response({'error': str(e)}, status=500)

class UserCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def post(self, request):
        try:
            data = request.data
            email = data.get('email')
            name = data.get('name')
            role = data.get('role', 'employee')

            if User.objects(email=email).first():
                return Response({'error': 'Email already exists'}, status=400)

            temp_password = generate_temp_password()
            user = User(
                email=email,
                name=name,
                role=role,
                temp_password_required=True
            )
            user.set_password(temp_password)

            # Set default permissions based on role
            if role == 'admin':
                user.permissions.update({
                    'manage_teams': True,
                    'manage_team_tasks': True,
                    'manage_team_calendars': True,
                    'view_team_dashboards': True
                })

            user.save()

            try:
                send_temp_password_email(email, temp_password, name)
            except Exception as e:
                logger.error(f"Erreur lors de l'envoi de l'email: {str(e)}")
                user.delete()
                return Response({'error': 'Failed to send credentials email'}, status=500)

            return Response({
                'message': 'User created successfully',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'name': user.name,
                    'role': user.role
                }
            }, status=201)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

class UserUpdateView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def put(self, request, user_id):
        try:
            user = User.objects.get(id=user_id)
            data = request.data

            if 'name' in data:
                user.name = data['name']
            if 'email' in data and data['email'] != user.email:
                if User.objects(email=data['email']).first():
                    return Response({'error': 'Email already exists'}, status=400)
                user.email = data['email']
            if 'role' in data and data['role'] != user.role:
                if data['role'] not in ['admin', 'employee', 'client']:
                    return Response({'error': 'Invalid role'}, status=400)
                user.role = data['role']

            user.save()

            return Response({
                'message': 'User updated successfully',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'name': user.name,
                    'role': user.role
                }
            })
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

class UpdateProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request):
        try:
            user = request.user
            data = request.data

            # Gérer les champs first_name et last_name séparément
            if 'first_name' in data and 'last_name' in data:
                # Si les deux champs sont fournis, construire le nom complet
                first_name = data['first_name'].strip()
                last_name = data['last_name'].strip()
                if first_name and last_name:
                    user.name = f"{first_name} {last_name}"
            elif 'first_name' in data:
                # Si seulement first_name est fourni, l'utiliser comme nom complet
                first_name = data['first_name'].strip()
                if first_name:
                    user.name = first_name
            elif 'name' in data:
                # Compatibilité avec l'ancien format
                user.name = data['name']

            # Gérer l'email
            if 'email' in data and data['email'] != user.email:
                if User.objects(email=data['email']).first():
                    return Response({'error': 'Email already exists'}, status=400)
                user.email = data['email']

            user.save()

            # Extraire first_name et last_name du nom complet pour la réponse
            name_parts = user.name.split(' ', 1) if user.name else ['', '']
            first_name = name_parts[0] if len(name_parts) > 0 else ''
            last_name = name_parts[1] if len(name_parts) > 1 else ''

            return Response({
                'message': 'Profile updated successfully',
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'name': user.name,
                    'first_name': first_name,
                    'last_name': last_name,
                    'role': user.role
                }
            })
        except Exception as e:
            return Response({'error': str(e)}, status=500)

from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator(csrf_exempt, name='dispatch')
class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'options']  # Explicitement autoriser POST

    def get(self, request):
        """Méthode GET pour vérifier si l'API est accessible"""
        logger.info(f"Requête GET reçue sur change-password - Utilisateur: {request.user.email}")
        return Response({
            'message': 'Change password API is accessible',
            'user': request.user.email,
            'role': request.user.role
        })

    def post(self, request):
        try:
            logger.info(f"Requête de changement de mot de passe reçue - Méthode: {request.method}, URL: {request.path}")
            user = request.user
            logger.info(f"Utilisateur authentifié: {user.email}, rôle: {user.role}")

            # Afficher les données reçues (sans le mot de passe)
            data_keys = list(request.data.keys()) if hasattr(request, 'data') else []
            logger.info(f"Données reçues - clés: {data_keys}")

            current_password = request.data.get('current_password')
            new_password = request.data.get('new_password')

            logger.info(f"Tentative de changement de mot de passe pour l'utilisateur: {user.email}, rôle: {user.role}")

            if not current_password or not new_password:
                logger.warning(f"Échec: mot de passe actuel ou nouveau manquant pour {user.email}")
                return Response({'error': 'Both current and new passwords are required'}, status=400)

            # Vérification de la complexité du mot de passe
            if len(new_password) < 8:
                logger.warning(f"Échec: nouveau mot de passe trop court pour {user.email}")
                return Response({'error': 'Le mot de passe doit contenir au moins 8 caractères'}, status=400)

            # Vérification du mot de passe actuel
            if not user.check_password(current_password):
                logger.warning(f"Échec: mot de passe actuel incorrect pour {user.email}")
                return Response({'error': 'Current password is incorrect'}, status=400)

            logger.info(f"Mot de passe actuel validé pour {user.email}, application du nouveau mot de passe")

            # Mise à jour du mot de passe
            user.set_password(new_password)
            user.temp_password_required = False
            user.temp_password_used = False

            # Sauvegarde des modifications
            user.save()

            logger.info(f"Mot de passe changé avec succès pour {user.email}")
            return Response({'message': 'Password changed successfully'})
        except Exception as e:
            logger.error(f"Erreur lors du changement de mot de passe pour {request.user.email}: {str(e)}")
            return Response({'error': str(e)}, status=500)

class UserPermissionsView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request, user_id):
        """Récupérer les permissions statiques d'un utilisateur selon son rôle"""
        try:
            user = User.objects.get(id=user_id)

            # Définir les permissions statiques selon le rôle
            static_permissions = self.get_static_permissions_by_role(user.role)

            return Response({
                'user_id': str(user.id),
                'name': user.name,
                'email': user.email,
                'role': user.role,
                'permissions': static_permissions,
                'is_static': True,  # Indique que ces permissions sont statiques
                'message': f'Permissions statiques pour le rôle {user.role}'
            })

        except User.DoesNotExist:
            return Response({'error': 'Utilisateur non trouvé'}, status=404)
        except Exception as e:
            return Response({'error': str(e)}, status=500)

    def get_static_permissions_by_role(self, role):
        """Retourne les permissions statiques selon le rôle"""

        # Permissions de base (toutes à False par défaut)
        base_permissions = {
            # Gestion personnelle
            'manage_personal_tasks': False,
            'manage_personal_calendar': False,
            'manage_journal_notes': False,
            'activate_focus_mode': False,
            'view_personal_dashboard': False,

            # Gestion équipes
            'manage_teams': False,
            'manage_team_tasks': False,
            'manage_team_calendars': False,
            'view_team_dashboards': False,

            # Gestion utilisateurs (super admin uniquement)
            'manage_users': False,
            'activate_user_permissions': False,

            # Dashboards
            'view_main_dashboard': False,

            # Archives et personnalisation
            'view_archives': False,
            'unarchive_items': False,
            'customize_events': False
        }

        if role == 'super_admin':
            # Super admin : pas de liste de permissions (géré différemment)
            return {
                'message': 'Super admin n\'a pas de liste de permissions modifiable',
                'note': 'Le super admin peut seulement modifier son email et mot de passe depuis son profil'
            }

        elif role == 'admin':
            # Admins : gérer les équipes, leurs calendriers, leurs tâches (si responsables)
            base_permissions.update({
                'manage_teams': True,
                'manage_team_calendars': True,
                'manage_team_tasks': True,
                'view_team_dashboards': True,
                'view_main_dashboard': True
            })

        elif role == 'employee':
            # Employés : gérer tâches perso, calendrier perso, consulter dashboard perso
            base_permissions.update({
                'manage_personal_tasks': True,
                'manage_personal_calendar': True,
                'view_personal_dashboard': True
            })

        elif role == 'client':
            # Clients : gérer tâches perso, calendrier perso, notes/journaux, mode focus, dashboard perso
            base_permissions.update({
                'manage_personal_tasks': True,
                'manage_personal_calendar': True,
                'manage_journal_notes': True,
                'activate_focus_mode': True,
                'view_personal_dashboard': True
            })

        return base_permissions

    @super_admin_required
    def put(self, request, user_id):
        """Les permissions sont statiques et ne peuvent pas être modifiées"""
        return Response({
            'error': 'Permissions non modifiables',
            'message': 'Les permissions sont statiques selon le rôle de l\'utilisateur et ne peuvent pas être modifiées manuellement'
        }, status=400)

class UserPermissionsListView(APIView):
    """Vue pour récupérer la liste des permissions avec leurs descriptions"""
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def get(self, request):
        """Récupérer la liste complète des permissions avec descriptions en français"""
        try:
            permissions_descriptions = {
                # Gestion personnelle
                'manage_personal_tasks': {
                    'name': 'Gérer tâches personnelles',
                    'description': 'Créer, modifier et supprimer ses propres tâches',
                    'category': 'Gestion personnelle'
                },
                'manage_personal_calendar': {
                    'name': 'Gérer calendrier personnel',
                    'description': 'Gérer son calendrier personnel et ses événements',
                    'category': 'Gestion personnelle'
                },
                'manage_journal_notes': {
                    'name': 'Journal & notes',
                    'description': 'Gérer journal et notes personnelles',
                    'category': 'Gestion personnelle'
                },
                'activate_focus_mode': {
                    'name': 'Mode focus',
                    'description': 'Activer le mode focus pour les sessions de travail',
                    'category': 'Gestion personnelle'
                },
                'view_personal_dashboard': {
                    'name': 'Dashboard personnel',
                    'description': 'Consulter son dashboard personnel',
                    'category': 'Tableaux de bord'
                },

                # Gestion équipes
                'manage_teams': {
                    'name': 'Gérer les équipes',
                    'description': 'Créer, modifier et gérer les équipes',
                    'category': 'Gestion équipes'
                },
                'manage_team_tasks': {
                    'name': 'Gérer tâches équipes',
                    'description': 'Gérer les tâches des équipes dont on est responsable',
                    'category': 'Gestion équipes'
                },
                'manage_team_calendars': {
                    'name': 'Gérer calendriers équipes',
                    'description': 'Gérer les calendriers des équipes',
                    'category': 'Gestion équipes'
                },
                'view_team_dashboards': {
                    'name': 'Dashboards équipes',
                    'description': 'Accès aux dashboards d\'équipe',
                    'category': 'Tableaux de bord'
                },

                # Dashboards
                'view_main_dashboard': {
                    'name': 'Dashboard principal',
                    'description': 'Accès au dashboard principal',
                    'category': 'Tableaux de bord'
                },

                # Gestion utilisateurs (super admin uniquement)
                'manage_users': {
                    'name': 'Gérer utilisateurs',
                    'description': 'Créer, modifier et supprimer des utilisateurs',
                    'category': 'Administration'
                },
                'activate_user_permissions': {
                    'name': 'Activer permissions utilisateurs',
                    'description': 'Modifier les permissions des utilisateurs',
                    'category': 'Administration'
                }
            }

            return Response({
                'permissions': permissions_descriptions,
                'message': 'Liste des permissions disponibles'
            })

        except Exception as e:
            return Response({'error': str(e)}, status=500)

class MigrateUsersView(APIView):
    permission_classes = [IsAuthenticated]

    @super_admin_required
    def post(self, request):
        try:
            users = User.objects.all()
            migrated_count = 0

            for user in users:
                if not user.permissions:
                    # Set default permissions based on role
                    if user.role == 'admin':
                        user.permissions = {
                            'manage_teams': True,
                            'manage_team_tasks': True,
                            'manage_team_calendars': True,
                            'view_team_dashboards': True
                        }
                    elif user.role == 'employee':
                        user.permissions = {
                            'manage_personal_tasks': True,
                            'activate_focus_mode': True,
                            'manage_journal_notes': True,
                            'view_personal_dashboard': True
                        }
                    user.save()
                    migrated_count += 1

            return Response({
                'message': f'Successfully migrated {migrated_count} users',
                'migrated_count': migrated_count
            })
        except Exception as e:
            return Response({'error': str(e)}, status=500)