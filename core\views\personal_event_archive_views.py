from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..models.personal_event_model import PersonalEvent
import logging

logger = logging.getLogger(__name__)

class PersonalEventArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, event_id):
        """Archiver un événement personnel"""
        try:
            user = request.user
            
            try:
                event = PersonalEvent.objects.get(id=event_id)
            except PersonalEvent.DoesNotExist:
                return Response({"error": "Événement personnel non trouvé"}, status=404)
            
            # Vérifier que l'utilisateur est autorisé à archiver cet événement
            if not event.can_manage_event(user):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cet événement personnel"}, status=403)
            
            # Archiver l'événement
            event.status = 'archived'
            event.save()
            
            return Response({
                "message": "Événement personnel archivé avec succès",
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "status": event.status,
                    "updated_at": event.updated_at
                }
            })
            
        except Exception as e:
            logger.error(f"Error in PersonalEventArchiveView.put: {str(e)}")
            return Response({"error": str(e)}, status=500)
