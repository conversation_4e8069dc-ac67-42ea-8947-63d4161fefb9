/**
 * Service de gestion des événements du calendrier pour le frontend
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/services/
 */

// Importation des dépendances avec la syntaxe ES6 (import) au lieu de CommonJS (require)
import axios from 'axios'; // Utilisez la syntaxe import au lieu de require

// Configuration de l'API
const API_URL = '/api/events';

/**
 * Récupère tous les événements
 * @returns {Promise} - Promise contenant les événements
 */
export const getEvents = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des événements:', error);
    throw error;
  }
};

/**
 * Récupère un événement par son ID
 * @param {String} id - ID de l'événement
 * @returns {Promise} - Promise contenant l'événement
 */
export const getEventById = async (id) => {
  try {
    const response = await axios.get(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'événement ${id}:`, error);
    throw error;
  }
};

/**
 * Crée un nouvel événement
 * @param {Object} eventData - Données de l'événement
 * @returns {Promise} - Promise contenant l'événement créé
 */
export const createEvent = async (eventData) => {
  try {
    const response = await axios.post(API_URL, eventData);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création de l\'événement:', error);
    throw error;
  }
};

/**
 * Met à jour un événement
 * @param {String} id - ID de l'événement
 * @param {Object} eventData - Données de l'événement
 * @returns {Promise} - Promise contenant l'événement mis à jour
 */
export const updateEvent = async (id, eventData) => {
  try {
    const response = await axios.put(`${API_URL}/${id}`, eventData);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour de l'événement ${id}:`, error);
    throw error;
  }
};

/**
 * Supprime un événement
 * @param {String} id - ID de l'événement
 * @returns {Promise} - Promise contenant la réponse
 */
export const deleteEvent = async (id) => {
  try {
    const response = await axios.delete(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la suppression de l'événement ${id}:`, error);
    throw error;
  }
};

/**
 * Met à jour le statut d'un événement
 * @param {String} id - ID de l'événement
 * @param {String} status - Nouveau statut (pending, completed, archived)
 * @returns {Promise} - Promise contenant la réponse
 */
export const updateEventStatus = async (id, status) => {
  try {
    const response = await axios.put(`${API_URL}/${id}/status/`, { status });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour du statut de l'événement ${id}:`, error);
    throw error;
  }
};

/**
 * Archive un événement
 * @param {String} id - ID de l'événement
 * @returns {Promise} - Promise contenant la réponse
 */
export const archiveEvent = async (id) => {
  try {
    const response = await axios.put(`${API_URL}/${id}/archive/`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de l'archivage de l'événement ${id}:`, error);
    throw error;
  }
};