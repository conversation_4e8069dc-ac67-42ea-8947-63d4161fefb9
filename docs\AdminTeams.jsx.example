/**
 * Page d'administration des équipes
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/pages/
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext'; // Ajustez le chemin selon votre structure
// Utiliser la syntaxe import ES6 au lieu de require
import { getTeams, createTeam, updateTeam, deleteTeam } from '../services/teamService'; // Importation avec la syntaxe ES6
import { checkTeamPermissions, hasPermission } from '../services/permissionService';
import TeamPermissionGate from '../components/teams/TeamPermissionGate';

// Composant fictif pour l'exemple
const TeamCard = ({ team, onEdit, onDelete, onManageMembers }) => {
  return (
    <div className="team-card bg-white p-4 rounded-lg shadow-md mb-4">
      <h3 className="text-xl font-semibold">{team.name}</h3>
      <p className="text-gray-600 mb-2">{team.description}</p>
      <p className="text-sm text-gray-500">Responsable: {team.responsable?.name}</p>
      <p className="text-sm text-gray-500">Membres: {team.members?.length || 0}</p>
      
      <div className="mt-4 flex gap-2">
        <TeamPermissionGate team={team} permissionType="canManage">
          <button 
            onClick={() => onEdit(team)} 
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Modifier
          </button>
        </TeamPermissionGate>
        
        <TeamPermissionGate team={team} permissionType="canManage">
          <button 
            onClick={() => onDelete(team.id)} 
            className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Supprimer
          </button>
        </TeamPermissionGate>
        
        <TeamPermissionGate team={team} permissionType="canAddMembers">
          <button 
            onClick={() => onManageMembers(team)} 
            className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Gérer les membres
          </button>
        </TeamPermissionGate>
      </div>
    </div>
  );
};

// Fonction principale pour la page d'administration des équipes
const AdminTeams = () => {
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Charger les équipes
  const loadTeams = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Utiliser la fonction importée avec ES6
      const teamsData = await getTeams();
      setTeams(teamsData);
    } catch (err) {
      console.error('Error fetching teams:', err);
      setError('Erreur lors de la récupération des équipes');
    } finally {
      setLoading(false);
    }
  };
  
  // Charger les équipes au montage du composant
  useEffect(() => {
    loadTeams();
  }, []);
  
  // Gérer la modification d'une équipe
  const handleEditTeam = (team) => {
    navigate(`/teams/edit/${team.id}`);
  };
  
  // Gérer la suppression d'une équipe
  const handleDeleteTeam = async (teamId) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette équipe ?')) {
      try {
        await deleteTeam(teamId);
        loadTeams(); // Recharger les équipes après la suppression
      } catch (err) {
        console.error('Error deleting team:', err);
        setError('Erreur lors de la suppression de l\'équipe');
      }
    }
  };
  
  // Gérer la gestion des membres d'une équipe
  const handleManageMembers = (team) => {
    navigate(`/teams/${team.id}/members`);
  };
  
  // Gérer la création d'une équipe
  const handleCreateTeam = () => {
    navigate('/teams/create');
  };
  
  // Vérifier si l'utilisateur a la permission de créer une équipe
  const canCreate = user && hasPermission(user, 'manage_teams');
  
  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Gestion des Équipes</h1>
        
        {canCreate && (
          <button 
            onClick={handleCreateTeam}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Créer une équipe
          </button>
        )}
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-4">Chargement...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {teams.length > 0 ? (
            teams.map(team => (
              <TeamCard 
                key={team.id} 
                team={team} 
                onEdit={handleEditTeam}
                onDelete={handleDeleteTeam}
                onManageMembers={handleManageMembers}
              />
            ))
          ) : (
            <div className="col-span-full text-center py-4">
              Aucune équipe trouvée.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminTeams;