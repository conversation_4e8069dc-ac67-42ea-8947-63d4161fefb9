# Fonctionnalité de Calendrier Personnel

## Description
Le calendrier personnel est une fonctionnalité qui permet à chaque utilisateur (employé ou client) de gérer ses propres événements indépendamment des calendriers d'équipe. Contrairement aux événements d'équipe qui sont créés par les administrateurs et assignés à des équipes ou des membres spécifiques, les événements personnels sont créés et gérés uniquement par l'utilisateur lui-même.

## Différences avec le Calendrier d'Équipe

| Caractéristique | Calendrier d'Équipe | Calendrier Personnel |
|-----------------|---------------------|----------------------|
| Création | Par les administrateurs | Par les employés/clients |
| Assignation | À une équipe ou un membre | Aucune assignation (uniquement à soi-même) |
| Visibilité | Visible par les administrateurs et les membres assignés | Visible uniquement par le créateur |
| Gestion | Par les administrateurs responsables | Par le créateur uniquement |

## Modèle de Données

Le modèle `PersonalEvent` a été créé avec les champs suivants :
- `id` : ObjectId (identifiant unique)
- `title` : Titre de l'événement (obligatoire)
- `description` : Description détaillée (optionnel)
- `start_date` et `end_date` : Dates de début et de fin (obligatoires)
- `start_time` et `end_time` : Heures de début et de fin (obligatoires)
- `note` : Notes additionnelles (optionnel)
- `status` : Statut de l'événement (pending, completed, archived)
- `created_by` : ID de l'utilisateur qui a créé l'événement
- `created_by_name` : Nom de l'utilisateur qui a créé l'événement
- `created_at` et `updated_at` : Dates de création et de mise à jour

## Permissions

- Chaque utilisateur (employé ou client) ne peut voir et gérer que ses propres événements personnels
- Les administrateurs n'ont pas accès aux événements personnels des utilisateurs
- Les opérations autorisées pour le créateur de l'événement sont :
  - Création
  - Consultation
  - Modification
  - Suppression
  - Mise à jour du statut
  - Archivage

## API Endpoints

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/api/personal-events/` | Récupérer tous les événements personnels de l'utilisateur connecté |
| POST | `/api/personal-events/` | Créer un nouvel événement personnel |
| GET | `/api/personal-events/<event_id>/` | Récupérer les détails d'un événement personnel spécifique |
| PUT | `/api/personal-events/<event_id>/` | Mettre à jour un événement personnel |
| PATCH | `/api/personal-events/<event_id>/` | Mettre à jour le statut d'un événement personnel |
| DELETE | `/api/personal-events/<event_id>/` | Supprimer un événement personnel |

## Tests à Effectuer

### 1. Tests de Création d'Événements Personnels

- **Test 1.1** : Créer un événement personnel avec tous les champs requis
  - Vérifier que l'événement est créé avec succès
  - Vérifier que l'ID de l'utilisateur connecté est bien assigné comme créateur

- **Test 1.2** : Tenter de créer un événement sans titre
  - Vérifier que l'API renvoie une erreur 400

- **Test 1.3** : Tenter de créer un événement avec une date de début dans le passé
  - Vérifier que l'API renvoie une erreur 400

### 2. Tests de Récupération d'Événements Personnels

- **Test 2.1** : Récupérer la liste des événements personnels
  - Vérifier que seuls les événements créés par l'utilisateur connecté sont retournés

- **Test 2.2** : Récupérer les détails d'un événement personnel spécifique
  - Vérifier que les détails sont correctement retournés si l'événement appartient à l'utilisateur

- **Test 2.3** : Tenter de récupérer les détails d'un événement personnel créé par un autre utilisateur
  - Vérifier que l'API renvoie une erreur 403

### 3. Tests de Mise à Jour d'Événements Personnels

- **Test 3.1** : Mettre à jour le titre et la description d'un événement personnel
  - Vérifier que les modifications sont bien enregistrées

- **Test 3.2** : Mettre à jour le statut d'un événement personnel
  - Vérifier que le statut est bien mis à jour

- **Test 3.3** : Tenter de mettre à jour un événement personnel créé par un autre utilisateur
  - Vérifier que l'API renvoie une erreur 403

### 4. Tests de Suppression d'Événements Personnels

- **Test 4.1** : Supprimer un événement personnel
  - Vérifier que l'événement est bien supprimé

- **Test 4.2** : Tenter de supprimer un événement personnel créé par un autre utilisateur
  - Vérifier que l'API renvoie une erreur 403

### 5. Tests d'Intégration

- **Test 5.1** : Vérifier la séparation entre les calendriers d'équipe et personnels
  - Créer un événement d'équipe et un événement personnel
  - Vérifier que l'événement d'équipe n'apparaît pas dans le calendrier personnel
  - Vérifier que l'événement personnel n'apparaît pas dans le calendrier d'équipe

- **Test 5.2** : Tester le cycle de vie complet d'un événement personnel
  - Créer un événement personnel
  - Mettre à jour ses informations
  - Changer son statut
  - Le supprimer
  - Vérifier que chaque étape fonctionne correctement

## Intégration dans l'Interface Utilisateur

Pour l'interface utilisateur, il est important de bien distinguer les calendriers d'équipe et les calendriers personnels :

1. Créer des onglets ou sections séparés pour chaque type de calendrier
2. Utiliser des couleurs ou des icônes différentes pour distinguer visuellement les deux types d'événements
3. Ajouter des filtres permettant de basculer entre les différents types de calendriers
4. Afficher clairement qui est le créateur de l'événement
5. Adapter les formulaires de création/modification selon le type d'événement (avec ou sans champs d'assignation)

## Conclusion

La fonctionnalité de calendrier personnel offre aux utilisateurs un espace privé pour gérer leurs propres événements, indépendamment des calendriers d'équipe. Cette séparation claire permet une meilleure organisation et une expérience utilisateur plus personnalisée.