/**
 * Service de gestion des équipes pour le frontend
 * Ce fichier est un exemple d'implémentation à placer dans le dossier frontend/mon-app-react/src/services/
 */

// Importation des dépendances avec la syntaxe ES6 (import) au lieu de CommonJS (require)
import axios from 'axios'; // Utilisez la syntaxe import au lieu de require

// Configuration de l'API
const API_URL = '/api/teams';

/**
 * Récupère toutes les équipes
 * @returns {Promise} - Promise contenant les équipes
 */
export const getTeams = async () => {
  try {
    const response = await axios.get(API_URL);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des équipes:', error);
    throw error;
  }
};

/**
 * Récupère une équipe par son ID
 * @param {String} id - ID de l'équipe
 * @returns {Promise} - Promise contenant l'équipe
 */
export const getTeamById = async (id) => {
  try {
    const response = await axios.get(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la récupération de l'équipe ${id}:`, error);
    throw error;
  }
};

/**
 * Crée une nouvelle équipe
 * @param {Object} teamData - Données de l'équipe
 * @returns {Promise} - Promise contenant l'équipe créée
 */
export const createTeam = async (teamData) => {
  try {
    const response = await axios.post(API_URL, teamData);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la création de l\'équipe:', error);
    throw error;
  }
};

/**
 * Met à jour une équipe
 * @param {String} id - ID de l'équipe
 * @param {Object} teamData - Données de l'équipe
 * @returns {Promise} - Promise contenant l'équipe mise à jour
 */
export const updateTeam = async (id, teamData) => {
  try {
    const response = await axios.put(`${API_URL}/${id}`, teamData);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la mise à jour de l'équipe ${id}:`, error);
    throw error;
  }
};

/**
 * Supprime une équipe
 * @param {String} id - ID de l'équipe
 * @returns {Promise} - Promise contenant la réponse
 */
export const deleteTeam = async (id) => {
  try {
    const response = await axios.delete(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de la suppression de l'équipe ${id}:`, error);
    throw error;
  }
};

/**
 * Ajoute un membre à une équipe
 * @param {String} teamId - ID de l'équipe
 * @param {String} userId - ID de l'utilisateur
 * @returns {Promise} - Promise contenant la réponse
 */
export const addTeamMember = async (teamId, userId) => {
  try {
    const response = await axios.post(`${API_URL}/${teamId}/members`, { userId });
    return response.data;
  } catch (error) {
    console.error(`Erreur lors de l'ajout du membre à l'équipe ${teamId}:`, error);
    throw error;
  }
};