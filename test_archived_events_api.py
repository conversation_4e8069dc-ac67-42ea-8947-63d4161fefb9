#!/usr/bin/env python
"""
Script de test pour vérifier l'API des événements archivés
"""

import requests
import json

def test_archived_events_api():
    """Test de l'API des événements archivés"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test de l'API des événements archivés ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Test de l'API des événements actifs
    print("\n2. Test API événements actifs...")
    active_response = requests.get(f"{base_url}/personal-events/", headers=headers)
    
    print(f"📡 Statut: {active_response.status_code}")
    if active_response.status_code == 200:
        active_events = active_response.json()
        print(f"✅ Événements actifs: {len(active_events)}")
        
        archived_in_active = [e for e in active_events if e.get('status') == 'archived']
        if archived_in_active:
            print(f"❌ PROBLÈME: {len(archived_in_active)} événement(s) archivé(s) dans la liste active!")
            for event in archived_in_active:
                print(f"   - {event['title']} (statut: {event['status']})")
        else:
            print("✅ Aucun événement archivé dans la liste active (correct)")
            
        for event in active_events:
            print(f"   - {event['title']} (statut: {event['status']})")
    else:
        print(f"❌ Erreur: {active_response.json()}")
    
    # 3. Test de l'API des événements archivés
    print("\n3. Test API événements archivés...")
    archived_response = requests.get(f"{base_url}/personal-events/archived/list/", headers=headers)
    
    print(f"📡 Statut: {archived_response.status_code}")
    print(f"📡 URL testée: {base_url}/personal-events/archived/list/")
    
    if archived_response.status_code == 200:
        archived_data = archived_response.json()
        print(f"✅ Réponse reçue")
        print(f"📄 Structure de la réponse:")
        print(json.dumps(archived_data, indent=2, default=str))
        
        archived_events = archived_data.get('archived_events', [])
        total_archived = archived_data.get('total_archived', 0)
        message = archived_data.get('message', 'N/A')
        
        print(f"\n📊 Résumé:")
        print(f"   - Événements archivés: {len(archived_events)}")
        print(f"   - Total archivé: {total_archived}")
        print(f"   - Message: {message}")
        
        if len(archived_events) > 0:
            print(f"\n📋 Liste des événements archivés:")
            for event in archived_events:
                print(f"   - ID: {event['id']}")
                print(f"     Titre: {event['title']}")
                print(f"     Statut: {event['status']}")
                print(f"     Archivé le: {event.get('archived_at', 'N/A')}")
                print(f"     Peut désarchiver: {event.get('can_unarchive', 'N/A')}")
                print()
        else:
            print("❌ PROBLÈME: Aucun événement archivé trouvé dans l'API!")
            
    elif archived_response.status_code == 404:
        print("❌ ERREUR 404: Route non trouvée!")
        print("   Vérifiez que la route /personal-events/archived/list/ existe")
    else:
        print(f"❌ Erreur {archived_response.status_code}: {archived_response.text}")
    
    # 4. Vérifier directement un événement spécifique
    print("\n4. Vérification d'un événement spécifique...")
    event_id = "68323469d6b7837bf5056f07"  # ID de l'événement "rendez vous visa"
    
    detail_response = requests.get(f"{base_url}/personal-events/{event_id}/", headers=headers)
    
    print(f"📡 Statut: {detail_response.status_code}")
    if detail_response.status_code == 200:
        event_detail = detail_response.json()
        print(f"✅ Détails de l'événement:")
        print(f"   - Titre: {event_detail['title']}")
        print(f"   - Statut: {event_detail['status']}")
        print(f"   - Créé par: {event_detail['created_by']}")
        print(f"   - Mis à jour le: {event_detail['updated_at']}")
        
        if event_detail['status'] == 'archived':
            print("🎯 L'événement est bien archivé dans la base!")
        else:
            print(f"❌ L'événement n'est pas archivé (statut: {event_detail['status']})")
    else:
        print(f"❌ Erreur: {detail_response.json()}")
    
    # 5. Test des routes disponibles
    print("\n5. Test des routes disponibles...")
    test_routes = [
        "/personal-events/",
        "/personal-events/archived/list/",
    ]
    
    for route in test_routes:
        test_response = requests.get(f"{base_url}{route}", headers=headers)
        print(f"   {route} → {test_response.status_code}")
    
    print("\n" + "="*60)
    print("🎯 DIAGNOSTIC TERMINÉ")
    print("="*60)

if __name__ == "__main__":
    test_archived_events_api()
