from ..mongo_models import User, Team
from ..models.event_model import Event
from ..models.personal_event_model import PersonalEvent
from ..models.team_task_model import TeamTask
from ..models.personal_task_model import PersonalTask
from ..models.pomodoro_model import PomodoroSettings
from ..models.bi_model import BiMetric
from datetime import datetime, timezone, timedelta
import logging

logger = logging.getLogger(__name__)

def update_bi_metrics():
    """
    Met à jour les métriques BI
    Cette fonction est appelée périodiquement par une tâche Celery
    """
    logger.info("Mise à jour des métriques BI")

    # Mise à jour des métriques d'activité des utilisateurs
    update_user_activity_metrics()

    # Mise à jour des métriques des tâches d'équipe
    update_team_task_metrics()

    # Mise à jour des métriques des événements d'équipe
    update_team_event_metrics()

    # Mise à jour des métriques des tâches personnelles
    update_personal_task_metrics()

    # Mise à jour des métriques des événements personnels
    update_personal_event_metrics()

    logger.info("Mise à jour des métriques BI terminée")

def update_user_activity_metrics():
    """
    Met à jour les métriques d'activité des utilisateurs
    """
    # Forcer le rechargement des données depuis la base de données
    from mongoengine import connect
    connect('mongodb', alias='default')

    # Nombre total d'utilisateurs - Comptage direct
    total_users = User.objects.count()
    logger.info(f"Nombre total d'utilisateurs: {total_users}")

    # Nombre d'utilisateurs actifs (connectés au cours des 30 derniers jours)
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    active_users = User.objects(last_login__gte=thirty_days_ago).count()

    # Répartition des utilisateurs par rôle - Comptage direct
    users_by_role = {
        'super_admin': User.objects(role='super_admin').count(),
        'admin': User.objects(role='admin').count(),
        'employee': User.objects(role='employee').count(),
        'client': User.objects(role='client').count()
    }

    # Log détaillé pour le débogage
    logger.info(f"Répartition des utilisateurs par rôle - Super Admin: {users_by_role['super_admin']}, Admin: {users_by_role['admin']}, Employé: {users_by_role['employee']}, Client: {users_by_role['client']}")

    # Supprimer toutes les métriques existantes pour éviter les doublons
    try:
        BiMetric.objects(metric_type='user_activity').delete()
        logger.info("Anciennes métriques d'activité des utilisateurs supprimées")
    except Exception as e:
        logger.error(f"Erreur lors de la suppression des anciennes métriques: {str(e)}")

    # Créer une nouvelle métrique
    metric = BiMetric(metric_type='user_activity', user_id=None, team_id=None)
    metric.data = {
        'total_users': total_users,
        'active_users': active_users,
        'active_percentage': round((active_users / total_users) * 100, 2) if total_users > 0 else 0,
        'users_by_role': users_by_role,
        'updated_at': datetime.now(timezone.utc).isoformat()
    }
    metric.save()

    logger.info(f"Métriques d'activité des utilisateurs mises à jour: {active_users}/{total_users} utilisateurs actifs")

def update_team_task_metrics():
    """
    Met à jour les métriques des tâches d'équipe
    """
    # Pour chaque équipe
    for team in Team.objects:
        team_id = str(team.id)

        # Tâches d'équipe
        team_tasks = TeamTask.objects(team_id=team_id)
        total_tasks = team_tasks.count()

        # Répartition des tâches par statut
        tasks_by_status = {
            'a_faire': team_tasks.filter(status='a_faire').count(),
            'en_cours': team_tasks.filter(status='en_cours').count(),
            'en_revision': team_tasks.filter(status='en_revision').count(),
            'achevee': team_tasks.filter(status='achevee').count(),
            'archived': team_tasks.filter(status='archived').count()
        }

        # Créer ou mettre à jour la métrique
        try:
            metric = BiMetric.objects.get(metric_type='team_task_status', team_id=team_id)
        except BiMetric.DoesNotExist:
            metric = BiMetric(metric_type='team_task_status', team_id=team_id)

        metric.data = {
            'total': total_tasks,
            'by_status': tasks_by_status,
            'completion_rate': round((tasks_by_status['achevee'] / total_tasks) * 100, 2) if total_tasks > 0 else 0,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        metric.save()

        logger.info(f"Métriques des tâches d'équipe mises à jour pour l'équipe {team.name}: {total_tasks} tâches")

def update_team_event_metrics():
    """
    Met à jour les métriques des événements d'équipe
    """
    # Pour chaque équipe
    for team in Team.objects:
        team_id = str(team.id)

        # Événements d'équipe
        team_events = Event.objects(team_id=team_id)
        total_events = team_events.count()

        # Répartition des événements par statut
        events_by_status = {
            'pending': team_events.filter(status='pending').count(),
            'completed': team_events.filter(status='completed').count(),
            'archived': team_events.filter(status='archived').count()
        }

        # Créer ou mettre à jour la métrique
        try:
            metric = BiMetric.objects.get(metric_type='team_event_status', team_id=team_id)
        except BiMetric.DoesNotExist:
            metric = BiMetric(metric_type='team_event_status', team_id=team_id)

        metric.data = {
            'total': total_events,
            'by_status': events_by_status,
            'completion_rate': round((events_by_status['completed'] / total_events) * 100, 2) if total_events > 0 else 0,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        metric.save()

        logger.info(f"Métriques des événements d'équipe mises à jour pour l'équipe {team.name}: {total_events} événements")

def update_personal_task_metrics():
    """
    Met à jour les métriques des tâches personnelles
    """
    # Pour chaque utilisateur (employé ou client)
    for user in User.objects(role__in=['employee', 'client']):
        user_id = str(user.id)

        # Tâches personnelles
        personal_tasks = PersonalTask.objects(created_by=user_id)
        total_tasks = personal_tasks.count()

        # Répartition des tâches par statut
        tasks_by_status = {
            'a_faire': personal_tasks.filter(status='a_faire').count(),
            'en_cours': personal_tasks.filter(status='en_cours').count(),
            'en_revision': personal_tasks.filter(status='en_revision').count(),
            'achevee': personal_tasks.filter(status='achevee').count(),
            'archived': personal_tasks.filter(status='archived').count()
        }

        # Créer ou mettre à jour la métrique
        try:
            metric = BiMetric.objects.get(metric_type='personal_task_status', user_id=user_id)
        except BiMetric.DoesNotExist:
            metric = BiMetric(metric_type='personal_task_status', user_id=user_id)

        metric.data = {
            'total': total_tasks,
            'by_status': tasks_by_status,
            'completion_rate': round((tasks_by_status['achevee'] / total_tasks) * 100, 2) if total_tasks > 0 else 0,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        metric.save()

        logger.info(f"Métriques des tâches personnelles mises à jour pour l'utilisateur {user.name}: {total_tasks} tâches")

def update_personal_event_metrics():
    """
    Met à jour les métriques des événements personnels
    """
    # Pour chaque utilisateur (employé ou client)
    for user in User.objects(role__in=['employee', 'client']):
        user_id = str(user.id)

        # Événements personnels
        personal_events = PersonalEvent.objects(created_by=user_id)
        total_events = personal_events.count()

        # Répartition des événements par statut
        events_by_status = {
            'pending': personal_events.filter(status='pending').count(),
            'completed': personal_events.filter(status='completed').count(),
            'archived': personal_events.filter(status='archived').count()
        }

        # Créer ou mettre à jour la métrique
        try:
            metric = BiMetric.objects.get(metric_type='personal_event_status', user_id=user_id)
        except BiMetric.DoesNotExist:
            metric = BiMetric(metric_type='personal_event_status', user_id=user_id)

        metric.data = {
            'total': total_events,
            'by_status': events_by_status,
            'completion_rate': round((events_by_status['completed'] / total_events) * 100, 2) if total_events > 0 else 0,
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        metric.save()

        logger.info(f"Métriques des événements personnels mises à jour pour l'utilisateur {user.name}: {total_events} événements")
