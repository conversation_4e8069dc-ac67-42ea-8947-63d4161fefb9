#!/usr/bin/env python
"""
Script de test pour la mise à jour du profil utilisateur
"""

import requests
import json

def test_profile_update():
    """Test de l'API de mise à jour du profil"""
    base_url = "http://localhost:8000/api"
    
    # Données de connexion
    login_data = {
        "email": "<EMAIL>",
        "password": "Sarra123$"
    }
    
    print("=== Test de mise à jour du profil ===\n")
    
    # 1. Connexion
    print("1. Connexion...")
    login_response = requests.post(
        f"{base_url}/login/",
        json=login_data,
        headers={"Content-Type": "application/json"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Échec de la connexion: {login_response.json()}")
        return
    
    token = login_response.json()['access']
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    print("✅ Connexion réussie")
    
    # 2. Récupérer le profil actuel
    print("\n2. Récupération du profil actuel...")
    profile_response = requests.get(f"{base_url}/profile/", headers=headers)
    
    if profile_response.status_code == 200:
        current_profile = profile_response.json()
        print("✅ Profil actuel récupéré:")
        print(f"   - Nom: {current_profile.get('first_name')} {current_profile.get('last_name')}")
        print(f"   - Email: {current_profile.get('email')}")
        print(f"   - Téléphone: {current_profile.get('phone', 'Non défini')}")
        print(f"   - Rôle: {current_profile.get('role')}")
    else:
        print(f"❌ Erreur récupération profil: {profile_response.status_code}")
        return
    
    # 3. Test de mise à jour du profil
    print("\n3. Test de mise à jour du profil...")
    
    update_data = {
        "first_name": "Sarra",
        "last_name": "Test Update",
        "email": "<EMAIL>",
        "phone": "+33123456789"
    }
    
    print(f"📤 Données à envoyer:")
    print(json.dumps(update_data, indent=2))
    
    update_response = requests.patch(
        f"{base_url}/profile/update/",
        json=update_data,
        headers=headers
    )
    
    print(f"📡 Statut de la réponse: {update_response.status_code}")
    
    if update_response.status_code == 200:
        response_data = update_response.json()
        print("✅ Mise à jour réussie!")
        print(f"📄 Réponse:")
        print(json.dumps(response_data, indent=2, default=str))
        
        updated_user = response_data.get('user', {})
        print(f"\n📊 Profil mis à jour:")
        print(f"   - Nom: {updated_user.get('first_name')} {updated_user.get('last_name')}")
        print(f"   - Email: {updated_user.get('email')}")
        print(f"   - Téléphone: {updated_user.get('phone')}")
        print(f"   - Mis à jour le: {updated_user.get('updated_at')}")
        
    else:
        print(f"❌ Erreur {update_response.status_code}:")
        try:
            error_data = update_response.json()
            print(json.dumps(error_data, indent=2))
        except:
            print(update_response.text)
    
    # 4. Vérification après mise à jour
    print("\n4. Vérification après mise à jour...")
    verify_response = requests.get(f"{base_url}/profile/", headers=headers)
    
    if verify_response.status_code == 200:
        verified_profile = verify_response.json()
        print("✅ Profil vérifié:")
        print(f"   - Nom: {verified_profile.get('first_name')} {verified_profile.get('last_name')}")
        print(f"   - Email: {verified_profile.get('email')}")
        print(f"   - Téléphone: {verified_profile.get('phone')}")
        
        # Comparer avec les données envoyées
        if (verified_profile.get('first_name') == update_data['first_name'] and
            verified_profile.get('last_name') == update_data['last_name'] and
            verified_profile.get('phone') == update_data['phone']):
            print("🎯 ✅ Toutes les données ont été correctement mises à jour!")
        else:
            print("❌ Incohérence dans les données mises à jour")
    else:
        print(f"❌ Erreur lors de la vérification: {verify_response.status_code}")
    
    # 5. Test avec données invalides
    print("\n5. Test avec données invalides...")
    
    invalid_data = {
        "email": "email-invalide",  # Email invalide
        "first_name": "",           # Prénom vide
    }
    
    invalid_response = requests.patch(
        f"{base_url}/profile/update/",
        json=invalid_data,
        headers=headers
    )
    
    print(f"📡 Statut avec données invalides: {invalid_response.status_code}")
    if invalid_response.status_code != 200:
        print("✅ Validation des données fonctionne correctement")
        try:
            error_data = invalid_response.json()
            print(f"📄 Erreurs de validation:")
            print(json.dumps(error_data, indent=2))
        except:
            print(invalid_response.text)
    else:
        print("❌ La validation des données ne fonctionne pas correctement")
    
    print("\n" + "="*60)
    print("🎯 TEST DE MISE À JOUR DU PROFIL TERMINÉ")
    print("="*60)

if __name__ == "__main__":
    test_profile_update()
