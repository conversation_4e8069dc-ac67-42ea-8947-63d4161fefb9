#!/usr/bin/env python
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from core.mongo_models import User
from datetime import datetime

def list_super_admins():
    """Lister tous les super admins"""
    print("🔍 Recherche des super admins...")
    super_admins = User.objects.filter(role="super_admin")
    
    if not super_admins:
        print("❌ Aucun super admin trouvé")
        return []
    
    print(f"✅ {len(super_admins)} super admin(s) trouvé(s):\n")
    
    for i, admin in enumerate(super_admins, 1):
        print(f"{i}. ID: {admin.id}")
        print(f"   Email: {admin.email}")
        print(f"   Nom: {admin.name}")
        print(f"   Créé le: {admin.created_at}")
        print(f"   Dernière connexion: {admin.last_login or 'Jamais'}")
        print("-" * 50)
    
    return list(super_admins)

def delete_super_admin(admin_to_delete):
    """Supprimer un super admin spécifique (contourne la protection)"""
    try:
        print(f"🗑️ Suppression du super admin: {admin_to_delete.email}")
        
        # Suppression directe (contourne la protection de user_views.py)
        admin_to_delete.delete()
        
        print(f"✅ Super admin {admin_to_delete.email} supprimé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la suppression: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("🛠️  GESTIONNAIRE DES SUPER ADMINS")
    print("=" * 60)
    
    # Lister les super admins
    super_admins = list_super_admins()
    
    if len(super_admins) <= 2:
        print(f"✅ Vous avez {len(super_admins)} super admin(s), c'est parfait !")
        return
    
    print(f"⚠️ Vous avez {len(super_admins)} super admins, il faut en supprimer {len(super_admins) - 2}")
    print("\n🎯 Objectif: Garder seulement 2 super admins:")
    print("   1. <EMAIL> (principal)")
    print("   2. Un autre de votre choix")
    
    # Identifier le super admin principal
    main_admin = None
    other_admins = []
    
    for admin in super_admins:
        if admin.email == "<EMAIL>":
            main_admin = admin
        else:
            other_admins.append(admin)
    
    if not main_admin:
        print("❌ Le super admin principal (<EMAIL>) n'a pas été trouvé !")
        return
    
    print(f"\n✅ Super admin principal identifié: {main_admin.email}")
    
    if len(other_admins) == 0:
        print("✅ Aucun autre super admin à gérer")
        return
    elif len(other_admins) == 1:
        print(f"✅ Un seul autre super admin: {other_admins[0].email}")
        print("✅ Configuration parfaite, rien à supprimer !")
        return
    
    # Il y a plus d'un autre super admin, il faut en supprimer
    print(f"\n📋 Autres super admins ({len(other_admins)}):")
    for i, admin in enumerate(other_admins, 1):
        print(f"{i}. {admin.email} (créé le {admin.created_at})")
    
    print(f"\n🎯 Il faut supprimer {len(other_admins) - 1} super admin(s)")
    
    # Demander confirmation
    print("\n⚠️ ATTENTION: Cette action est irréversible !")
    confirm = input("Voulez-vous continuer? (tapez 'OUI' pour confirmer): ")
    
    if confirm != "OUI":
        print("❌ Opération annulée")
        return
    
    # Stratégie: garder le plus ancien (après le principal) et supprimer les autres
    other_admins.sort(key=lambda x: x.created_at)
    admin_to_keep = other_admins[0]
    admins_to_delete = other_admins[1:]
    
    print(f"\n✅ Super admin à conserver: {admin_to_keep.email}")
    print(f"🗑️ Super admin(s) à supprimer: {[admin.email for admin in admins_to_delete]}")
    
    # Supprimer les super admins en surplus
    for admin in admins_to_delete:
        success = delete_super_admin(admin)
        if not success:
            print(f"❌ Échec de la suppression de {admin.email}")
            return
    
    print("\n" + "=" * 60)
    print("✅ OPÉRATION TERMINÉE")
    print("=" * 60)
    
    # Vérification finale
    final_super_admins = list_super_admins()
    print(f"\n🎯 Résultat final: {len(final_super_admins)} super admin(s)")
    
    if len(final_super_admins) == 2:
        print("✅ Configuration parfaite atteinte !")
    else:
        print(f"⚠️ Nombre inattendu de super admins: {len(final_super_admins)}")

if __name__ == "__main__":
    main()
