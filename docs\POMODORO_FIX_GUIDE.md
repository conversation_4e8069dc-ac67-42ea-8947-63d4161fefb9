# Guide de correction des problèmes Pomodoro

## Problèmes identifiés et corrigés

### 1. Problèmes Backend ✅ CORRIGÉ

**Problème** : L'endpoint `/api/pomodoro/control/complete/` retournait une erreur 400 quand aucune session n'était active.

**Solution** : Ajout de validations dans `core/views/pomodoro_views.py` :
- Vérification de l'existence d'une session avant chaque action
- Messages d'erreur explicites pour chaque cas
- Gestion correcte des états de session

### 2. Problèmes Frontend React

**Problème** : "Cannot update a component while rendering a different component"

**Causes principales** :
1. Appels de `setState` pendant le rendu
2. Effets infinis dus à des dépendances mal gérées
3. Timers non nettoyés

**Solutions** :

#### A. Utiliser `useCallback` pour les fonctions
```jsx
const controlSession = useCallback(async (action) => {
  // Code de contrôle
}, [apiCall]);
```

#### B. G<PERSON>rer correctement les timers avec `useRef`
```jsx
const intervalRef = useRef(null);

useEffect(() => {
  if (currentSession.active && currentSession.status === 'active') {
    intervalRef.current = setInterval(() => {
      setCurrentSession(prev => ({
        ...prev,
        remaining_time: prev.remaining_time - 1
      }));
    }, 1000);
  }

  return () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };
}, [currentSession.active, currentSession.status]);
```

#### C. Éviter les setState pendant le rendu
```jsx
// ❌ Mauvais
if (someCondition) {
  setSomeState(newValue); // Pendant le rendu
}

// ✅ Bon
useEffect(() => {
  if (someCondition) {
    setSomeState(newValue);
  }
}, [someCondition]);
```

### 3. Problème Audio

**Problème** : "The element has no supported sources"

**Solutions** :

#### A. Vérifier l'existence des fichiers audio
```jsx
const playNotificationSound = useCallback(() => {
  if (audioRef.current) {
    audioRef.current.play().catch(error => {
      console.log('Impossible de jouer le son:', error);
    });
  }
}, []);
```

#### B. Alternative avec Web Audio API
```jsx
const playNotificationSound = (soundType = 'complete') => {
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.type = 'sine';
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  } catch (error) {
    console.log('Impossible de jouer le son:', error);
  }
};
```

## Fichiers corrigés

### 1. Backend
- `core/views/pomodoro_views.py` : Validation des actions et gestion d'erreurs améliorée

### 2. Frontend (exemples fournis)
- `docs/ClientPomodoro.jsx.fixed` : Composant React corrigé
- `docs/pomodoroService.js` : Service API avec gestion d'erreurs

## Instructions d'implémentation

### 1. Remplacer le composant ClientPomodoro existant
```bash
# Copier le fichier corrigé
cp docs/ClientPomodoro.jsx.fixed src/pages/ClientPomodoro.jsx
```

### 2. Ajouter le service Pomodoro
```bash
# Copier le service
cp docs/pomodoroService.js src/services/pomodoroService.js
```

### 3. Installer les dépendances nécessaires
```bash
npm install framer-motion  # Pour les animations
```

### 4. Ajouter les fichiers audio (optionnel)
```bash
# Placer les fichiers dans public/
public/notification.mp3
public/notification.ogg
```

## Tests de validation

### Backend
```bash
python test_pomodoro_fix.py
```

### Frontend
1. Démarrer une session Pomodoro
2. Mettre en pause et reprendre
3. Réinitialiser la session
4. Modifier les paramètres
5. Vérifier que les erreurs sont gérées correctement

## Bonnes pratiques appliquées

### 1. Gestion d'état React
- Utilisation de `useCallback` pour éviter les re-renders
- Nettoyage des timers avec `useRef`
- Gestion des effets avec des dépendances correctes

### 2. Gestion d'erreurs
- Try-catch pour tous les appels API
- Messages d'erreur explicites
- Validation côté backend et frontend

### 3. Performance
- Éviter les re-renders inutiles
- Nettoyage des ressources (timers, audio)
- Optimisation des appels API

### 4. UX/UI
- Feedback visuel pour les actions
- Gestion des états de chargement
- Messages d'erreur utilisateur-friendly

## Résultats attendus

Après application des corrections :
- ✅ Aucune erreur 400 sur les endpoints Pomodoro
- ✅ Aucun warning React dans la console
- ✅ Timer fonctionnel avec pause/reprise
- ✅ Gestion d'erreurs robuste
- ✅ Interface utilisateur réactive
